import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
  TemplateRef,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, map, skipWhile, take, takeUntil } from 'rxjs';

import { HEADER_LIST, MODULE_INFO } from 'src/app/app.constants';
import { LogoutType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  changeCalendar,
  getSystemTimeOffset,
  getTimeZoneDate,
  isEmptyObject,
  isStringSubsetInArray,
} from 'src/app/core/utils/common.util';
import { AlertComponent } from 'src/app/features/subscription/alert/alert.component';
import {
  ClockIn,
  ClockOut,
  FetchAttendanceListById,
  FetchAttendanceSetting,
  FetchNotification
} from 'src/app/reducers/attendance/attendance.actions';
import {
  getAttendanceListById,
  getAttendanceSettings,
  getNotification,
} from 'src/app/reducers/attendance/attendance.reducer';
import { getGlobalAnonymousIsLoading } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getIsLeadCustomStatusEnabled } from 'src/app/reducers/lead/lead.reducer';
import { Logout } from 'src/app/reducers/login/login.actions';
import {
  getPermissions,
  getViewPermissions,
} from 'src/app/reducers/permissions/permissions.reducers';
import { FetchReportExportTracker } from 'src/app/reducers/reports/reports.actions';
import { FetchCustomStatus } from 'src/app/reducers/status/status.actions';
import {
  FetchGeoFencingList,
  FetchOnlyReportees,
  FetchUserBasicDetailsById,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import { getGeoFencingList, getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { DeviceInfoService } from 'src/app/services/shared/device-info.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ExportReportsTrackerComponent } from 'src/app/shared/components/export-reports-tracker/export-reports-tracker.component';
import { InformationComponent } from 'src/app/shared/components/information/information.component';
import { WebcamComponent } from 'src/app/shared/components/web-cam/web-cam.component';
import { environment as env } from 'src/environments/environment';

@Component({
  selector: 'header',
  templateUrl: './header.component.html',
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Output() checkEvent = new EventEmitter<string>();
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  header: any = HEADER_LIST;
  notificationCounter: number = 0;
  userData: any;
  s3BucketUrl: string = env.s3ImageBucketURL;
  titles: string = '';
  isOpenUserDetails: boolean = false;
  canView: Array<String> = [];
  isClockIn: boolean;
  isLocationDisabled: boolean;
  hours: number = 0;
  minutes: number = 0;
  seconds: number = 0;
  userId: any;
  params: any;
  private timer: any;
  private startTime: Date;
  notificationModalRef: BsModalRef<unknown>;
  canExportReportees: boolean;
  canExportAllUsers: boolean;
  currentPath: string;
  isUserListFetchedOnce: boolean = false;
  isMandatorySelfieClockin: boolean = false;
  isMandatorySelfieClockout: boolean = false;
  isBannerEnabled: boolean = false;
  isShiftOver: boolean = false;
  balanceTime: number;
  stream: MediaStream;
  isClockInMode: boolean;
  clockInImg: any;
  clockOutImg: any;
  moduleInfo = MODULE_INFO;
  moduleContent: { [key: string]: string };
  currentDate: Date = new Date();
  intervalId: any;
  getTimeZoneDate = getTimeZoneDate;
  geoFencingData: any = [];
  isInGeoFence: boolean = false;
  isCheckingGeoFence: boolean = false;
  geoFenceCheckInterval: any;
  showGeoFenceBanner: boolean = true;
  geoFenceBannerTimeout: any;
  isGeoFenceEnabled: boolean = false;
  isAccuracyLow: boolean = false;
  previousAccuracy: number = 0;
  isAccuracyChecked: boolean = false;
  showAccuracyBanner: boolean = false;
  accuracyBannerTimeout: any;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private router: Router,
    private elRef: ElementRef,
    private notificationService: NotificationsService,
    private deviceInfoService: DeviceInfoService,
  ) {
    this.userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    if (this.userId) {
      this._store.dispatch(new FetchUserBasicDetailsById(this.userId));
    }
    this._store
      .select(getGeoFencingList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.geoFencingData = data;
        this.checkGeoFenceLocation();
        this.triggerAccuracyCheckIfReady();
      });

    this._store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const wasGeoFenceEnabled = this.isGeoFenceEnabled;
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
        if (wasGeoFenceEnabled && !this.isGeoFenceEnabled) {
          this.resetAccuracyState();
        }
        this.triggerAccuracyCheckIfReady();
      });

    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          const wasGeoFenceActive = this.userData?.isGeoFenceActive;
          this.userData = data;
          if (wasGeoFenceActive && !this.userData?.isGeoFenceActive) {
            this.resetAccuracyState();
          }

          this.currentDate = changeCalendar(
            this.userData?.timeZoneInfo?.baseUTcOffset
          );
          clearInterval(this.intervalId);
          this.intervalId = setInterval(() => {
            const presentTime = this.currentDate;
          }, 1000);
          if (this.userData?.subscriptionDetails?.isAdmin && this.userData?.subscriptionDetails?.validDays <= 30) {
            const alertShown = sessionStorage.getItem('alertModalShown');
            if (!alertShown) {
              setTimeout(() => {
                this.modalRef = this.modalService.show(AlertComponent, {
                  class: 'modal-1000 modal-dialog-centered ip-modal-unset', ignoreBackdropClick: true
                });
                sessionStorage.setItem('alertModalShown', 'true');
              }, 1000);
            }
          }
          this.getAttendanceList();
          this.triggerAccuracyCheckIfReady();
        }
      });

    // this._store.dispatch(new FetchGlobalSettingsAnonymous());

    this._store
      .select(getViewPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canView: any) => {
        this.canView = canView;
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Reports.ExportAllUsers')) {
          this.canExportAllUsers = true;
        }
        if (permissions?.includes('Permissions.Reports.ExportReportees')) {
          this.canExportReportees = true;
        }
        if (permissions?.includes('Permissions.Users.SetGeoFence')) {
          if (this.userId) {
            this._store.dispatch(new FetchGeoFencingList(this.userId));
          }
        }
      });
  }

  async ngOnInit() {
    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;

    await this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(
        skipWhile((isLoading: boolean) => {
          return isLoading;
        }),
        take(1)
      )
      .toPromise();
    const isCustomStatusEnabled: boolean = await this._store
      .select(getIsLeadCustomStatusEnabled)
      .pipe(
        map((data: any) => data),
        take(1)
      )
      .toPromise();
    if (isCustomStatusEnabled) this._store?.dispatch(new FetchCustomStatus());
    if (this.userId && !this.isUserListFetchedOnce) {
      if (!isStringSubsetInArray(location?.href, ['/leads/manage-leads']))
        this._store.dispatch(new FetchUsersListForReassignment());
      this._store.dispatch(new FetchOnlyReportees());
      this._store.dispatch(new FetchNotification());
      this._store.dispatch(new FetchAttendanceSetting());

      this.isUserListFetchedOnce = true;
    }
    this.headerTitle.pageTitle.subscribe((newTitle) => {
      this.titles = newTitle;
      this.moduleContent = MODULE_INFO[this.titles];
    });

    this._store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        (this.isMandatorySelfieClockin = data?.isSelfieMandatoryForClockIn),
          (this.isMandatorySelfieClockout = data?.isSelfieMandatoryForClockOut);
      });

    const storedClockInTime = localStorage.getItem('clockInTime');
    if (storedClockInTime) {
      this.startTime = new Date(storedClockInTime);
      this.startTimer();
    }

    this._store
      .select(getNotification)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.scheduleNotifications(data);
      });

    this.geoFenceCheckInterval = setInterval(() => {
      if (this.geoFencingData) {
        this.checkGeoFenceLocation();
      }
    }, 120000);

    setTimeout(() => {
      if (this.geoFencingData && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive && !this.isAccuracyChecked) {
        this.checkGeoFenceLocation();
        this.isAccuracyChecked = true;
      }
    }, 2000);
  }

  scheduleNotifications(data: any) {
    if (data?.isNotificationEnabled && data?.shiftEndTime) {
      const shiftEndTime = this.parseTime(
        data?.shiftEndTime,
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      const timestampPresentTime = this.currentDate.getTime();

      data?.minutesBefore?.forEach((minutes: number) => {
        const notificationTime = shiftEndTime - minutes * 60 * 1000;
        const delay = notificationTime - timestampPresentTime;

        if (delay > 0) {
          setTimeout(() => {
            this.isBannerEnabled = true;
            this.balanceTime = minutes;
            setTimeout(() => {
              this.isBannerEnabled = false;
            }, 10000);
          }, delay);
        }
      });

      const endDelay = shiftEndTime - timestampPresentTime;
      if (endDelay > 0) {
        setTimeout(() => {
          this.isShiftOver = true;
          setTimeout(() => {
            this.isShiftOver = false;
          }, 10000);
        }, endDelay);
      }
    }
  }

  parseTime(timeString: string, offset: string): number {
    const [hours, minutes, seconds] = timeString?.split(':')?.map(Number);

    const now = new Date(this.currentDate);
    const endTime = new Date(
      now.getFullYear(),
      now.getMonth(),
      now.getDate(),
      hours,
      minutes,
      seconds
    );
    const baseoffset = offset ? offset : getSystemTimeOffset();
    const offsetSign = baseoffset[0] === '-' ? -1 : 1;
    const [offsetHours, offsetMinutes] = baseoffset
      .slice(1)
      .split(':')
      .map(Number);

    endTime.setMinutes(
      endTime.getMinutes() + offsetSign * (offsetHours * 60 + offsetMinutes)
    );
    return endTime?.getTime();
  }

  startTimer() {
    this.timer = setInterval(() => {
      this.updateTime();
    }, 1000);
  }

  updateTime() {
    const currentTime = new Date(this.currentDate);
    const timeDifference = Math.floor(
      (currentTime.getTime() - this.startTime.getTime()) / 1000
    );

    this.hours = Math.floor(timeDifference / 3600);
    this.minutes = Math.floor((timeDifference % 3600) / 60);
    this.seconds = timeDifference % 60;
  }

  @HostListener('document:click', ['$event'])
  offClickHandler(event: MouseEvent): void {
    if (!this.elRef.nativeElement.contains(event.target)) {
      this.isOpenUserDetails = false;
    }
  }

  getAddressFromLatLng(latitude: number, longitude: number) {
    return new Promise((resolve, reject) => {
      const geocoder = new google.maps.Geocoder();
      const location = new google.maps.LatLng(latitude, longitude);

      geocoder.geocode({ location }, (results, status) => {
        if (status === 'OK' && results[0]) {
          resolve(results[0].formatted_address);
        } else {
          resolve('');
        }
      });
    });
  }

  clockIn() {
    if (this.geoFencingData && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive && !this.isInGeoFence) {
      this.checkGeoFenceLocation().then(isWithinFence => {
        if (!isWithinFence) {
          return;
        } else if (this.isAccuracyLow) {
          return;
        } else {
          this.proceedWithClockIn();
        }
      });
    } else if (this.isGeoFenceEnabled && this.userData?.isGeoFenceActive && this.isAccuracyLow) {
      return;
    } else {
      this.proceedWithClockIn();
    }
  }

  proceedWithClockIn() {
    this.isClockInMode = true;
    navigator?.mediaDevices
      ?.getUserMedia({
        video: true,
      })
      .then((res: any) => {
        this.stream = res;
        this.modalRef = this.modalService?.show(WebcamComponent, {
          class: 'modal-640 modal-dialog-centered h-100 tb-modal-unset',
          initialState: {
            isClockInMode: this.isClockInMode,
            isMandatorySelfieClockin: this.isMandatorySelfieClockin,
          },
        });
        this.modalRef?.content?.photoCaptured?.subscribe((res: any) => {
          this.clockInImg = res?.imageAsDataUrl;
          if (
            (this.isMandatorySelfieClockin && res?.imageAsDataUrl) ||
            (!this.isMandatorySelfieClockin && res?.imageAsDataUrl)
          ) {
            this.processClockIn(res?.imageAsDataUrl);
          }
          this.stopStream();
          if (!this.isMandatorySelfieClockin && !res?.imageAsDataUrl)
            this.clockInImg = null;
        });

        this.modalRef?.onHidden?.subscribe(() => {
          if (!this.isMandatorySelfieClockin && !this.clockInImg) {
            this.processClockIn(null);
          }
          this.stopStream();
        });
      })
      .catch((err: any) => {
        this.handleCameraError(err);
      });
  }

  async processClockIn(imageDataUrl: string | null) {
    if (navigator.geolocation) {
      const geolocationOptions = this.deviceInfoService.getGeolocationOptions();
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const accuracy = position.coords.accuracy;
          if (accuracy > 200 && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive) {
            return;
          }

          let clockPayload = {
            latitude: String(position.coords.latitude),
            longitude: String(position.coords.longitude),
            locationName: await this.getAddressFromLatLng(
              position.coords.latitude,
              position.coords.longitude
            ),
            clockInImageUrl: imageDataUrl,
          };
          if (this.isMandatorySelfieClockin && imageDataUrl) {
            clockPayload.clockInImageUrl = imageDataUrl;
          }
          this._store.dispatch(new ClockIn(clockPayload, 'header'));
          this.startTime = new Date();
          localStorage.setItem('clockInTime', this.startTime.toISOString());
          this.startTimer();
        },
        () => {
          this.openDisabledLocMsg();
        },
        geolocationOptions
      );
    } else {
      this.openDisabledLocMsg();
    }
  }

  clockOut() {
    if (this.geoFencingData && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive && !this.isInGeoFence) {
      this.checkGeoFenceLocation().then(isWithinFence => {
        if (!isWithinFence) {
          return;
        } else if (this.isAccuracyLow) {
          return;
        } else {
          this.proceedWithClockOut();
        }
      });
    } else if (this.isGeoFenceEnabled && this.userData?.isGeoFenceActive && this.isAccuracyLow) {
      return;
    } else {
      this.proceedWithClockOut();
    }
  }

  proceedWithClockOut() {
    this.isClockInMode = false;
    navigator?.mediaDevices
      ?.getUserMedia({
        video: true,
      })
      .then((res: any) => {
        this.stream = res;
        this.modalRef = this.modalService?.show(WebcamComponent, {
          class: 'modal-640 modal-dialog-centered h-100 tb-modal-unset',
          initialState: {
            isClockInMode: this.isClockInMode,
            isMandatorySelfieClockout: this.isMandatorySelfieClockout,
          },
        });
        this.modalRef?.content?.photoCaptured?.subscribe((res: any) => {
          this.clockOutImg = res?.imageAsDataUrl;
          if (
            (this.isMandatorySelfieClockout && res?.imageAsDataUrl) ||
            (!this.isMandatorySelfieClockout && res?.imageAsDataUrl)
          ) {
            this.processClockOut(res?.imageAsDataUrl);
          }
          this.stopStream();
          if (!this.isMandatorySelfieClockout && !res?.imageAsDataUrl)
            this.clockOutImg = null;
        });

        this.modalRef?.onHidden?.subscribe(() => {
          if (!this.isMandatorySelfieClockout && !this.clockOutImg) {
            this.processClockOut(null);
          }
          this.stopStream();
        });
      })
      .catch((err: any) => {
        this.handleCameraError(err);
      });
  }

  async processClockOut(imageDataUrl: string | null) {
    if (navigator.geolocation) {
      const geolocationOptions = this.deviceInfoService.getGeolocationOptions();
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const accuracy = position.coords.accuracy;
          if (accuracy > 200 && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive) {
            return;
          }

          let clockPayload = {
            latitude: String(position.coords.latitude),
            longitude: String(position.coords.longitude),
            locationName: await this.getAddressFromLatLng(
              position.coords.latitude,
              position.coords.longitude
            ),
            clockOutImageUrl: imageDataUrl,
          };
          if (this.isMandatorySelfieClockout && imageDataUrl) {
            clockPayload.clockOutImageUrl = imageDataUrl;
          }
          this._store.dispatch(new ClockOut(clockPayload, 'header'));
        },
        () => {
          this.openDisabledLocMsg();
        },
        geolocationOptions
      );
    } else {
      this.openDisabledLocMsg();
    }
  }

  handleCameraError(err: any) {
    if (err?.message === 'Permission denied') {
      this.notificationService.warn(
        'Allow camera permission while clocking in/out'
      );
    } else {
      this.notificationService.warn(
        'Camera cannot be accessed, please login via a device with a camera to clock in/out'
      );
    }
  }

  stopStream() {
    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
    }
  }

  openDisabledLocMsg() {
    this.isLocationDisabled = true;
    setTimeout(() => {
      this.isLocationDisabled = false;
    }, 5000);
  }

  getAttendanceList() {
    if (this.userId && this.isOpenUserDetails) {
      this._store.dispatch(
        new FetchAttendanceListById(this.userId, this.userData?.timeZoneInfo)
      );
    }
    this._store
      .select(getAttendanceListById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        let attendanceList = data;
        this.isClockIn = !attendanceList.length
          ? false
          : !attendanceList?.[0]?.isClosed;
        const lastClockInDateTime = new Date(attendanceList?.[0]?.clockInTime);
        const presentDateTime = new Date();
        const timeDifference =
          presentDateTime.getTime() - lastClockInDateTime.getTime();
        const secondsDifference = Math.floor(timeDifference / 1000);
        this.hours = Math.floor(secondsDifference / 3600);
        this.minutes = Math.floor((secondsDifference % 3600) / 60);
        this.seconds = secondsDifference % 60;
      });
  }

  getTimeZoneTooltip(): string {
    const timeZoneInfo = this.userData?.timeZoneInfo;
    return timeZoneInfo
      ? `Zone: ${timeZoneInfo.timeZoneDisplay}\n` +
      `Zone Id: ${timeZoneInfo.timeZoneId}\n` +
      `Zone Name: ${timeZoneInfo.timeZoneName}`
      : '';
  }

  openNotificationModal(notification: TemplateRef<any>) {
    let initialState: any = {
      class: 'right-modal modal-300',
    };
    this.notificationModalRef = this.modalService.show(
      notification,
      initialState
    );
  }

  openReportsTracker() {
    let payload: any = {
      trackerPermission: this.canExportAllUsers ? 0 : 1,
    };
    this._store.dispatch(new FetchReportExportTracker(payload, 1, 10));
    this.modalService.show(ExportReportsTrackerComponent, {
      class: 'modal-1000 modal-dialog-centered h-100 tb-modal-unset',
    });
  }

  openInfoModal(titles: keyof typeof MODULE_INFO) {
    const moduleContent = MODULE_INFO[titles];
    const keyValuePairs = moduleContent
      ? Object.entries(moduleContent).map(([key, value]) => ({
        key,
        value,
      }))
      : [];
    if (this.modalRef) {
      this.modalRef.hide();
    }
    let initialState: any = {
      data: keyValuePairs,
    };
    this.modalRef = this.modalService.show(
      InformationComponent,
      Object.assign(
        {},
        {
          class: 'right-modal modal-400 ip-modal-unset',
          initialState,
        }
      )
    );
  }

  signOut() {
    let payload = {
      idToken: localStorage.getItem('idToken'),
      refreshToken: localStorage.getItem('refreshToken'),
      logoutType: LogoutType.Manual,
      userId: JSON.parse(localStorage.getItem('userDetails'))?.sub,
    }
    this._store.dispatch(new Logout(payload));
  }

  alertSubscription() {
    this.modalRef = this.modalService.show(AlertComponent, {
      class: 'modal-1000 modal-dialog-centered ip-modal-unset', ignoreBackdropClick: true
    });
  }

  // signOut() {
  //   let columnData = localStorage.getItem('manage-lead-columns');
  //   let columnState = localStorage.getItem('myColumnState');
  //   localStorage.clear();
  //   localStorage.setItem('myColumnState', columnState);
  //   if (columnData) localStorage.setItem('manage-lead-columns', columnData);
  //   location.href = '/login';
  // }

  // notification aws amplify
  resetCounter() {
    this.notificationCounter = 0;
  }

  triggerAccuracyCheckIfReady() {
    if (this.geoFencingData && this.userData && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive && !this.isAccuracyChecked) {
      setTimeout(() => {
        this.checkGeoFenceLocation();
        this.isAccuracyChecked = true;
      }, 1000);
    }
  }

  resetAccuracyState() {
    this.isAccuracyLow = false;
    this.showAccuracyBanner = false;
    this.previousAccuracy = 0;
    if (this.accuracyBannerTimeout) {
      clearTimeout(this.accuracyBannerTimeout);
      this.accuracyBannerTimeout = null;
    }
  }

  checkGeoFenceLocation(): Promise<boolean> {
    if (!this.geoFencingData) {
      return Promise.resolve(true);
    }

    if (this.isCheckingGeoFence) {
      return Promise.resolve(this.isInGeoFence);
    }

    this.isCheckingGeoFence = true;

    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        this.isCheckingGeoFence = false;
        if (this.isGeoFenceEnabled) {
          this.openDisabledLocMsg();
        }
        this.showGeoFenceBanner = false;
        resolve(false);
        return;
      }

      const geolocationOptions = this.deviceInfoService.getGeolocationOptions();

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          const accuracy = position.coords.accuracy;
          this.previousAccuracy = accuracy;
          const wasAccuracyPoor = this.isAccuracyLow;
          this.isAccuracyLow = accuracy > 200 && this.isGeoFenceEnabled && this.userData?.isGeoFenceActive;
          if (this.isAccuracyLow) {
            this.showAccuracyBanner = true;
            if (this.accuracyBannerTimeout) {
              clearTimeout(this.accuracyBannerTimeout);
            }
            this.accuracyBannerTimeout = setTimeout(() => {
              this.showAccuracyBanner = false;
            }, 5000);
          } else if (wasAccuracyPoor && !this.isAccuracyLow) {
            this.showAccuracyBanner = false;
            if (this.accuracyBannerTimeout) {
              clearTimeout(this.accuracyBannerTimeout);
              this.accuracyBannerTimeout = null;
            }
          }

          let isWithinFence = true;
          let hasValidLocations = false;

          if (this.geoFencingData.properties && this.geoFencingData.properties.length > 0) {
            for (const property of this.geoFencingData.properties) {
              if (property.latitude && property.longitude) {
                hasValidLocations = true;
                const distance = this.calculateDistance(
                  userLat,
                  userLng,
                  property.latitude,
                  property.longitude
                );
                const radiusInMeters = this.geoFencingData.radiusUnit === 1
                  ? this.geoFencingData.geoFenceRadius
                  : this.geoFencingData.geoFenceRadius * 1000;
                if (distance > radiusInMeters) {
                  isWithinFence = false;
                  break;
                }
              }
            }
          }
          if (isWithinFence && this.geoFencingData.projects && this.geoFencingData.projects.length > 0) {
            for (const project of this.geoFencingData.projects) {
              if (project.latitude && project.longitude) {
                hasValidLocations = true;
                const distance = this.calculateDistance(
                  userLat,
                  userLng,
                  project.latitude,
                  project.longitude
                );
                const radiusInMeters = this.geoFencingData.radiusUnit === 1
                  ? this.geoFencingData.geoFenceRadius
                  : this.geoFencingData.geoFenceRadius * 1000;
                if (distance > radiusInMeters) {
                  isWithinFence = false;
                  break;
                }
              }
            }
          }
          if (!hasValidLocations) {
            isWithinFence = true;
          }

          if (this.isInGeoFence !== isWithinFence) {
            if (!isWithinFence) {
              this.showGeoFenceBanner = true;
              if (this.geoFenceBannerTimeout) {
                clearTimeout(this.geoFenceBannerTimeout);
              }
              this.geoFenceBannerTimeout = setTimeout(() => {
                this.showGeoFenceBanner = false;
              }, 5000);
            }
          }
          if (!this.isInGeoFence && !isWithinFence) {
            this.showGeoFenceBanner = true;
            if (this.geoFenceBannerTimeout) {
              clearTimeout(this.geoFenceBannerTimeout);
            }
            this.geoFenceBannerTimeout = setTimeout(() => {
              this.showGeoFenceBanner = false;
            }, 5000);
          }

          this.isInGeoFence = isWithinFence;
          this.isCheckingGeoFence = false;
          resolve(isWithinFence);
        },
        (error) => {
          console.error('Error getting location:', error);
          this.isCheckingGeoFence = false;
          if (this.isGeoFenceEnabled) {
            this.openDisabledLocMsg();
          }

          this.showGeoFenceBanner = false;
          this.showAccuracyBanner = false;
          if (this.accuracyBannerTimeout) {
            clearTimeout(this.accuracyBannerTimeout);
            this.accuracyBannerTimeout = null;
          }
          resolve(false);
        },
        geolocationOptions
      );
    });
  }

  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371000;
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }

  deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    clearInterval(this.timer);
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    if (this.geoFenceCheckInterval) {
      clearInterval(this.geoFenceCheckInterval);
    }
    if (this.geoFenceBannerTimeout) {
      clearTimeout(this.geoFenceBannerTimeout);
    }
    if (this.accuracyBannerTimeout) {
      clearTimeout(this.accuracyBannerTimeout);
    }
  }
}
