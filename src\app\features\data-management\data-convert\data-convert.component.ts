import {
  Component,
  EventE<PERSON>ter,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, take, takeUntil } from 'rxjs';

import { AnimationOptions } from 'ngx-lottie';
import { VALIDATION_CLEAR, VALIDATION_SET } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { ConversionStatus } from 'src/app/core/interfaces/data-management.interface';
import {
  changeCalendar,
  getAssignedToDetails,
  getTimeZoneDate,
  onPickerOpened,
  setTimeZoneDateWithTime,
  toggleValidation,
  validateAllFormFields,
  validateScheduleTime,
} from 'src/app/core/utils/common.util';
import { ConvertConfirmationComponent } from 'src/app/features/data-management/convert-confirmation/convert-confirmation.component';
import { FetchDataConversionStatus } from 'src/app/reducers/data/data-management.actions';
import { getDataConversionStatus } from 'src/app/reducers/data/data-management.reducer';
import {
  getPermissions,
  getPermissionsIsLoading,
} from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getAdminsAndReportees,
  getAdminsAndReporteesIsLoading,
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { DataManagementService } from 'src/app/services/controllers/data-management.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'data-convert',
  templateUrl: './data-convert.component.html',
})
export class DataConvertComponent implements OnInit, OnDestroy {
  @ViewChild('convertPopUp') convertPopUp: any;
  data: any;
  allUsers: any[] = [];
  users: any[] = [];
  assignToUsersList: any[] = [];
  deactiveUsers: any[] = [];
  assignToUserForm: FormGroup;
  stopper: EventEmitter<void> = new EventEmitter<void>();
  conditionalStatus: ConversionStatus[];
  selectedStatus: any;
  // minDate: Date;
  isPermissionsLoading: boolean = true;
  isProjectsListLoading: boolean = true;
  isUsersListForReassignmentLoading: boolean = true;
  isAdminsAndReporteesLoading: boolean = true;
  isAssignToLoading: boolean = true;
  isDuplicatesAllowed: boolean = false;
  currentDate: Date = new Date();
  userData: any;
  onPickerOpened = onPickerOpened;

  get minDate(): Date {
    const minDate = new Date(this.currentDate);
    minDate.setHours(0, 0, 0, 0);
    return minDate;
  }
  isConvertLoading: boolean = false;

  sad: AnimationOptions = {
    path: 'assets/animations/sad.json',
  };

  get assignTo(): string {
    return getAssignedToDetails(this.data?.assignTo, this.allUsers, true) || '';
  }

  constructor(
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private router: Router,
    private api: DataManagementService,
    private shareDataService: ShareDataService,

  ) { }

  ngOnInit(): void {
    // this.minDate = new Date();
    // this.minDate.setDate(this.minDate.getDate());

    this._store.dispatch(new FetchDataConversionStatus());
    this._store
      .select(getDataConversionStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((statuses: ConversionStatus[]) => {
        this.conditionalStatus = statuses;
      });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this.assignToUserForm = this.formBuilder.group({
      assignedToUsers: [null, Validators.required],
      status: [null],
      subStatus: [null],
      scheduledDate: [null],
      isQualified: true,
      notes: [null],
      projects: [null],
    });

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isUsersListForReassignmentLoading = isLoading;
      });

    this._store
      .select(getAdminsAndReporteesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAdminsAndReporteesLoading = isLoading;
      });

    this.fetchUsersBasedOnPermission();
  }

  /**
   * Function to prepare the user list to display in the dropdown
   * @param usersList
   */
  prepareUsersList(usersList: any): void {
    this.users = usersList?.filter((user: any) => user?.isActive);
    this.deactiveUsers = usersList?.filter((user: any) => !user.isActive);
    this.assignToUsersList = this.users?.map((user: any) => {
      return {
        ...user,
        label: `${user.firstName} ${user.lastName}`,
        value: user.id,
      };
    });
  }

  /**
   * Function to check the permissions the user has
   * and fetch users list accordingly
   */
  fetchUsersBasedOnPermission(): void {
    this._store
      .select(getPermissionsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isPermissionsLoading = isLoading;
      });

    this._store
      .select(getPermissions)
      .pipe(
        skipWhile(() => this.isPermissionsLoading),
        take(1)
      )
      .subscribe((permissions: string[]) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.fetchAllUsers();
        } else {
          this.fetchAdminAndReportees();
        }
      });
  }

  /**
   * Function to fetch admin and reportees
   */
  fetchAdminAndReportees() {
    this._store.dispatch(new FetchAdminsAndReportees());
    this._store
      .select(getAdminsAndReportees)
      .pipe(
        skipWhile(() => this.isAdminsAndReporteesLoading),
        take(1)
      )
      .subscribe((adminsWithReportees: any) => {
        if (!adminsWithReportees?.length) return;
        this.prepareUsersList(adminsWithReportees);
        this.isAssignToLoading = false;
      });
  }

  /**
   * Function to fetch all users.
   */
  fetchAllUsers() {
    this._store.dispatch(new FetchUsersListForReassignment());
    this._store
      .select(getUsersListForReassignment)
      .pipe(
        skipWhile(() => this.isUsersListForReassignmentLoading),
        take(1)
      )
      .subscribe((allUsers: any) => {
        if (!allUsers?.length) return;
        this.allUsers = allUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.prepareUsersList(allUsers);
        this.isAssignToLoading = false;
      });
  }

  /**
   * Function to update the selectedStatus when the user selects a status
   * and add or remove required validation for sub statuses.
   * The required validation is added if there are sub statuses for the selected status
   * If there are no sub statuses the validation is removed
   * @param status Currently selected status
   */
  statusChanged(status: any): void {
    this.selectedStatus = status;
    toggleValidation(VALIDATION_SET, this.assignToUserForm, 'scheduledDate', [
      Validators.required,
      validateScheduleTime(this.currentDate),
    ]);
    if (this.selectedStatus?.children?.length) {
      toggleValidation(VALIDATION_SET, this.assignToUserForm, 'subStatus', [
        Validators.required,
      ]);
    }
  }

  openConfirmDeleteModal(dataName: string) {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: dataName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.modalService.hide();
        }
      });
    }
  }

  save() {
    if (!this.assignToUserForm.valid) {
      validateAllFormFields(this.assignToUserForm);
      return;
    }
    const formData = this.assignToUserForm.value;
    const payload = {
      id: this.data?.id,
      assignTo: formData?.assignedToUsers,
      leadStatusId: formData?.subStatus || formData?.status,
      scheduledDate: setTimeZoneDateWithTime(
        formData?.scheduledDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      notes: formData?.notes,
      projects: formData?.projects,
      convertedDateTime: this.userData?.timeZoneInfo?.timeZoneName
        ? `${getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dateWithFullTime')} (${this.userData?.timeZoneInfo?.timeZoneName})`
        : getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dateWithFullTime')
    };
    this.isConvertLoading = true;
    this.modalService.hide();
    this.api.convertToLead(payload).subscribe(
      (resp: any) => {
        this.isConvertLoading = false;
        this.router.navigate(['data/manage-data']);
        if (resp?.succeeded) {
          if (resp?.data?.prospectId && resp?.data?.assignTo) {
            let modal = this.modalService.show(this.convertPopUp, {
              class: 'modal-450 modal-dialog-centered ph-modal-unset',
            });
            setTimeout(() => {
              modal.hide();
            }, 10000);
          } else {
            this.showConvertToLeadSuccess();
          }
        }
      },
      () => (this.isConvertLoading = false)
    );
    // this._store.dispatch(new ConvertToLead(payload));
    // this._store
    //   .select(getIsConvertToLeadLoading)
    //   .pipe(
    //     skipWhile((isLoading: boolean) => isLoading),
    //     take(1)
    //   )
    //   .subscribe(() => {
    //     this.showConvertToLeadSuccess();
    //   });
  }

  showConvertToLeadSuccess() {
    let initialState: any = {
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    const bulkSuccessModalRef = this.modalService.show(
      ConvertConfirmationComponent,
      initialState
    );
    setTimeout(() => {
      bulkSuccessModalRef.hide();
    }, 10000);
  }

  /**
   * Function to deselct the status selected
   * remove the validation on scheduledDate
   * reset form values for status, subStatus, scheduledDate
   */
  deselectStatus(): void {
    this.selectedStatus = null;
    toggleValidation(VALIDATION_CLEAR, this.assignToUserForm, 'scheduledDate');
    toggleValidation(VALIDATION_CLEAR, this.assignToUserForm, 'subStatus');
    this.assignToUserForm?.patchValue({
      status: null,
      subStatus: null,
      scheduledDate: null,
      projects: null,
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
