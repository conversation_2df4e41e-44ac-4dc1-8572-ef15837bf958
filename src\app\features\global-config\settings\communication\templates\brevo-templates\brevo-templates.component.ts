import { Component, EventEmitter, OnInit, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { DeleteBrevoTemplate, GetBrevoTemplates, SyncBrevoTemplates, UpdateBrevoTemplateFilterPayload } from 'src/app/reducers/email/email-settings.action';
import { getBrevoTemplateList, getBrevoTemplateListIsLoading, getBrevoTemplateTotalCount, getBrevoTotalCount } from 'src/app/reducers/email/email-settings.reducer';
import { getPages } from 'src/app/core/utils/common.util';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AddBrevoTemplateComponent } from './add-brevo-template/add-brevo-template.component';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';

@Component({
  selector: 'brevo-templates',
  templateUrl: './brevo-templates.component.html',
})
export class BrevoTemplatesComponent implements OnInit {
  showEntriesSize = SHOW_ENTRIES;
  brevoForm: FormGroup;
  filterPayload: any;
  isTemplateLoading: boolean = true;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  brevoTemplates: any;
  totalCount: number = 0;
  currOffset: number = 0;
  currPageSize: number = PAGE_SIZE;
  getPages = getPages;
  canTemplatesUpdate: boolean;
  canTemplatesCreate: boolean;
  canTemplatesDelete: boolean;


  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    private modalService: BsModalService,
    public modalRef: BsModalRef,
  ) { }

  ngOnInit(): void {
    this.brevoForm = this.fb.group({
      searchTerm: ['', [Validators.required]],
      pageNumber: [1],
      pageSize: [this.currPageSize],
    });
    this.filterFunction();
    this.initializeSubscriptions();
  }

  initializeSubscriptions() {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Templates.Update')) {
          this.canTemplatesUpdate = true;
        }
        if (permissions?.includes('Permissions.Templates.Create')) {
          this.canTemplatesCreate = true;
        }
        if (permissions?.includes('Permissions.Templates.Delete')) {
          this.canTemplatesDelete = true;
        }
      });
    this.store.select(getBrevoTemplateList).pipe(takeUntil(this.stopper)).subscribe((res: any) => {
      this.brevoTemplates = res;
    });
    this.store.select(getBrevoTemplateListIsLoading).pipe(takeUntil(this.stopper)).subscribe((loading: boolean) => {
      this.isTemplateLoading = loading;
    });

    this.store.select(getBrevoTemplateTotalCount).pipe(takeUntil(this.stopper)).subscribe((resp) => {
      this.totalCount = resp;
    });
  }

  assignPageSize() {
    this.currPageSize = this.brevoForm.value.pageSize;
    this.currOffset = 0;
    this.brevoForm.patchValue({
      pageNumber: 1,
    });
    this.filterFunction();
  }

  filterFunction() {
    const values = this.brevoForm.value;
    this.filterPayload = {
      ...values,
    };
    this.store.dispatch(new UpdateBrevoTemplateFilterPayload(this.filterPayload));
    this.store.dispatch(new GetBrevoTemplates());
  }

  onPageChange(event: any) {
    this.currOffset = event;
    this.brevoForm.patchValue({
      pageNumber: event + 1,
    });
    this.filterFunction();
  }

  isEmptyInput(event: any) {
    if (event.target.value === '') {
      this.filterFunction();
    }
  }

  openDeleteModal(id: string, deleteModal: TemplateRef<any>) {
    this.modalRef = this.modalService.show(deleteModal, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteBrevoTemplate(id));
        }
      });
    }
  }

  openAddModal() {
    this.modalRef = this.modalService.show(AddBrevoTemplateComponent, {
      class: 'modal-600 modal-dialog-centered right-modal ip-modal-unset',
    });
  }

  openEditModal(data: any) {
    const initialState = {
      selectedTemplate: data,
    };
    this.modalRef = this.modalService.show(AddBrevoTemplateComponent, {
      class: 'modal-600 modal-dialog-centered right-modal ip-modal-unset',
      initialState,
    });
  }

  syncBrevoTemplates() {
    this.store.dispatch(new SyncBrevoTemplates());
  }
  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
