import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Action, Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { Observable, of } from 'rxjs';
import { catchError, map, mergeMap, switchMap, take } from 'rxjs/operators';

import { OnError } from 'src/app/app.actions';
import { FolderNamesS3 } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
    AddReportAutomation,
    AddReportAutomationSuccess,
    CleanMeetingVisitReportList,
    DeleteReportAutomation,
    DisableReportAutomation,
    DisableReportAutomationSuccess,
    ExistReportAutomation,
    ExistReportAutomationSuccess,
    ExportActivity,
    ExportActivitySuccess,
    ExportAgencyStatus,
    ExportAgencyStatusSuccess,
    ExportCallStatus,
    ExportCallStatusSuccess,
    ExportCityReport,
    ExportMeetingSiteVisitStatus,
    ExportMeetingSiteVisitStatusSuccess,
    ExportProjectStatus,
    ExportProjectStatusSuccess,
    ExportProjectSubstatus,
    ExportProjectSubstatusSuccess,
    ExportReceivedDateStatus,
    ExportReceivedDateStatusSuccess,
    ExportReportCampaignSubStatus,
    ExportReportCampaignSubStatusSuccess,
    ExportReportCpSubStatus,
    ExportReportCpSubStatusSuccess,
    ExportSourceStatus,
    ExportSourceStatusSuccess,
    ExportSubReport,
    ExportSubReportSuccess,
    ExportSubSourceStatus,
    ExportSubSourceStatusSuccess,
    ExportSubStatus,
    ExportSubStatusSuccess,
    ExportUserMeetingSiteReport,
    ExportUserMeetingSiteReportSuccess,
    ExportUserSourceReport,
    ExportUserSourceReportSuccess,
    ExportUserStatus,
    ExportUserStatusSuccess,
    ExportUserSubSourceReport,
    ExportUserSubSourceReportSuccess,
    FetchActivityExport,
    FetchActivityExportSuccess,
    FetchAgencyExport,
    FetchAgencyExportSuccess,
    FetchCallExport,
    FetchCallExportSuccess,
    FetchCityReports,
    FetchCityReportsCount,
    FetchCityReportsCountSuccess,
    FetchCityReportsSuccess,
    FetchExportReportAutomationStatus,
    FetchExportReportAutomationStatusSuccess,
    FetchMeetingSiteVisitExport,
    FetchMeetingSiteVisitExportSuccess,
    FetchProjectExport,
    FetchProjectExportSuccess,
    FetchProjectSubstatusExport,
    FetchProjectSubstatusExportSuccess,
    FetchReceivedDateExport,
    FetchReceivedDateExportSuccess,
    FetchReportAutomationList,
    FetchReportAutomationListSuccess,
    FetchReportAutomationTypeList,
    FetchReportAutomationTypeListSuccess,
    FetchReportCampaignSubStatus,
    FetchReportCampaignSubStatusExport,
    FetchReportCampaignSubStatusExportSuccess,
    FetchReportCampaignSubStatusSuccess,
    FetchReportCampaignSubStatusTotalCount,
    FetchReportCampaignSubStatusTotalCountSuccess,
    FetchReportCpSubStatus,
    FetchReportCpSubStatusExport,
    FetchReportCpSubStatusExportSuccess,
    FetchReportCpSubStatusSuccess,
    FetchReportCpSubStatusTotalCount,
    FetchReportCpSubStatusTotalCountSuccess,
    FetchReportExportTracker,
    FetchReportExportTrackerSuccess,
    FetchReportFlagCount,
    FetchReportFlagCountSuccess,
    FetchReportsActivity1,
    FetchReportsActivity10,
    FetchReportsActivity10Success,
    FetchReportsActivity11,
    FetchReportsActivity11Success,
    FetchReportsActivity12,
    FetchReportsActivity12Success,
    FetchReportsActivity1Success,
    FetchReportsActivity2,
    FetchReportsActivity2Success,
    FetchReportsActivity3,
    FetchReportsActivity9,
    FetchReportsActivity9Success,
    FetchReportsActivityTotalCount,
    FetchReportsActivityTotalCountSuccess,
    FetchReportsAgency,
    FetchReportsAgencyCustom,
    FetchReportsAgencyCustomSuccess,
    FetchReportsAgencyCustomTotalCount,
    FetchReportsAgencyCustomTotalCountSuccess,
    FetchReportsAgencySuccess,
    FetchReportsAgencyTotalCount,
    FetchReportsAgencyTotalCountSuccess,
    FetchReportsCall,
    FetchReportsCallSuccess,
    FetchReportsCallTotalCount,
    FetchReportsCallTotalCountSuccess,
    FetchReportsCustomProjects,
    FetchReportsCustomProjectsSuccess,
    FetchReportsCustomProjectsTotalCount,
    FetchReportsCustomProjectsTotalCountSuccess,
    FetchReportsCustomSources,
    FetchReportsCustomSourcesSuccess,
    FetchReportsCustomSourceTotalCount,
    FetchReportsCustomSourceTotalCountSuccess,
    FetchReportsCustomSubSourcesSuccess,
    FetchReportsCustomSubSourceTotalCount,
    FetchReportsCustomSubSourceTotalCountSuccess,
    FetchReportsCustomUser,
    FetchReportsCustomUserSuccess,
    FetchReportsCustomUserTotalCount,
    FetchReportsCustomUserTotalCountSuccess,
    FetchReportsMeetingSiteVisitLevel1,
    FetchReportsMeetingSiteVisitLevel1Success,
    FetchReportsMeetingSiteVisitLevel2,
    FetchReportsMeetingSiteVisitLevel2Success,
    FetchReportsMeetingSiteVisitTotalCount,
    FetchReportsMeetingSiteVisitTotalCountSuccess,
    FetchReportsProject,
    FetchReportsProjectSubstatus,
    FetchReportsProjectSubstatusSuccess,
    FetchReportsProjectSuccess,
    FetchReportsProjectTotalCount,
    FetchReportsProjectTotalCountSuccess,
    FetchReportsProjSubTotalCount,
    FetchReportsProjSubTotalCountSuccess,
    FetchReportsReceivedDate,
    FetchReportsReceivedDateSuccess,
    FetchReportsReceivedDateTotalCount,
    FetchReportsReceivedDateTotalCountSuccess,
    FetchReportsSources,
    FetchReportsSourcesSuccess,
    FetchReportsSourceTotalCount,
    FetchReportsSourceTotalCountSuccess,
    FetchReportsSubSources,
    FetchReportsSubSourcesSuccess,
    FetchReportsSubSourceTotalCount,
    FetchReportsSubSourceTotalCountSuccess,
    FetchReportsSubStatus,
    FetchReportsSubStatusSuccess,
    FetchReportsSubStatusTotalCount,
    FetchReportsSubStatusTotalCountSuccess,
    FetchReportsSubTotalCount,
    FetchReportsSubTotalCountSuccess,
    FetchReportsUserMeetingSite,
    FetchReportsUserMeetingSiteSuccess,
    FetchReportsUserMeetingSiteTotalCount,
    FetchReportsUserMeetingSiteTotalCountSuccess,
    FetchReportsUser as FetchReportsUsers,
    FetchReportsUserSource,
    FetchReportsUserSourceSuccess,
    FetchReportsUserSourceTotalCount,
    FetchReportsUserSourceTotalCountSuccess,
    FetchReportsUserSuccess as FetchReportsUsersSuccess,
    FetchReportsUserSubSource,
    FetchReportsUserSubSourceSuccess,
    FetchReportsUserSubSourceTotalCount,
    FetchReportsUserSubSourceTotalCountSuccess,
    FetchReportsUserTotalCount,
    FetchReportsUserTotalCountSuccess,
    FetchSourceExport,
    FetchSourceExportSuccess,
    FetchSubReport,
    FetchSubReportExport,
    FetchSubReportExportSuccess,
    FetchSubReportSuccess,
    FetchSubSourceExport,
    FetchSubSourceExportSuccess,
    FetchSubStatusExport,
    FetchSubStatusExportSuccess,
    FetchUserExport,
    FetchUserExportSuccess,
    FetchUserMeetingSiteExport,
    FetchUserMeetingSiteExportSuccess,
    FetchUserSourceExport,
    FetchUserSourceExportSuccess,
    FetchUserSubSourceExport,
    FetchUserWithRole,
    FetchUserWithRoleSuccess,
    ReportsActionTypes,
    UpdateReportAutomation,
    UpdateReportAutomationsFiltersPayload,
    UpdateReportAutomationSuccess,
    UploadReportPdf,
    UploadReportPdfSuccess
} from 'src/app/reducers/reports/reports.actions';
import {
    getActivityFiltersPayload,
    getAgencyFiltersPayload,
    getCallFiltersPayload,
    getCampaignSubStatusFiltersPayload,
    getCityReportsFilter,
    getMeetingSiteVisitFiltersPayload,
    getProjectFiltersPayload,
    getProjectSubstatusFiltersPayload,
    getReceivedDateFiltersPayload,
    getReportAutomationFiltersPayload,
    getReportCpSubStatusFiltersPayload,
    getSourcesFiltersPayload,
    getSubReportFiltersPayload,
    getSubSourcesFiltersPayload,
    getSubStatusFiltersPayload,
    getUserFiltersPayload,
    getUserMeetingSiteFiltersPayload,
    getUserSourceFiltersPayload,
    getUserSubSourceFiltersPayload
} from 'src/app/reducers/reports/reports.reducer';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { ReportsService } from 'src/app/services/controllers/reports.service';
import { CommonService } from 'src/app/services/shared/common.service';
import { environment } from 'src/environments/environment';
import { getIsLeadCustomStatusEnabled } from '../lead/lead.reducer';

@Injectable()
export class ReportsEffects {
    getReportsUsers$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER),
            map((action: FetchReportsUsers) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsUserTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsUsersSuccess(resp);
                        }
                        return new FetchReportsUsersSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsUsersTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_TOTAL_COUNT),
            map((action: FetchReportsUserTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/user/new/status-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsUserTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomUsers$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER),
            map((action: FetchReportsCustomUser) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserFiltersPayload).subscribe((data: any) => {
                    data = {
                        ...data,
                        path: 'report/user/status/custom'
                    };
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsCustomUserTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsCustomUserSuccess(resp);
                        }
                        return new FetchReportsCustomUserSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomUsersTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER_TOTAL_COUNT),
            map((action: FetchReportsCustomUserTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/user/status/custom-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsCustomUserTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getUserExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_EXPORT),
            map((action: FetchUserExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchUserExportSuccess(resp.data);
                        }
                        return new FetchUserExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportUserStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_USER_STATUS),
            switchMap((action: ExportUserStatus) => {
                let isCustomStatusEnabled: any;
                this.store.select(getIsLeadCustomStatusEnabled).pipe(take(1)).subscribe((isEnabled: any) => {
                    isCustomStatusEnabled = isEnabled;
                });
                return (isCustomStatusEnabled ? this.api.customExportUserStatus(action.payload) : this.api.exportUserStatus(action.payload)).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportUserStatusSuccess(resp);
                        }
                        return new ExportUserStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsProjects$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_PROJECT),
            map((action: FetchReportsProject) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getProjectFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsProjectTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsProjectSuccess(resp);
                        }
                        return new FetchReportsProjectSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsProjectTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_PROJECT_TOTAL_COUNT),
            map((action: FetchReportsProjectTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getProjectFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/project/status/new-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsProjectTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getProjectExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_PROJECT_EXPORT),
            map((action: FetchProjectExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchProjectExportSuccess(resp.data);
                        }
                        return new FetchProjectExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportProjectStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_PROJECT_STATUS),
            switchMap((action: ExportProjectStatus) => {
                let isCustomStatusEnabled: any;
                this.store.select(getIsLeadCustomStatusEnabled).pipe(take(1)).subscribe((isEnabled: any) => {
                    isCustomStatusEnabled = isEnabled;
                });
                return (isCustomStatusEnabled ? this.api.exportCustomProjectStatus(action.payload) : this.api.exportProjectStatus(action.payload)).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportProjectStatusSuccess(resp);
                        }
                        return new ExportProjectStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSources$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SOURCES),
            map((action: FetchReportsSources) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSourcesFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsSourcesSuccess(resp);
                        }
                        return new FetchReportsSourcesSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomSources$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCES),
            map((action: FetchReportsCustomSources) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSourcesFiltersPayload).subscribe((data: any) => {
                    data = {
                        ...data,
                        path: "report/source/status/custom"
                    };
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsCustomSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsCustomSourcesSuccess(resp);
                        }
                        return new FetchReportsCustomSourcesSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSourceTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SOURCE_TOTAL_COUNT),
            map((action: FetchReportsSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSourcesFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/source/status/new-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomSourceTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCE_TOTAL_COUNT),
            map((action: FetchReportsCustomSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSourcesFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/source/status/custom-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsCustomSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getSourceExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SOURCE_EXPORT),
            map((action: FetchSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchSourceExportSuccess(resp.data);
                        }
                        return new FetchSourceExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportSourceStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_SOURCE_STATUS),
            switchMap((action: ExportSourceStatus) => {
                let isCustomStatusEnabled: any;
                this.store.select(getIsLeadCustomStatusEnabled).pipe(take(1)).subscribe((isEnabled: any) => {
                    isCustomStatusEnabled = isEnabled;
                });
                return (isCustomStatusEnabled ? this.api.exportCustomSourceStatus(action.payload) : this.api.exportSourceStatus(action.payload)).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportSourceStatusSuccess();
                        }
                        return new ExportSourceStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSubSources$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_SOURCES),
            map((action: FetchReportsSubSources) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getSubSourcesFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportsSubSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsSubSourcesSuccess(resp);
                        }
                        return new FetchReportsSubSourcesSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomSubSources$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCES),
            map((action: FetchReportsCustomSources) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getSubSourcesFiltersPayload)
                    .subscribe((data: any) => {
                        data = {
                            ...data,
                            path: 'report/subsource/status/custom'
                        };
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportsCustomSubSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsCustomSubSourcesSuccess(resp);
                        }
                        return new FetchReportsCustomSubSourcesSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSubSourceTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_TOTAL_COUNT),
            map((action: FetchReportsSubSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getSubSourcesFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/subsource/status/new-count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsSubSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomSubSourceTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCE_TOTAL_COUNT),
            map((action: FetchReportsCustomSubSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getSubSourcesFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/subsource/status/custom-count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsCustomSubSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getSubSourceExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_EXPORT),
            map((action: FetchSubSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchSubSourceExportSuccess(resp.data);
                        }
                        return new FetchSubSourceExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportSubSourceStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_SUB_SOURCE_STATUS),
            switchMap((action: ExportSubSourceStatus) => {
                let isCustomStatusEnabled: any;
                this.store.select(getIsLeadCustomStatusEnabled).pipe(take(1)).subscribe((isEnabled: any) => {
                    isCustomStatusEnabled = isEnabled;
                });
                return (isCustomStatusEnabled ? this.api.exportCustomSubSourceStatus(action.payload) : this.api.exportSubSourceStatus(action.payload)).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportSubSourceStatusSuccess();
                        }
                        return new ExportSubSourceStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsAgency$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_AGENCY),
            map((action: FetchReportsAgency) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getAgencyFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsAgencyTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsAgencySuccess(resp);
                        }
                        return new FetchReportsAgencySuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsAgencyCustom$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM),
            map((action: FetchReportsAgencyCustom) => action),
            switchMap((data: any) => {
                let filterPayload: any;
                this.store.select(getAgencyFiltersPayload).subscribe((data: any) => {
                    data = {
                        ...data,
                        path: 'report/agency/status/custom'
                    };
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsAgencyCustomTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsAgencyCustomSuccess(resp);
                        }
                        return new FetchReportsAgencyCustomSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsAgencyTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_AGENCY_TOTAL_COUNT),
            map((action: FetchReportsAgencyTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getAgencyFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/agency/status/new-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsAgencyTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsAgencyCustomTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM_TOTAL_COUNT),
            map((action: FetchReportsAgencyCustomTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getAgencyFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/agency/status/custom-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsAgencyCustomTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getAgencyExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_AGENCY_EXPORT),
            map((action: FetchAgencyExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchAgencyExportSuccess(resp.data);
                        }
                        return new FetchAgencyExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportAgencyStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_AGENCY_STATUS),
            switchMap((action: ExportAgencyStatus) => {
                let isCustomStatusEnabled: any;
                this.store.select(getIsLeadCustomStatusEnabled).pipe(take(1)).subscribe((isEnabled: any) => {
                    isCustomStatusEnabled = isEnabled;
                });
                return (isCustomStatusEnabled ? this.api.exportCustomAgencyStatus(action.payload) : this.api.exportAgencyStatus(action.payload)).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportAgencyStatusSuccess();
                        }
                        return new ExportAgencyStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsMeetingSiteVisitsL1$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1),
            map((action: FetchReportsMeetingSiteVisitLevel1) => action),
            switchMap((data: any) => {
                this.store.dispatch(new CleanMeetingVisitReportList());
                let filterPayload = {};
                this.store
                    .select(getMeetingSiteVisitFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportsMeetingSiteVisitLevel2());
                this.store.dispatch(new FetchReportsMeetingSiteVisitTotalCount());
                return this.commonService.getModuleListByAdvFilter({ ...filterPayload }).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsMeetingSiteVisitLevel1Success(resp.items);
                        }
                        return new FetchReportsMeetingSiteVisitLevel1Success();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )

    );

    getReportsMeetingSiteVisitsL2$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2),
            map((action: FetchReportsMeetingSiteVisitLevel2) => action),
            switchMap((data: any) => {
                let filterPayload = {};
                this.store
                    .select(getMeetingSiteVisitFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });

                return this.commonService.getModuleListByAdvFilter({ ...filterPayload, path: 'report/user/meetingandvisit/level2' }).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsMeetingSiteVisitLevel2Success(resp.items);
                        }
                        return new FetchReportsMeetingSiteVisitLevel2Success();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )

    );

    getReportsMeetingSiteVisitsTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_TOTAL_COUNT),
            map((action: FetchReportsMeetingSiteVisitTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getMeetingSiteVisitFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/user/meetingandvisit/count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsMeetingSiteVisitTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getMeetingSiteVisitExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_EXPORT),
            map((action: FetchMeetingSiteVisitExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchMeetingSiteVisitExportSuccess(resp.data);
                        }
                        return new FetchMeetingSiteVisitExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportUserMeetingandvisit$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_MEETING_SITE_VISIT_STATUS),
            switchMap((action: ExportMeetingSiteVisitStatus) => {
                return this.api.exportUserMeetingandvisit(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportMeetingSiteVisitStatusSuccess();
                        }
                        return new ExportMeetingSiteVisitStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsActivity1$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY1),
            map((action: FetchReportsActivity1) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsActivity2());
                this.store.dispatch(new FetchReportsActivity3());
                this.store.dispatch(new FetchReportsActivityTotalCount());

                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsActivity1Success(resp);
                        }
                        return new FetchReportsActivity1Success();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsActivity2$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY2),
            map((action: FetchReportsActivity2) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = {
                        ...data,
                        path: 'report/activity/level2',
                    };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsActivity2Success(resp);
                        }
                        return new FetchReportsActivity2Success();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsActivity9$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY9),
            map((action: FetchReportsActivity9) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = {
                        ...data,
                        path: 'report/activity/level9',
                    };
                });
                this.store.dispatch(new FetchReportsActivity10());
                this.store.dispatch(new FetchReportsActivity11());
                this.store.dispatch(new FetchReportsActivity12());
                this.store.dispatch(new FetchReportsActivityTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsActivity9Success(resp);
                        }
                        return new FetchReportsActivity9Success();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
    getReportsActivity10$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY10),
            map((action: FetchReportsActivity10) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = {
                        ...data,
                        path: 'report/activity/level10',
                    };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsActivity10Success(resp);
                        }
                        return new FetchReportsActivity10Success();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
    getReportsActivity11$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY11),
            map((action: FetchReportsActivity11) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = {
                        ...data,
                        path: 'report/activity/level11',
                    };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsActivity11Success(resp);
                        }
                        return new FetchReportsActivity11Success();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsActivity12$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY12),
            map((action: FetchReportsActivity12) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = {
                        ...data,
                        path: 'report/activity/level12',
                    };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsActivity12Success(resp);
                        }
                        return new FetchReportsActivity12Success();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsActivityTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY_TOTAL_COUNT),
            map((action: FetchReportsActivityTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getActivityFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/activity-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsActivityTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getActivityExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_ACTIVITY_EXPORT),
            map((action: FetchActivityExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchActivityExportSuccess(resp.data);
                        }
                        return new FetchActivityExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportActivity$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_ACTIVITY),
            switchMap((action: ExportActivity) => {
                return this.api.exportActivity(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportActivitySuccess();
                        }
                        return new ExportActivitySuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSubStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_STATUS),
            map((action: FetchReportsSubStatus) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSubStatusFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsSubStatusTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsSubStatusSuccess(resp);
                        }
                        return new FetchReportsSubStatusSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSubStatusTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_TOTAL_COUNT),
            map((action: FetchReportsSubStatusTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSubStatusFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/substatus/new-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsSubStatusTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getSubStatusExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_EXPORT),
            map((action: FetchSubStatusExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchSubStatusExportSuccess(resp.data);
                        }
                        return new FetchSubStatusExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportSubStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_SUB_STATUS),
            switchMap((action: ExportSubStatus) => {
                return this.api.exportSubStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportSubStatusSuccess();
                        }
                        return new ExportSubStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getSubReport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_SUB_REPORT),
            map((action: FetchSubReport) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getSubReportFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsSubTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchSubReportSuccess(resp);
                        }
                        return new FetchSubReportSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsSubTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_SUB_TOTAL_COUNT),
            map((action: FetchReportsSubTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getSubReportFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/substatus/bysubsource/new-count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsSubTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getSubReportExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_SUB_REPORT_EXPORT),
            map((action: FetchSubReportExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchSubReportExportSuccess(resp.data);
                        }
                        return new FetchSubReportExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportSubReport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_SUB_REPORT),
            switchMap((action: ExportSubReport) => {
                return this.api.exportSubReport(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportSubReportSuccess();
                        }
                        return new ExportSubReportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsProjectSubstatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS),
            map((action: FetchReportsProjectSubstatus) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getProjectSubstatusFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportsProjSubTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsProjectSubstatusSuccess(resp);
                        }
                        return new FetchReportsProjectSubstatusSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsProjSubstatusTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_PROJ_SUB_STATUS_TOTAL_COUNT),
            map((action: FetchReportsProjSubTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getProjectSubstatusFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/project/bysubstatus/new-count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsProjSubTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getProjectSubstatusExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS_EXPORT),
            map((action: FetchProjectSubstatusExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchProjectSubstatusExportSuccess(resp.data);
                        }
                        return new FetchProjectSubstatusExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportProjectSubstatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_PROJECT_SUB_STATUS),
            switchMap((action: ExportProjectSubstatus) => {
                return this.api.exportProjectSubstatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportProjectSubstatusSuccess(resp);
                        }
                        return new ExportProjectSubstatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCall$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CALL),
            map((action: FetchReportsCall) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getCallFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsCallTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsCallSuccess(resp);
                        }
                        return new FetchReportsCallSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCallTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CALL_TOTAL_COUNT),
            map((action: FetchReportsCallTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getCallFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/user/call-log/new-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsCallTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getCallExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CALL_EXPORT),
            map((action: FetchCallExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchCallExportSuccess(resp.data);
                        }
                        return new FetchCallExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportCallStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_CALL_STATUS),
            switchMap((action: ExportCallStatus) => {
                return this.api.exportCallStatus(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportCallStatusSuccess();
                        }
                        return new ExportCallStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getExportStatusList$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_EXPORT_TRACKER),
            map((action: FetchReportExportTracker) => action),
            switchMap((data: any) => {
                return this.api.getReportsExportStatus(data.payload, data?.pageNumber, data?.pageSize).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportExportTrackerSuccess(resp);
                        }
                        return new FetchReportExportTrackerSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsReceivedDate$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE),
            map((action: FetchReportsReceivedDate) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getReceivedDateFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportsReceivedDateTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsReceivedDateSuccess(resp);
                        }
                        return new FetchReportsReceivedDateSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsReceivedDateTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_TOTAL_COUNT),
            map((action: FetchReportsReceivedDateTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getReceivedDateFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            // need to change
                            path: 'report/datewisesource/count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsReceivedDateTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReceivedDateExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_EXPORT),
            map((action: FetchReceivedDateExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReceivedDateExportSuccess(resp.data);
                        }
                        return new FetchReceivedDateExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportReceivedDate$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_RECEIVED_DATE_STATUS),
            switchMap((action: ExportReceivedDateStatus) => {
                return this.api.exportReceivedDate(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportReceivedDateStatusSuccess();
                        }
                        return new ExportReceivedDateStatusSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getFlagCountList$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_FLAG_COUNT),
            map((action: FetchReportFlagCount) => action),
            switchMap((data: any) => {
                let payload = { ...data?.payload, path: "report/activity/flags" };
                return this.api.getFlagsCounts(payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportFlagCountSuccess(resp);
                        }
                        return new FetchReportFlagCountSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsUserSource$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_SOURCE),
            map((action: FetchReportsUserSource) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserSourceFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsUserSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsUserSourceSuccess(resp);
                        }
                        return new FetchReportsUserSourceSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsUserSourceTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_TOTAL_COUNT),
            map((action: FetchReportsUserSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getUserSourceFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/uservssource/count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsUserSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getUserSourceExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_EXPORT),
            map((action: FetchUserSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchUserSourceExportSuccess(resp.data);
                        }
                        return new FetchUserSourceExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportUserSource$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_USER_SOURCE_REPORT),
            switchMap((action: ExportUserSourceReport) => {
                return this.api.exportUserSource(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportUserSourceReportSuccess();
                        }
                        return new ExportUserSourceReportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );


    getReportsUserSubSource$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE),
            map((action: FetchReportsUserSubSource) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserSubSourceFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsUserSubSourceTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsUserSubSourceSuccess(resp);
                        }
                        return new FetchReportsUserSubSourceSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsUserSubSourceTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_TOTAL_COUNT),
            map((action: FetchReportsUserSubSourceTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getUserSubSourceFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/uservssubsource/count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsUserSubSourceTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getUserSubSourceExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_EXPORT),
            map((action: FetchUserSubSourceExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchUserSourceExportSuccess(resp.data);
                        }
                        return new FetchUserSourceExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportUserSubSource$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_USER_SUB_SOURCE_REPORT),
            switchMap((action: ExportUserSubSourceReport) => {
                return this.api.exportUserSubSource(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportUserSubSourceReportSuccess();
                        }
                        return new ExportUserSubSourceReportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsCustomProjects$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS),
            map((action: FetchReportsCustomProjects) => action),
            switchMap((data: any) => {

                let filterPayload;
                this.store.select(getProjectFiltersPayload).subscribe((data: any) => {
                    data = {
                        ...data,
                        path: "report/project/status/custom"
                    };
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsCustomProjectsTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsCustomProjectsSuccess(resp);
                        }
                        return new FetchReportsCustomProjectsSuccess();
                    }),
                    catchError((err: Error) => {
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    getReportsCustomProjectsTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS_TOTAL_COUNT),
            map((action: FetchReportsCustomProjectsTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getProjectFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/project/status/custom-count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsCustomProjectsTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsUserMeetingSite$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE),
            map((action: FetchReportsUserMeetingSite) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getUserMeetingSiteFiltersPayload).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchReportsUserMeetingSiteTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportsUserMeetingSiteSuccess(resp);
                        }
                        return new FetchReportsUserMeetingSiteSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportsUserMeetingSiteTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_TOTAL_COUNT),
            map((action: FetchReportsUserMeetingSiteTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getUserMeetingSiteFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/user/meetingandvisit/count/fromhistory',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportsUserMeetingSiteTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getUserMeetingSiteExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_EXPORT),
            map((action: FetchUserMeetingSiteExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchUserMeetingSiteExportSuccess(resp.data);
                        }
                        return new FetchUserMeetingSiteExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportUserMeetingSite$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_USER_MEETING_SITE_REPORT),
            switchMap((action: ExportUserMeetingSiteReport) => {
                return this.api.exportUserMeetingSite(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportUserMeetingSiteReportSuccess();
                        }
                        return new ExportUserMeetingSiteReportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getCityReports$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_CITY_REPORTS),
            map((action: FetchCityReports) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getCityReportsFilter).subscribe((data: any) => {
                    filterPayload = data;
                });
                this.store.dispatch(new FetchCityReportsCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchCityReportsSuccess(resp);
                        }
                        return new FetchCityReportsSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getCityReportsTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_CITY_REPORTS_COUNT),
            map((action: FetchCityReportsCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store.select(getCityReportsFilter).subscribe((data: any) => {
                    filterPayload = data;
                    filterPayload = { ...data, path: 'report/cityvslead/count' };
                });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchCityReportsCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    exportCityReports$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_CITY_REPORTS),
            switchMap((action: ExportCityReport) => {
                return this.api.exportCityReport(action.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return null;
                        }
                        return null;
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );
    // ----------------------- REPORT AUTOMATION --------------------------------

    getReportAutomationList$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORT_AUTOMATION),
            switchMap((action: FetchReportAutomationList) =>
                this.commonService.getModuleListByAdvFilter(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp.succeeded && resp?.items?.length === 0) {
                            const previousPageNumber = action.payload.PageNumber || 1;
                            if (previousPageNumber > 1) {
                                const retryPayload = {
                                    ...action.payload,
                                    PageNumber: previousPageNumber - 1,
                                };
                                this.store.dispatch(new UpdateReportAutomationsFiltersPayload(retryPayload))
                                return this.commonService.getModuleListByAdvFilter(retryPayload).pipe(
                                    map((retryResp: any) => new FetchReportAutomationListSuccess(retryResp)),
                                    catchError((err) => of(new OnError(err)))
                                );
                            }
                        }
                        return of(new FetchReportAutomationListSuccess(resp));
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    existReportAutomation$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXIST_REPORT_AUTOMATION),
            map((action: ExistReportAutomation) => action.reportName),
            switchMap((data: any) => {
                return this.api.existReportAutomation(data).pipe(
                    map((resp: any) => {
                        return new ExistReportAutomationSuccess(resp.data);
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    getExportReportAutomationStatusList$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_EXPORT_REPORT_AUTOMATION_STATUS),
            map((action: FetchExportReportAutomationStatus) => action),
            switchMap((data: any) => {
                return this.api
                    .getExportReportAutomationStatus(data?.pageNumber, data?.pageSize)
                    .pipe(
                        map((resp: any) => {
                            if (resp.succeeded) {
                                return new FetchExportReportAutomationStatusSuccess(resp);
                            }
                            return new FetchExportReportAutomationStatusSuccess();
                        }),
                        catchError((err) => of(new OnError(err)))
                    );
            })
        )
    );

    getReportAutomationTypeList$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORT_AUTOMATION_TYPE),
            switchMap((action: FetchReportAutomationTypeList) =>
                this.api.getReportAutomationType().pipe(
                    map((resp: any) => new FetchReportAutomationTypeListSuccess(resp?.data)),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    addReportAutomation$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.ADD_REPORT_AUTOMATION),
            switchMap((action: AddReportAutomation) => {
                let PageNumber: number, PageSize: number
                this.store.select(getReportAutomationFiltersPayload).subscribe((data: any) => {
                    PageNumber = data?.PageNumber
                    PageSize = data?.PageSize
                })
                return this.api.addReportAutomation(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            this._notificationService.success(`Report added successfully.`);
                            return [
                                new AddReportAutomationSuccess(resp.data),
                                new FetchReportAutomationList({
                                    path: 'report/reportconfigurations',
                                    PageNumber,
                                    PageSize
                                }),
                            ];
                        }
                        return [new AddReportAutomationSuccess(resp.data)];
                    }),
                    catchError((err) => {
                        return of(new OnError(err));
                    })
                );
            })
        )
    );


    updateReportAutomation$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.UPDATE_REPORT_AUTOMATION),
            switchMap((action: UpdateReportAutomation) =>
                this.api.updateReportAutomation(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store.select(getReportAutomationFiltersPayload).pipe(
                                take(1),
                                switchMap((payload: any) => {
                                    this._notificationService.success(
                                        `Report updated successfully.`
                                    );
                                    return [
                                        new UpdateReportAutomationSuccess(resp),
                                        new FetchReportAutomationList(payload),
                                    ];
                                })
                            );
                        }
                        return [new UpdateReportAutomationSuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );


    deleteReportAutomation$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.DELETE_REPORT_AUTOMATION),
            switchMap((action: DeleteReportAutomation) => {
                let PageNumber: number, PageSize: number
                this.store.select(getReportAutomationFiltersPayload).subscribe((data: any) => {
                    PageNumber = data?.PageNumber
                    PageSize = data?.PageSize
                })
                return this.api.deleteReportAutomation(action.id).pipe(
                    mergeMap((resp: any) => {
                        if (resp?.succeeded) {
                            this._notificationService.success(`Report deleted successfully.`);
                            return [
                                new AddReportAutomationSuccess(resp.data),
                                new FetchReportAutomationList({
                                    path: 'report/reportconfigurations',
                                    PageNumber,
                                    PageSize,
                                }),
                            ];
                        } else {
                            return [];
                        }
                    }),
                    catchError((err) => {
                        return of(new OnError(err));
                    })
                );
            })
        )
    );

    disableReportAutomation$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.DISABLE_REPORT_AUTOMATION),
            switchMap((action: DisableReportAutomation) =>
                this.api.disableReportAutomation(action.payload).pipe(
                    switchMap((resp: any) => {
                        if (resp) {
                            return this.store.select(getReportAutomationFiltersPayload).pipe(
                                take(1),
                                switchMap((payload: any) => {
                                    this._notificationService.success(
                                        `Report status updated successfully.`
                                    );
                                    return [
                                        new DisableReportAutomationSuccess(resp),
                                        new FetchReportAutomationList(payload),
                                    ];
                                })
                            );
                        }
                        return [new UpdateReportAutomationSuccess(resp)];
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    fetchUserWithRole$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_USER_WITH_ROLE),
            switchMap((action: FetchUserWithRole) =>
                this.api.getUserWithRole().pipe(
                    map((resp: any) => new FetchUserWithRoleSuccess(resp?.data)),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    uploadReportPdf$ = createEffect((): Observable<Action> =>
        this.actions.pipe(
            ofType<UploadReportPdf>(ReportsActionTypes.UPLOAD_REPORT_PDF),
            switchMap((action: UploadReportPdf) =>
                this.blobStorageService.uploadDoc(action.payload.file, FolderNamesS3.ProjectUnit).pipe(
                    map((resp: any) => {
                        const s3Url = resp?.path ? (environment.s3ImageBucketURL + resp.path) : '';
                        return new UploadReportPdfSuccess(s3Url);
                    }),
                    catchError((err) => of(new OnError(err)))
                )
            )
        )
    );

    getReportCampaignSubStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS),
            map((action: FetchReportCampaignSubStatus) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getCampaignSubStatusFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportCampaignSubStatusTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportCampaignSubStatusSuccess(resp);
                        }
                        return new FetchReportsProjectSubstatusSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportCampaignSubStatusTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS_TOTAL_COUNT),
            map((action: FetchReportCampaignSubStatusTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getCampaignSubStatusFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/campaign/bysubstatus/count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportCampaignSubStatusTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportCampaignSubStatusExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS_EXPORT),
            map((action: FetchReportCampaignSubStatusExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportCampaignSubStatusExportSuccess(resp.data);
                        }
                        return new FetchProjectSubstatusExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportReportCampaignSubStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_CAMPAIGN_SUBSTATUS),
            map((action: ExportReportCampaignSubStatus) => action),
            switchMap((data: any) => {
                return this.api.exportCampaignSubStatus(data.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportReportCampaignSubStatusSuccess(resp);
                        }
                        return new ExportReportCampaignSubStatusSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportCpSubStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS),
            map((action: FetchReportCpSubStatus) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getReportCpSubStatusFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                    });
                this.store.dispatch(new FetchReportCpSubStatusTotalCount());
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportCpSubStatusSuccess(resp);
                        }
                        return new FetchReportCpSubStatusSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportCpSubStatusTotalCount$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS_TOTAL_COUNT),
            map((action: FetchReportCpSubStatusTotalCount) => action),
            switchMap((data: any) => {
                let filterPayload;
                this.store
                    .select(getReportCpSubStatusFiltersPayload)
                    .subscribe((data: any) => {
                        filterPayload = data;
                        filterPayload = {
                            ...data,
                            path: 'report/channelpartner/bysubstatus/count',
                        };
                    });
                return this.commonService.getModuleListByAdvFilter(filterPayload).pipe(
                    map((resp: any) => {
                        return new FetchReportCpSubStatusTotalCountSuccess(resp);
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    getReportCpSubStatusExport$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS_EXPORT),
            map((action: FetchReportCpSubStatusExport) => action.payload),
            switchMap((data: any) => {
                return this.commonService.getModuleList(data).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            return new FetchReportCpSubStatusExportSuccess(resp.data);
                        }
                        return new FetchReportCpSubStatusExportSuccess();
                    }),
                    catchError((err) => of(new OnError(err)))
                );
            })
        )
    );

    exportReportCpSubStatus$ = createEffect(() =>
        this.actions.pipe(
            ofType(ReportsActionTypes.EXPORT_CP_SUBSTATUS),
            map((action: ExportReportCpSubStatus) => action),
            switchMap((data: any) => {
                return this.api.exportCpSubStatus(data.payload).pipe(
                    map((resp: any) => {
                        if (resp.succeeded) {
                            this._notificationService.success(
                                `Reports are being exported in excel format`
                            );
                            return new ExportReportCpSubStatusSuccess(resp);
                        }
                        return new ExportReportCpSubStatusSuccess();
                    }),
                    catchError((err: Error) => of(new OnError(err)))
                );
            })
        )
    );

    constructor(
        private actions: Actions,
        private store: Store<AppState>,
        private api: ReportsService,
        private _notificationService: NotificationsService,
        private commonService: CommonService,
        private blobStorageService: BlobStorageService
    ) { }
}