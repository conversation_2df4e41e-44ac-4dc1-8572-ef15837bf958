import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { EmployeeTrackingComponent } from 'src/app/features/teams/employee-tracking/employee-tracking.component';
import { IndividualTrackingComponent } from 'src/app/features/teams/individual-tracking/individual-tracking.component';
import { LoginHistoryComponent } from 'src/app/features/teams/login-history/login-history.component';
import { AddRoleComponent } from 'src/app/features/teams/manage-role/add-role/add-role.component';
import { UserRolesComponent } from 'src/app/features/teams/manage-role/manage-role.component';
import { RolesActionComponent } from 'src/app/features/teams/manage-role/roles-action/roles-action.component';
import { AddTeamComponent } from 'src/app/features/teams/manage-team/add-team/add-team.component';
import { ManageTeamComponent } from 'src/app/features/teams/manage-team/manage-team.component';
import { MemberActionsComponent } from 'src/app/features/teams/manage-team/team-members/member-actions/member-actions.component';
import { TeamMembersComponent } from 'src/app/features/teams/manage-team/team-members/team-members.component';
import { AddUserComponent } from 'src/app/features/teams/manage-user/add-user/add-user.component';
import { AddGeoFencingComponent } from 'src/app/features/teams/manage-user/geo-fencing/add-geo-fencing/add-geo-fencing.component';
import { GeoFencingComponent } from 'src/app/features/teams/manage-user/geo-fencing/geo-fencing.component';
import { ManageUserActionsComponent } from 'src/app/features/teams/manage-user/manage-user-actions/manage-user-actions.component';
import { ManageUserComponent } from 'src/app/features/teams/manage-user/manage-user.component';
import { UserAssignmentComponent } from 'src/app/features/teams/manage-user/user-assignment/user-assignment.component';
import { AssignedDetailsComponent } from 'src/app/features/teams/manage-user/user-info/assigned-details/assigned-details.component';
import { ChangePasswordComponent } from 'src/app/features/teams/manage-user/user-info/change-password/change-password.component';
import { EditUserComponent } from 'src/app/features/teams/manage-user/user-info/edit-user/edit-user.component';
import { AttendanceVisualComponent } from 'src/app/features/teams/manage-user/user-info/user-attendance/attendance-visual/attendance-visual.component';
import { ClockInOutComponent } from 'src/app/features/teams/manage-user/user-info/user-attendance/clock-in-out/clock-in-out.component';
import { UserAttendanceComponent } from 'src/app/features/teams/manage-user/user-info/user-attendance/user-attendance.component';
import { UserInfoComponent } from 'src/app/features/teams/manage-user/user-info/user-info.component';
import { UserUploadedDocumentsComponent } from 'src/app/features/teams/manage-user/user-info/user-uploaded-documents/user-uploaded-documents.component';
import { TeamsComponent } from 'src/app/features/teams/teams.component';
import { WorkflowComponent } from 'src/app/features/teams/workflow/workflow.component';
import { ExcelUploadComponent } from 'src/app/shared/components/excel-upload/excel-upload.component';
import { PunchInComponent } from 'src/app/features/teams/manage-user/user-info/user-attendance/punch-in/punch-in.component';

export const routes: Routes = [
  { path: '', redirectTo: 'manage-user', pathMatch: 'full' },
  { path: 'manage-team', component: ManageTeamComponent },
  { path: 'manage-member/:id', component: TeamMembersComponent },
  { path: 'manage-role', component: UserRolesComponent },
  { path: 'manage-user', component: ManageUserComponent },
  { path: 'add-user', component: AddUserComponent },
  { path: 'edit-user/:id', component: AddUserComponent },
  { path: 'work-flow', component: WorkflowComponent },
  { path: 'login-history', component: LoginHistoryComponent },
  { path: 'user-details/:id', component: UserInfoComponent },
  { path: 'employee-tracking', component: EmployeeTrackingComponent },
  { path: 'tracking', component: IndividualTrackingComponent },
  { path: 'bulk-upload', component: ExcelUploadComponent },

];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TeamsRoutingModule { }

export const ROLES_DECLARATIONS = [
  TeamsComponent,
  ManageUserComponent,
  UserRolesComponent,
  AddRoleComponent,
  AddUserComponent,
  ManageTeamComponent,
  WorkflowComponent,
  AddTeamComponent,
  RolesActionComponent,
  ManageUserActionsComponent,
  LoginHistoryComponent,
  UserAssignmentComponent,
  UserInfoComponent,
  AssignedDetailsComponent,
  UserUploadedDocumentsComponent,
  UserAttendanceComponent,
  AttendanceVisualComponent,
  ClockInOutComponent,
  ChangePasswordComponent,
  EmployeeTrackingComponent,
  IndividualTrackingComponent,
  EditUserComponent,
  TeamMembersComponent,
  MemberActionsComponent,
  GeoFencingComponent,
  AddGeoFencingComponent,
  PunchInComponent
];
