import { LabelType, Options } from '@angular-slider/ngx-slider';
import { Component, EventEmitter, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { CellClickedEvent } from 'ag-grid-community';
import * as moment from 'moment';
import { BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { Subject } from 'rxjs';
import { filter, skipWhile, takeUntil } from 'rxjs/operators';

import {
  BHK_TYPE,
  PAGE_SIZE,
  PROPERTY_BUDGET_FILTER,
  PROPERTY_FILTERS_KEY_LABEL,
  PROP_DATE_TYPE,
  SHOW_ENTRIES,
  lockInPeriodList,
  noticePeriodList,
  securityDepositDates,
} from 'src/app/app.constants';
import {
  EnquiryType,
  Facing,
  FurnishStatus,
  PossessionType,
  PropertyDateType,
  PropertyPriceFilter,
} from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  PropertiesFilter,
  Property,
} from 'src/app/core/interfaces/property.interface';
import {
  assignToSort,
  changeCalendar,
  formatBudget,
  getAreaUnit,
  getAssignedToDetails,
  getBHKDisplayString,
  getLocationDetailsByObj,
  getPages,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTimeZoneDate,
  onFilterChanged,
  onPickerOpened,
  onlyNumbers,
  patchTimeZoneDate,
  setPropertySubTypeList,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { PropertiesActionGridComponent } from 'src/app/features/property/manage-properties/action-grid-child/action-grid-child.component';
import { PropertyAdvanceFilterComponent } from 'src/app/features/property/manage-properties/property-advance-filter/property-advance-filter.component';
import { MatchingLeadsComponent } from 'src/app/features/property/matching-leads/matching-leads.component';
import { PropertyStatusComponent } from 'src/app/features/property/property-status/property-status.component';
import { getAllAmenities, getAmenitiesLoading } from 'src/app/reducers/amenities-attributes/amenities-attributes.reducer';
import {
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchProjectList } from 'src/app/reducers/lead/lead.actions';
import { getProjectListIsLoading } from 'src/app/reducers/lead/lead.reducer';
import {
  FetchAreaUnitList,
  FetchPropertyAmenityList,
} from 'src/app/reducers/master-data/master-data.actions';
import {
  getAreaUnitIsLoading,
  getAreaUnits
} from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  FetchArchivedPropertyList,
  FetchExportPropertyStatus,
  FetchLocationList,
  FetchPropertyExcelUploadedList,
  FetchPropertyExportSuccess,
  FetchPropertyList,
  UpdateFilterPayload,
} from 'src/app/reducers/property/property.actions';
import {
  getArchivedProperties,
  getArchivedPropertiesIsLoading,
  getFiltersPayload,
  getLocationListIsLoading,
  getProperties,
  getPropertiesIsLoading,
  getPropertiesTotalCount,
  getPropertyLeadsCount,
} from 'src/app/reducers/property/property.reducer';
import {
  FetchAdminsAndReportees,
  FetchUsersListForReassignment,
} from 'src/app/reducers/teams/teams.actions';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
  getUsersListForReassignmentIsLoading,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { ExportPropertyTrackerComponent } from 'src/app/shared/components/export-property-tracker/export-property-tracker.component';

@Component({
  selector: 'manage-properties',
  templateUrl: './manage-properties.component.html',
})
export class ManagePropertiesComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();
  bulkReassignForm: FormGroup;
  rowData: any[] = [];
  gridOptions: any;
  columns: any[];
  propertyBudgets = PROPERTY_BUDGET_FILTER;
  pageSize: number = PAGE_SIZE;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  dateTypeList: Array<string> = PROP_DATE_TYPE?.filter(item => item !== 'Possession Date');
  budgetRange: Array<string> = PROPERTY_BUDGET_FILTER;
  currOffset: number = 0;
  currArchiveOffset: number = 0;
  columnDropDown: { field: string; hide: boolean }[] = [];
  selectedPageSize: number;
  defaultCurrency: string = 'INR';

  canEdit: boolean = false;
  canAdd: boolean = false;
  canDelete: boolean = false;
  canSearch: boolean = false;
  canAssign: boolean = false;
  canExport: boolean = false;
  canView: boolean = false;
  canViewAssigned: boolean = false;
  canBulkUpload: boolean = false;
  appliedFilter: any = {};
  filtersPayload: PropertiesFilter;
  searchTerm: string;
  userId: string;
  showFilters: boolean = false;
  bulkReassignPropertyIsLoading: boolean = false;
  propertyCurrencyIsLoading: boolean = false;
  isViewAllProperties: boolean = true;
  filterForm: FormGroup;
  propertiesTotalCount: number;
  archivedTotalCount: number;
  propertyTypeList: Array<any> = JSON.parse(
    localStorage.getItem('propertyType')
  );
  allPropertySubTypes: Array<Object> = [];
  propertySubTypes: Array<Object> = [];
  areaSizeUnits: Array<any> = [];
  isAreaUnitsLoading: boolean = true;
  propertySubTypeList: Array<{ displayName: string }> = [];
  isLocationListLoading: boolean = true;
  getPages = getPages;
  onlyNumbers = onlyNumbers;
  getBHKDisplayString = getBHKDisplayString;
  onPickerOpened = onPickerOpened;
  currentDate: Date = new Date();
  Possession: any = new Date();
  noDataFound: AnimationOptions = {
    path: 'assets/animations/empty-notes.json',
  };
  archivedPayload: any = {
    path: 'property/archived',
    id: null,
    pageNumber: 1,
    pageSize: this.pageSize,
    search: null,
  };
  archivedProperties: any;
  isArchivedPropertiesLoading: boolean = true;
  propertyCount: any;
  defaultColumns: any[];
  showLeftNav: boolean = true;
  isProjectsListLoading: boolean = true;
  isPropertyListLoading: boolean = true;
  propertyFiltersKeyLabel = {
    ...PROPERTY_FILTERS_KEY_LABEL,
    SerialNo: 'Serial No',
  };
  moment = moment;

  currentPath: string;
  options: Options = {
    floor: 0,
    ceil: 500,
    translate: (value: number, label: LabelType): string => {
      switch (label) {
        case LabelType.Low:
          return (
            '<span class="text-sm text-wrap">Min: ' + value + ' lakhs</span>'
          );
        case LabelType.High:
          return (
            '<span class="text-sm text-wrap">Max: ' + value + ' lakhs</span>'
          );
        default:
          return '';
      }
    },
  };
  amenities: any[];
  isAmenitiesLoading: boolean = true;
  formatBudget = formatBudget;
  budgetValidation: boolean = true;
  globalSettingsData: any;
  canViewOwner: boolean = false;
  isUnassignPropertySelected: boolean;
  allUserList: any;
  allUserListIsLoading: boolean = true;
  currency: any[] = [];
  propertyCurrency: any[] = [];
  isGlobalSettingsLoading: boolean = true;
  isBulkDeleting: boolean = false;
  canBulkReassign: boolean = false;
  canBulkDelete: boolean = false;
  canBulkShare: boolean = false;
  canBulkRestore: boolean = false;
  canPermanentDelete: boolean = false;
  canList: boolean = false;

  onFilterChanged = onFilterChanged;
  selectedTrackerOption: string;
  selectedOption: string;
  userData: any;

  securityDepositDates: any = securityDepositDates;
  lockInPeriodList: any = lockInPeriodList;
  noticePeriodList: any = noticePeriodList;
  leadsDataCount: Map<string, any> = new Map();

  constructor(
    private gridOptionsService: GridOptionsService,
    private modalService: BsModalService,
    private fb: FormBuilder,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private router: Router,
    private shareDataService: ShareDataService,
    public trackingService: TrackingService
  ) {
    super();
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    if (this.isViewAllProperties) {
      this.gridOptions.rowData = this.rowData;
    } else {
      this.gridOptions.rowData = this.archivedProperties;
    }
    this.filterForm = this.fb.group({
      propertyType: [null],
      propertySubType: [null],
      propertyCity: null,
      budget: null,
      statusOfProperty: null,
    });
    this.metaTitle.setTitle('CRM | Properties');
    this.headerTitle.setLangTitle('SIDEBAR.manage-properties');
  }

  ngOnInit(): void {
    this.router.events
      .pipe(filter((event: any) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.currentPath = this.router.url;
      });
    this.currentPath = this.router.url;
    this.selectedPageSize = 10;
    this._store.dispatch(new FetchProjectList());
    this._store.dispatch(new FetchAreaUnitList());
    this._store.dispatch(new FetchLocationList());
    this._store.dispatch(new FetchUsersListForReassignment());
    this._store.dispatch(new FetchAdminsAndReportees());

    if (!this.allPropertySubTypes?.length) {
      this.propertyTypeList?.map((type: any) => {
        this.allPropertySubTypes = this.allPropertySubTypes.concat(
          type.childTypes
        );
      });
    }
    this.propertySubTypes = [...this.allPropertySubTypes];
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
        this.currentDate = changeCalendar(
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isGlobalSettingsLoading = isLoading;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.defaultCurrency =
          data.countries && data.countries.length > 0
            ? data.countries[0].defaultCurrency
            : null;
        this.currency =
          data.countries && data.countries.length > 0
            ? data.countries[0].currencies.map((cur: any) => cur.symbol)
            : null;
        this.globalSettingsData = data;
      });

    this._store
      .select(getPropertiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isPropertyListLoading = isLoading;
      });

    this._store
      .select(getProperties)
      .pipe(takeUntil(this.stopper))
      .subscribe((item: any) => {
        if (item?.properties) {
          this.rowData = item?.properties;
          this.propertyCount = item?.propertyCount;
        }
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this._store
      .select(getAreaUnitIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isAreaUnitsLoading = isLoading;
      });

    this._store
      .select(getProjectListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isProjectsListLoading = isLoading;
      });

    this._store
      .select(getLocationListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isLocationListLoading = isLoading;
      });

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.allUserListIsLoading = isLoading;
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allUserList = data;
        // this.allUserList = data?.filter((user: any) => user.isActive);
        this.allUserList = this.allUserList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUserList = assignToSort(this.allUserList, '');
        this.gridApi?.refreshCells();
      });

    this.filterForm
      .get('propertyType')
      .valueChanges.subscribe((pType: string) => {
        this.filterForm.controls['propertySubType'].reset();
        const propType: string = this.propertyTypeList.filter(
          (baseType: Property) => baseType.id == pType
        )?.[0]?.displayName;
        return (this.propertySubTypeList = setPropertySubTypeList(
          propType,
          this.propertyTypeList
        ));
      });

    this._store
      .select(getPropertiesTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: any) => {
        this.propertiesTotalCount = count.totalCount;
      });

    this._store
      .select(getArchivedPropertiesIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: any) => {
        this.isArchivedPropertiesLoading = isLoading;
      });

    this._store
      .select(getArchivedProperties)
      .pipe(takeUntil(this.stopper))
      .subscribe((property: any) => {
        this.archivedProperties = property.items;
        this.archivedTotalCount = property.totalCount;
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this._store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.pageSize = this.filtersPayload?.pageSize;
        this.currOffset = this.filtersPayload?.pageNumber - 1;
        this.setFilterPayload();
      });

    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.pageNumber = 1;
      this.filterPropertyList();
    });

    this._store
      .select(getAllAmenities)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const allAmenities = data.flatMap((category: any) => category.amenities);
        this.amenities = allAmenities
        this.amenities?.sort((a: any, b: any) =>
          a.amenityDisplayName.localeCompare(b.amenityDisplayName)
        );
      })

    this._store
      .select(getAmenitiesLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAmenitiesLoading = isLoading;
      })

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canEdit = permissionsSet.has('Permissions.Properties.Update');
        this.canAdd = permissionsSet.has('Permissions.Properties.Create');
        this.canDelete = permissionsSet.has('Permissions.Properties.Delete');
        this.canSearch = permissionsSet.has('Permissions.Properties.Search');
        this.canAssign = permissionsSet.has('Permissions.Properties.Assign');
        this.canView = permissionsSet.has('Permissions.Properties.View');
        this.canBulkUpload = permissionsSet.has(
          'Permissions.Properties.BulkUpload'
        );
        this.canBulkReassign = permissionsSet.has(
          'Permissions.Properties.BulkReassign'
        );
        this.canBulkShare = permissionsSet.has(
          'Permissions.Properties.BulkShare'
        );
        this.canBulkDelete = permissionsSet.has(
          'Permissions.Properties.BulkDelete'
        );
        this.canBulkRestore = permissionsSet.has(
          'Permissions.Properties.BulkRestore'
        );
        this.canPermanentDelete = permissionsSet.has(
          'Permissions.Properties.BulkPermanentDelete'
        );
        this.canViewAssigned = permissionsSet.has(
          'Permissions.Properties.ViewAssigned'
        );
        this.canExport = permissionsSet.has('Permissions.Properties.Export');
        this.canViewOwner = permissionsSet.has(
          'Permissions.Properties.ViewOwnerInfo'
        );
        this.initializeGridSettings();
        this.filterPropertyList();
      });

    this._store.select(getPropertyLeadsCount).pipe(takeUntil(this.stopper)).subscribe((count: any) => {
      this.leadsDataCount.clear();
      if (count?.length) {
        count.forEach((parent: any) => {
          this.leadsDataCount.set(parent.propertyId, parent);
        });
        this.gridApi?.refreshCells();
      }
    });
  }

  assignToUserListChanged($event: any): void {
    const assignedToUsers = $event
      .filter((user: any) => user.isActive)
      .map((user: any) => user.id);
    this.bulkReassignForm.patchValue({
      assignedToUsers: assignedToUsers,
    });
  }

  setFilterPayload() {
    this.appliedFilter = {
      ...this.appliedFilter,
      pageNumber: this.filtersPayload?.pageNumber,
      pageSize: this.filtersPayload?.pageSize,
      dateType: PropertyDateType[this.filtersPayload?.dateType],
      date: [
        patchTimeZoneDate(
          this.filtersPayload?.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        patchTimeZoneDate(
          this.filtersPayload?.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
      ],
      propertyType: this.filtersPayload?.BasePropertyTypeId,
      propertySubType: this.filtersPayload?.PropertySubTypes,
      priceRange: PropertyPriceFilter[this.filtersPayload?.PriceRange],
      locations: this.filtersPayload?.Locations,
      assignedTo: this.filtersPayload?.UserIds,
      cities: this.filtersPayload?.Cities,
      states: this.filtersPayload?.States,
      enquiredFor: EnquiryType[this.filtersPayload?.EnquiredFor],
      propertySearch: this.filtersPayload?.PropertySearch,
      bhkType: this.filtersPayload?.BHKTypes,
      BHKs: this.filtersPayload?.BHKs,
      projects: this.filtersPayload?.Projects,
      furnishStatus: this.filtersPayload?.FurnishStatuses,
      noOfFloors: this.filtersPayload?.noOfFloors,
      noOfBathrooms: this.filtersPayload?.NoOfBathrooms,
      noOfLivingrooms: this.filtersPayload?.NoOfLivingrooms,
      noOfBedrooms: this.filtersPayload?.NoOfBedrooms,
      noOfUtilites: this.filtersPayload?.NoOfUtilites,
      noOfKitchens: this.filtersPayload?.NoOfKitchens,
      noOfBalconies: this.filtersPayload?.NoOfBalconies,
      facing: Facing[this.filtersPayload?.Facing],
      title: this.filtersPayload?.PropertyTitle,
      SerialNo: this.filtersPayload?.SerialNo,
      ownerDetails: this.filtersPayload?.OwnerNames,
      FromPossesionDate: patchTimeZoneDate(
        this.filtersPayload?.FromPossesionDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToPossesionDate: patchTimeZoneDate(
        this.filtersPayload?.ToPossesionDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      minPrice: this.filtersPayload?.MinPrice || null,
      maxPrice: this.filtersPayload?.MaxPrice || null,
      currency: this.filtersPayload?.Currency || this.defaultCurrency,
      amenities: this.filtersPayload?.Amenities,
      MinPropertyArea: this.filtersPayload?.['PropertySize.MinPropertyArea'],
      MaxPropertyArea: this.filtersPayload?.['PropertySize.MaxPropertyArea'],
      PropertyAreaUnitId: this.filtersPayload?.['PropertySize.PropertyAreaUnitId'],
      MinCarpetArea: this.filtersPayload?.['PropertySize.MinCarpetArea'],
      MaxCarpetArea: this.filtersPayload?.['PropertySize.MaxCarpetArea'],
      carpetAreaId: this.filtersPayload?.['PropertySize.CarpetAreaId'],
      MinBuildUpArea: this.filtersPayload?.['PropertySize.MinBuildUpArea'],
      MaxBuildUpArea: this.filtersPayload?.['PropertySize.MaxBuildUpArea'],
      buildUpAreaId: this.filtersPayload?.['PropertySize.BuildUpAreaId'],
      MinSaleableArea: this.filtersPayload?.['PropertySize.MinSaleableArea'],
      MaxSaleableArea: this.filtersPayload?.['PropertySize.MaxSaleableArea'],
      saleableAreaId: this.filtersPayload?.['PropertySize.SaleableAreaId'],
      PropertyStatus: this.filtersPayload?.PropertyStatus,
      MinLeadCount: this.filtersPayload?.MinLeadCount,
      MaxLeadCount: this.filtersPayload?.MaxLeadCount,
      MinProspectCount: this.filtersPayload?.MinProspectCount,
      MaxProspectCount: this.filtersPayload?.MaxProspectCount,
      PossesionType: this.filtersPayload?.PossesionType,
    };
  }

  reset(keepNonAdvancedFilters: boolean = false) {
    this.appliedFilter = {
      dateType: 'All',
      date: '',
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    if (keepNonAdvancedFilters) {
      this.appliedFilter = {
        ...this.appliedFilter,
        dateType: PropertyDateType[this.filtersPayload?.dateType],
        date: [
          patchTimeZoneDate(
            this.filtersPayload?.fromDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
          patchTimeZoneDate(
            this.filtersPayload?.toDate,
            this.userData?.timeZoneInfo?.baseUTcOffset
          ),
        ],
        propertyType: this.filtersPayload?.BasePropertyTypeId,
      };
    }
    this.filterPropertyList();
  }

  currentVisibility(id: any) {
    this.appliedFilter.pageNumber = 1;
    this.appliedFilter.propertyType = id;
    this.updatePropertySubType();
    this.filterPropertyList();
    let displayName = this.propertyTypeList.find(
      (item: any) => item.id === id
    )?.displayName;
    this.trackingService.trackFeature(
      `Web.Property.Filter.${!displayName ? 'All' : displayName}.Click`
    );
  }

  applyAdvancedFilter() {
    if (!this.budgetValidation) return;
    this.filterPropertyList();
    this.modalService.hide();
  }

  filterPropertyList() {
    if (!this.budgetValidation) return;
    if (
      this.appliedFilter?.propertySubType?.length ||
      this.appliedFilter?.MinCarpetArea ||
      this.appliedFilter?.MaxCarpetArea ||
      this.appliedFilter?.MinBuildUpArea ||
      this.appliedFilter?.MaxBuildUpArea ||
      this.appliedFilter?.MinSaleableArea ||
      this.appliedFilter?.MaxSaleableArea ||
      this.appliedFilter?.MinPropertyArea ||
      this.appliedFilter?.MaxPropertyArea ||
      this.appliedFilter?.priceRange?.length ||
      this.appliedFilter?.locations?.length ||
      this.appliedFilter?.assignedTo?.length ||
      this.appliedFilter?.cities?.length ||
      this.appliedFilter?.states?.length ||
      this.appliedFilter?.enquiredFor?.length ||
      this.appliedFilter?.bhkType?.length ||
      this.appliedFilter?.BHKs?.length ||
      this.appliedFilter?.projects?.length ||
      this.appliedFilter?.furnishStatus?.length ||
      this.appliedFilter?.noOfFloors?.length ||
      this.appliedFilter?.noOfBathrooms?.length ||
      this.appliedFilter?.noOfLivingrooms?.length ||
      this.appliedFilter?.noOfBedrooms?.length ||
      this.appliedFilter?.noOfUtilites?.length ||
      this.appliedFilter?.noOfKitchens?.length ||
      this.appliedFilter?.noOfBalconies?.length ||
      this.appliedFilter?.facing ||
      this.appliedFilter?.ownerDetails?.length ||
      this.appliedFilter?.title?.length ||
      this.appliedFilter?.SerialNo?.length ||
      this.appliedFilter?.possessionDate?.[0] ||
      this.appliedFilter?.maxPrice ||
      this.appliedFilter?.minPrice ||
      this.appliedFilter?.amenities?.length ||
      typeof this.appliedFilter?.PropertyStatus === 'number' ||
      this.appliedFilter?.MinLeadCount ||
      this.appliedFilter?.MaxLeadCount ||
      this.appliedFilter?.MinProspectCount ||
      this.appliedFilter?.MaxProspectCount ||
      this.appliedFilter?.PossesionType ||
      this.appliedFilter?.noOfParking ||
      this.appliedFilter?.countries?.length
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }
    this.filtersPayload = {
      ...this.filtersPayload,
      permission: this.canView ? 2 : this.canViewAssigned ? 1 : 0,
      pageNumber: this.appliedFilter?.pageNumber,
      pageSize: this.pageSize,
      dateType: PropertyDateType[this.appliedFilter?.dateType],
      fromDate: setTimeZoneDate(
        this.appliedFilter?.date[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      toDate: setTimeZoneDate(
        this.appliedFilter?.date[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      BasePropertyTypeId: this.appliedFilter.propertyType,
      PropertySubTypes: this.appliedFilter.propertySubType,
      PriceRange: PropertyPriceFilter[this.appliedFilter.priceRange],
      Locations: this.appliedFilter.locations,
      UserIds: this.appliedFilter.assignedTo,
      Cities: this.appliedFilter.cities,
      States: this.appliedFilter.states,
      BHKTypes: this.appliedFilter.bhkType,
      BHKs: this.appliedFilter.BHKs,
      Projects: this.appliedFilter.projects,
      PropertyTitle: this.appliedFilter.title,
      SerialNo: this.appliedFilter.SerialNo,
      OwnerNames: this.appliedFilter.ownerDetails,
      Facing:
        Facing[
        this.appliedFilter?.facing?.displayName || this.appliedFilter?.facing
        ],
      FurnishStatuses: this.appliedFilter.furnishStatus,
      noOfFloors: this.appliedFilter.noOfFloors
        ? this.appliedFilter.noOfFloors.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      NoOfBathrooms: this.appliedFilter.noOfBathrooms
        ? this.appliedFilter.noOfBathrooms.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      NoOfLivingrooms: this.appliedFilter.noOfLivingrooms
        ? this.appliedFilter.noOfLivingrooms.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      NoOfBedrooms: this.appliedFilter.noOfBedrooms
        ? this.appliedFilter.noOfBedrooms.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      NoOfUtilites: this.appliedFilter.noOfUtilites
        ? this.appliedFilter.noOfUtilites.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      NoOfKitchens: this.appliedFilter.noOfKitchens
        ? this.appliedFilter.noOfKitchens.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      NoOfBalconies: this.appliedFilter.noOfBalconies
        ? this.appliedFilter.noOfBalconies.map(
          (item: { value: any }) => item?.value || item
        )
        : [],
      EnquiredFor: EnquiryType[this.appliedFilter.enquiredFor],
      PropertySearch: this.searchTerm,
      'PropertySize.MinPropertyArea': this.appliedFilter?.MinPropertyArea,
      'PropertySize.MaxPropertyArea': this.appliedFilter?.MaxPropertyArea,
      'PropertySize.PropertyAreaUnitId': this.appliedFilter?.PropertyAreaUnitId,
      'PropertySize.MinCarpetArea': this.appliedFilter?.MinCarpetArea,
      'PropertySize.MaxCarpetArea': this.appliedFilter?.MaxCarpetArea,
      'PropertySize.CarpetAreaId': this.appliedFilter?.carpetAreaId,
      'PropertySize.MinBuildUpArea': this.appliedFilter?.MinBuildUpArea,
      'PropertySize.MaxBuildUpArea': this.appliedFilter?.MaxBuildUpArea,
      'PropertySize.BuildUpAreaId': this.appliedFilter?.buildUpAreaId,
      'PropertySize.MinSaleableArea': this.appliedFilter?.MinSaleableArea,
      'PropertySize.MaxSaleableArea': this.appliedFilter?.MaxSaleableArea,
      'PropertySize.SaleableAreaId': this.appliedFilter?.saleableAreaId,
      MinPrice: this.appliedFilter?.minPrice,
      MaxPrice: this.appliedFilter?.maxPrice,
      Currency:
        this.appliedFilter?.minPrice || this.appliedFilter?.maxPrice
          ? this.appliedFilter.currency || this.defaultCurrency
          : null,
      Amenities: this.appliedFilter?.amenities,
      FromPossesionDate: setTimeZoneDate(
        this.appliedFilter.FromPossesionDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToPossesionDate: setTimeZoneDate(
        this.appliedFilter.ToPossesionDate,
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      PropertyStatus: this.appliedFilter?.PropertyStatus,
      MinLeadCount: this.appliedFilter?.MinLeadCount,
      MaxLeadCount: this.appliedFilter?.MaxLeadCount,
      MinProspectCount: this.appliedFilter?.MinProspectCount,
      MaxProspectCount: this.appliedFilter?.MaxProspectCount,
      PossesionType: this.appliedFilter?.PossesionType,
      Parking: this.appliedFilter?.noOfParking,
      Countries: this.appliedFilter?.countries,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchPropertyList(true));
  }

  getCarpetArea(carpetArea: number) {
    return (
      carpetArea +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.carpetAreaId) || '')
    );
  }

  getPropertyArea(propertyArea: number) {
    return (
      propertyArea +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.PropertyAreaUnitId) || '')
    );
  }

  getBuildUpArea(buildUpArea: number) {
    return (
      buildUpArea +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.buildUpAreaId) || '')
    );
  }

  getSaleableArea(saleableArea: number) {
    return (
      saleableArea +
      ' ' +
      (this.getAreaUnit(this.appliedFilter?.saleableAreaId) || '')
    );
  }

  getAmenity(value: string) {
    return this.amenities.filter((amenity: any) => amenity?.id == value)?.[0]
      ?.amenityDisplayName;
  }

  getPossessionDate(value: any) {
    if (typeof value === 'string') {
      const date = new Date(value);
      if (isNaN(date.getTime())) return '';
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}-${month}-${year}`;
    } else {
      this.Possession = setTimeZoneDate(
        value,
        this.userData?.timeZoneInfo?.baseUTcOffset
      );
      return getTimeZoneDate(
        this.Possession,
        this.userData?.timeZoneInfo?.baseUTcOffset,
        'dayMonthYear'
      );
    }
  }

  getAreaUnit(id: string) {
    let areaUnit = '';
    this.areaSizeUnits?.forEach((type: any) => {
      if (type.id === id) areaUnit = type.unit;
    });
    return areaUnit;
  }

  fetchArchivedProperty() {
    this.archivedPayload = {
      ...this.archivedPayload,
      pageNumber: 1,
      pageSize: this.pageSize,
      PropertySearch: this.searchTerm,
    };
    this._store.dispatch(new FetchArchivedPropertyList(this.archivedPayload));
  }

  updatePropertySubType() {
    if (this.appliedFilter?.propertyType?.length) {
      this.propertySubTypes = this.allPropertySubTypes.filter((subTypes: any) =>
        this.appliedFilter.propertyType.includes(subTypes.baseId)
      );
    } else {
      this.propertySubTypes = [];
      this.propertySubTypes = [...this.allPropertySubTypes];
    }
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = [
      'assignedTo',
      'locations',
      'projects',
      'states',
      'cities',
      'ownerDetails',
    ];
    if (
      [
        'pageSize',
        'pageNumber',
        'date',
        'dateType',
        'carpetAreaId',
        'PropertyAreaUnitId',
        'buildUpAreaId',
        'saleableAreaId',
        'possessionDate',
        'propertyType',
        'FromPossessionDate',
        'ToPossessionDate',
        'Possession',
      ].includes(key) ||
      values?.length === 0 ||
      (key == 'maxPrice' && this.appliedFilter?.maxPrice == 0)
    )
      return [];
    else if (key === 'currency') return null;
    else if ((key === 'maxPrice' || key === 'minPrice') && values)
      return [' ' + this.filtersPayload?.Currency + values];
    else if (key === 'noOfFloors' && values) {
      return values?.toString()?.split(',').map((value: any) =>
        (value === 0 || value === '0') ? 'Ground Floor' : value
      );
    }
    else if (allowedKeys.includes(key)) return values;
    else if ((key === 'title' || key === 'SerialNo') && values) return [values];
    return values?.toString()?.split(',');
  }

  onRemoveFilter(key: string, value: string) {
    if (
      [
        'priceRange',
        'enquiredFor',
        'propertyType',
        'buildUpArea',
        'saleableArea',
        'PropertyStatus',
        'MaxCarpetArea',
        'MinCarpetArea',
        'MaxBuildUpArea',
        'MinBuildUpArea',
        'MaxSaleableArea',
        'MinSaleableArea',
        'MaxPropertyArea',
        'MinPropertyArea',
        'MinLeadCount',
        'MaxLeadCount',
        'MinProspectCount',
        'MaxProspectCount',
      ].includes(key)
    ) {
      this.appliedFilter[key] = null;
      key === 'MaxPropertyArea' || key === 'MinPropertyArea'
        ? (this.appliedFilter['MaxPropertyArea'] = null,
          this.appliedFilter['MinPropertyArea'] = null,
          this.appliedFilter['PropertyAreaUnitId'] = null)
        : key === 'MinCarpetArea' || key === 'MaxCarpetArea'
          ? (this.appliedFilter['MinCarpetArea'] = null,
            this.appliedFilter['MaxCarpetArea'] = null,
            this.appliedFilter['carpetAreaId'] = null)
          : key === 'MinBuildUpArea' || key === 'MaxBuildUpArea'
            ? (this.appliedFilter['MinBuildUpArea'] = null,
              this.appliedFilter['MaxBuildUpArea'] = null,
              this.appliedFilter['buildUpAreaId'] = null)
            : key === 'MaxSaleableArea' || key === 'MinSaleableArea'
              ? (this.appliedFilter['MaxSaleableArea'] = null,
                this.appliedFilter['MinSaleableArea'] = null,
                this.appliedFilter['saleableAreaId'] = null)
              : null;
    } else if (['PossesionType', 'FromPossesionDate', 'ToPossesionDate'].includes(key)) {
      this.appliedFilter = {
        ...this.appliedFilter,
        PossesionType: null,
        FromPossesionDate: null,
        ToPossesionDate: null
      };
      this.filtersPayload = {
        ...this.filtersPayload,
        PossesionType: null,
        FromPossesionDate: null,
        ToPossesionDate: null
      };
    } else if (typeof this.appliedFilter[key] === 'string') {
      this.appliedFilter[key] = null;
    } else if (key == 'maxPrice' || key == 'minPrice') {
      this.appliedFilter[key] = null;
    } else if (key == 'PropertyStatus') {
      this.appliedFilter['PropertyStatus'] = null;
      this.filtersPayload['PropertyStatus'] = null;
    } else if (key === 'noOfFloors') {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    } else {
      this.appliedFilter[key] = this.appliedFilter[key]?.filter(
        (item: any) => item !== value
      );
    }
    this.filterPropertyList();
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: 'All',
      date: '',
    };
    this.filterPropertyList();
  }

  getPropertyTypeName(id: string) {
    let propertyType = '';
    this.propertyTypeList?.forEach((type: any) => {
      if (type.id === id) propertyType = type.displayName;
    });
    return propertyType;
  }

  getAssignedToName(id: string) {
    let assignedTo = '';
    this.allUserList?.forEach((assign: any) => {
      if (assign.id === id) assignedTo = assign.fullName;
    });
    return assignedTo;
  }

  getPropertySubTypeName(id: string) {
    let propertySubType = '';
    this.allPropertySubTypes?.forEach((type: any) => {
      if (type.id === id) propertySubType = type.displayName;
    });
    return propertySubType;
  }

  getPossessionTypeDisplayName(value: any): string {
    const numValue = Number(value);
    if (!isNaN(numValue) && PossessionType[numValue]) {
      return PossessionType[numValue];
    }
    return String(value);
  }

  assignCount() {
    this.pageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this.archivedPayload = {
      ...this.archivedPayload,
      pageNumber: 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchPropertyList(true));
    this._store.dispatch(new FetchArchivedPropertyList(this.archivedPayload));
    this.currOffset = 0;
    this.currArchiveOffset = 0;
    this.trackingService.trackFeature(
      `Web.Property.Option.${this.pageSize}.Click`
    );
  }

  onPageChange(e: number) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this._store.dispatch(new FetchPropertyList(true));
  }

  onPageChangeArchived(e: number) {
    this.currArchiveOffset = e;
    this.archivedPayload = {
      ...this.archivedPayload,
      pageNumber: e + 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new FetchArchivedPropertyList(this.archivedPayload));
  }

  initializeGridSettings() {
    this.trackingService.trackFeature(
      `Web.Property.Menu.${this.isViewAllProperties ? 'All' : 'Deleted'}.Click`
    );
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    const currentDate = moment(new Date().toISOString());

    this.gridOptions.getRowClass = (params: any) => {
      if (params.data) {
        if (params.data.status === 1 ||
          params.data.status === 'Sold') {
          return 'bg-red-410';
        }
      }
      return '';
    };

    this.gridOptions.columnDefs = [
      {
        headerName: 'Status',
        field: 'Status',
        filter: false,
        valueGetter: (params: any) => [params.data?.status],
        maxWidth: 75,
        cellRenderer: PropertyStatusComponent,
      },
      {
        headerName: 'Property Title',
        field: 'Property Title',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [params.data?.title],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        hide: true,
        minWidth: 195,
        field: 'Assigned To',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data.assignedTo
            ? params.data.assignedTo?.map(
              (id: any) =>
                getAssignedToDetails(id, this.allUserList, true) || '--'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value[0]}</p>`;
        },
      },
      {
        headerName: 'Leads Count',
        field: 'Leads Count',
        minWidth: 100,
        maxWidth: 100,
        filter: false,
        valueGetter: (params: any) => {
          if (!params.data?.id) return ['--', ''];
          const leadData = this.leadsDataCount.get(params.data.id);
          return [
            leadData?.leadCount ?? '--',
            params.data?.title
          ];
        },
        cellRenderer: (params: any) => {
          const leadCount = params.value[0];
          const propertyName = params.value[1];
          return `<p>
            <a ${leadCount !== '--'
              ? `href="leads/manage-leads?isNavigatedFromProperties=true&Properties=${encodeURIComponent(
                JSON.stringify([propertyName])
              )}"`
              : ''
            } onclick="return false;">${leadCount}</a>
          </p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          if (!event.data?.id || event.value[0] === '--') return;
          const propertyName = event.data?.title;
          const isCtrlClick = event?.event?.ctrlKey;

          if (isCtrlClick) {
            window.open(
              `leads/manage-leads?isNavigatedFromProperties=true&Properties=${encodeURIComponent(
                JSON.stringify([propertyName])
              )}`,
              '_blank'
            );
          } else {
            this.router.navigate(['leads', 'manage-leads'], {
              queryParams: {
                Properties: JSON.stringify([propertyName]),
                isNavigatedFromProperties: true,
              },
            });
          }
        }
      },
      {
        headerName: 'Data Count',
        field: 'Data Count',
        minWidth: 100,
        maxWidth: 100,
        filter: false,
        valueGetter: (params: any) => {
          if (!params.data?.id) return ['--', ''];
          const leadData = this.leadsDataCount.get(params.data.id);
          return [
            leadData?.prospectCount ?? '--',
            params.data?.title
          ];
        },
        cellRenderer: (params: any) => {
          const dataCount = params.value[0];
          const propertyName = params.value[1];
          return `<p>
            <a ${dataCount !== '--'
              ? `href="data/manage-data?isNavigatedFromProperties=true&Properties=${encodeURIComponent(
                JSON.stringify([propertyName])
              )}"`
              : ''
            } onclick="return false;">${dataCount}</a>
          </p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          if (!event.data?.id || event.value[0] === '--') return;
          const propertyName = event.data?.title;
          const isCtrlClick = event?.event?.ctrlKey;

          if (isCtrlClick) {
            window.open(
              `data/manage-data?isNavigatedFromProperties=true&Properties=${encodeURIComponent(
                propertyName
              )}`,
              '_blank'
            );
          } else {
            this.router.navigate(['data', 'manage-data'], {
              queryParams: {
                Properties: propertyName,
                isNavigatedFromProperties: true,
              },
            });
          }
        }
      },
      {
        headerName: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        field: !this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Site Visit Done' : 'Referral Taken',
        filter: false,
        hide: true,
        valueGetter: (params: any) => {
          if (!params.data?.id) return ['--', ''];
          const leadData = this.leadsDataCount.get(params.data.id);
          return [
            leadData?.siteVisitDoneCount,
            leadData?.siteVisitDoneUniqueCount,
            params.data?.title
          ];
        },
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
      },
      {
        headerName: 'Meeting Done',
        field: 'Meeting Done',
        filter: false,
        hide: true,
        valueGetter: (params: any) => {
          if (!params.data?.id) return ['--', ''];
          const leadData = this.leadsDataCount.get(params.data.id);
          return [
            leadData?.meetingDoneCount,
            leadData?.meetingDoneUniqueCount,
            params.data?.title
          ];
        },
        minWidth: 120,
        cellRenderer: (params: any) => {
          return `<a><p>${params.value[0] ? params.value[0] : '--'}</p>
          <p class="text-truncate"><span class="text-dark-gray">unique count: </span><span class="fw-600">${params.value[1] ? params.value[1] : '--'
            }<span></p></a>`;
        },
      },
      {
        headerName: 'Property Type',
        field: 'Property Type',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => {
          const displayName = params.data?.propertyType?.displayName || '';
          const childType =
            params.data?.propertyType?.childType?.displayName || '';
          if (displayName === 'Residential') {
            const bhkType = params.data?.bhkType || '';
            const noOfBHK = params.data?.noOfBHK || '';
            return [displayName, childType, bhkType, noOfBHK];
          }
          return [displayName, childType];
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}${params.value[0] && params.value[1] ? ',' : ''
            } ${params.value[1]} </p>
          <p class="text-truncate-1 break-all">${params.value[2] > 0 ? BHK_TYPE[params.value[2] - 1] : ''
            }${params.value[2] && params.value[3] ? ',' : ''} ${params.value[3] ? getBHKDisplayString(params.value[3]) : ''
            } </p>`;
        },
      },
      {
        headerName: 'Property Description',
        field: 'Property Description',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.monetaryInfo?.expectedPrice
            ? formatBudget(
              params.data?.monetaryInfo?.expectedPrice,
              params.data?.monetaryInfo?.currency || this.defaultCurrency
            )
            : '',
          params.data?.dimension?.area,
          params.data?.dimension?.unit,
          params?.data?.taxationMode,
        ],
        cellRenderer: (params: any) => {
          if (!params.value[0]) return '';
          const gstLabel = params.value[3] == '1' ? 'Excl. GST' : 'Incl. GST';
          return `<p>Budget: ${params.value[0]} (${gstLabel})</p>
                  <p>${params.value[1] ? params.value[1] : ''}
                  ${params.value[2] ? params.value[2] : ''}</p>`;
        },
      },
      {
        headerName: 'Maintenance Cost',
        field: 'Maintenance Cost',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.monetaryInfo?.maintenanceCost
            ? formatBudget(
              params.data?.monetaryInfo?.maintenanceCost,
              params.data?.monetaryInfo?.currency || this.defaultCurrency
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p> ${params.value[0] ? params.value[0] : ''}</p>`;
        },
      },
      {
        headerName: 'Security Deposit',
        field: 'Security Deposit',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => {
          const securityDepositValue = params.data?.securityDeposit;
          const foundDeposit = securityDepositDates.find(
            (deposit) => deposit.value === securityDepositValue
          );
          return [foundDeposit ? foundDeposit.label : ''];
        },
        cellRenderer: (params: any) => {
          const depositLabel = params.value[0] ? params.value[0] : '';
          return `<p title="${depositLabel}">${depositLabel}</p>`;
        },
      },
      {
        headerName: 'Deposit/Security Amount',
        field: 'Deposit/Security Amount',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.monetaryInfo?.depositAmount
            ? formatBudget(
              params.data?.monetaryInfo?.depositAmount,
              params.data?.monetaryInfo?.currency || this.defaultCurrency
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          const depositAmount = params.value[0] ? params.value[0] : '';
          return `<p title="${depositAmount}">${depositAmount}</p>`;
        },
      },
      {
        headerName: 'Common Area Charges',
        field: 'Common Area Charges',
        hide: true,
        valueGetter: (params: any) => {
          const commonAreaCharges = params.data?.dimension?.commonAreaCharges;
          const icons = params.data?.dimension?.currency
            ? formatBudget(
              params.data?.dimension?.commonAreaCharges,
              params.data?.dimension?.currency || this.defaultCurrency
            )
            : '';
          const commonAreaChargesId =
            params.data?.dimension?.commonAreaChargesId;
          const unit = commonAreaChargesId
            ? this.areaSizeUnits.find((unit) => unit.id === commonAreaChargesId)
              ?.unit
            : '';
          return commonAreaCharges ? [icons, unit] : ['', ''];
        },
        cellRenderer: (params: any) => {
          const charges = params.value[0];
          const unit = params.value[1];
          const content = charges
            ? `${charges} ${unit ? `per ${unit}` : ''}`
            : '';
          return charges
            ? `<p class="text-normal" title="${content}">
                ${charges}
                ${unit ? `<span class="ml-2">per ${unit}</span>` : ''}
              </p>`
            : '';
        },
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Notice Period',
        field: 'Notice Period',
        cellClass: 'cursor-pointer',
        minWidth: 105,
        hide: true,
        valueGetter: (params: any) => {
          return params?.data?.noticePeriod
            ? noticePeriodList.find(
              (item: any) => item.value === params?.data?.noticePeriod
            )?.label
            : '';
        },
        cellRenderer: (params: any) => {
          const noticeValue = params.value === 'None' ? '' : params.value;
          return `<p title="${noticeValue}">${noticeValue}</p>`;
        },
      },
      {
        headerName: 'Lock In Period',
        field: 'Lock In Period',
        cellClass: 'cursor-pointer',
        minWidth: 105,
        hide: true,
        valueGetter: (params: any) => {
          return params?.data?.lockInPeriod
            ? lockInPeriodList.find(
              (item: any) => item.value === params?.data?.lockInPeriod
            )?.label
            : '';
        },
        cellRenderer: (params: any) => {
          const lockInValue = params.value === 'None' ? '' : params.value;
          return `<p title="${lockInValue}">${lockInValue}</p>`;
        },
      },
      {
        headerName: 'Location',
        field: 'Location',
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          getLocationDetailsByObj(params.data?.address),
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'City',
        field: 'City',
        hide: true,
        valueGetter: (params: any) => {
          const cityArray = params.data?.address?.city;
          return [cityArray];
        },
        cellRenderer: (params: any) => {
          const fullCity = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCity}">${fullCity}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'State',
        field: 'State',
        hide: true,
        valueGetter: (params: any) => {
          const stateArray = params.data?.address?.state;
          return [stateArray];
        },
        cellRenderer: (params: any) => {
          const fullState = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullState}">${fullState}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Country',
        field: 'Country',
        hide: true,
        valueGetter: (params: any) => {
          const countryArray = params.data?.address?.country;
          return [countryArray];
        },
        cellRenderer: (params: any) => {
          const fullCountry = params.value ? params.value : '';
          return `<p class="text-truncate-1 break-all" title="${fullCountry}">${fullCountry}</p>`;
        },
        cellClass: 'cursor-pointer',
        sortable: true,
        unSortIcon: true,
      },
      {
        headerName: 'Tenant POC Info',
        field: 'Tenant POC Info',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params?.data?.propertyType?.childType?.displayName ===
          'Co Working Office Space',
          params.data?.tenantContactInfo?.name,
          params.data?.tenantContactInfo?.phone,
          params.data?.tenantContactInfo?.designation,
        ],
        cellRenderer: (params: any) => {
          const isCoWorkingOfficeSpace = params?.value[0];
          const name = params?.value[1];
          const phone = params?.value[2];
          const designation = isCoWorkingOfficeSpace ? '' : params?.value[3];
          const infoArray = [name, phone];
          if (!isCoWorkingOfficeSpace && designation) {
            infoArray.push(designation);
          }
          const infoString = infoArray.filter(Boolean).join(', ');
          return `<p class="gap-2 align-center text-truncate">
            <span class="text-truncate-1 break-all" title="${infoString}">${infoString}</span>
          </p>`;
        },
        width: 200,
      },
      {
        headerName: 'Co Working Operator',
        field: 'Co Working Operator',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.coWorkingOperatorName,
          params.data?.coWorkingOperator,
          params.data?.coWorkingOperatorPhone,
        ],
        cellRenderer: (params: any) => {
          const coWorkingOperatorName = params?.value[0] || '';
          const coWorkingOperator = params?.value[1] || '';
          const coWorkingOperatorPhone = params?.value[2] || '';
          return `
            <p class="gap-2 align-center text-truncate" title="${coWorkingOperatorName}">
              ${coWorkingOperatorName
              ? `<span class="text-truncate-1 break-all">${coWorkingOperatorName}</span>`
              : ''
            }
            </p>
            <span class="align-center">
              <p class="gap-2 align-center" title="${coWorkingOperator}">
                ${coWorkingOperator
              ? `<span class="text-truncate-1 break-all">${coWorkingOperator}</span>`
              : ''
            }
              </p>
              <p class="gap-2 align-center ml-6" title="${coWorkingOperatorPhone}">
                ${coWorkingOperatorPhone
              ? `<span class="text-truncate-1 break-all">${coWorkingOperatorPhone}</span>`
              : ''
            }
              </p>
            </span>
          `;
        },
        width: 200,
      },
      {
        headerName: 'Project',
        field: 'Project',
        hide: true,
        valueGetter: (params: any) => [params.data.project],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Share Count',
        field: 'Share Count',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.whatsAppShareCount,
          params.data?.emailShareCount,
          params.data?.smsShareCount,
          params.data?.shareCount,
          params.data?.callShareCount,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-sm text-truncate-2">
          ${params.value[3] ? 'Total: ' + params.value[3] + '; ' : ''}
          ${params.value[0] ? 'WhatsApp: ' + params.value[0] + '; ' : ''}
          ${params.value[1] ? 'Email: ' + params.value[1] + '; ' : ''}
          ${params.value[2] ? 'SMS: ' + params.value[2] + '; ' : ''}
          ${params.value[4] ? 'Call: ' + params.value[4] + '; ' : ''}
          </p>`;
        },
      },
      {
        headerName: 'Maximum Occupancy',
        field: 'Maximum Occupancy',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => {
          const maximumOccupantsAttribute = params.data?.attributes?.find(
            (attribute: any) => attribute?.attributeName === 'maximumOccupants'
          );
          return maximumOccupantsAttribute?.value || '--';
        },
        cellRenderer: (params: any) => {
          const occupancyValue = params.value;
          return `<p class='text-truncate-2 break-all' title='${occupancyValue}'>${occupancyValue}</p>`;
        },
      },
      {
        headerName: 'Total Floors',
        field: 'Total Floors',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => {
          const totalFloorsAttribute = params.data?.attributes?.find(
            (attr: any) => attr?.attributeName === 'numberOfFloors'
          );
          return totalFloorsAttribute?.value || '--';
        },
        cellRenderer: (params: any) => {
          return `<p class='text-truncate-2 break-all' title='${params.value}'>${params.value}</p>`;
        },
      },
      {
        headerName: 'Floor Number',
        field: 'Floor Number',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => {
          const floorNumberAttribute = params.data?.attributes?.find(
            (attr: any) => attr?.attributeName === 'floorNumber'
          );
          return floorNumberAttribute?.value || '--';
        },
        cellRenderer: (params: any) => {
          return `<p class='text-truncate-2 break-all' title='${params.value}'>${params.value}</p>`;
        },
      },
      {
        headerName: 'Number of Floor occupied',
        field: 'Number of Floor occupied',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => {
          return params.data?.noOfFloorsOccupied || [];
        },
        cellRenderer: (params: any) => {
          const floorNumbers = params.value.join(', ');
          return `<p class='text-truncate-2 break-all' title='${floorNumbers}'>${floorNumbers}</p>`;
        },
      },
      {
        headerName: 'Furnish Status',
        field: 'Furnish Status',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          FurnishStatus[params.data.furnishStatus],
        ],
        cellRenderer: (params: any) => {
          return `<p>${params.value == 'Unknown' ? '' : [params.value]}</p>`;
        },
      },
      {
        headerName: 'Facing',
        field: 'Facing',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [Facing[params.data.facing]],
        cellRenderer: (params: any) => {
          return `<p>${params.value == 'Unknown' ? '' : [params.value]}</p>`;
        },
      },
      {
        headerName: 'Serial No',
        field: 'Serial No',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.serialNo],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params.value}</p>`;
        },
      },
      {
        headerName: 'About Property',
        field: 'About Property',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.aboutProperty],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Carpet Area',
        field: 'Carpet Area',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.dimension?.carpetArea
            ? params.data?.dimension?.carpetArea
            : '--',
          params.data?.dimension?.carpetArea
            ? getAreaUnit(
              params.data?.dimension?.carpetAreaId,
              this.areaSizeUnits
            )?.unit
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p> ${params.value[0]}  ${params.value[1]}</p>`;
        },
      },
      {
        headerName: 'Built-Up Area',
        field: 'Built-Up Area',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.dimension?.buildUpArea
            ? params.data?.dimension?.buildUpArea
            : '--',
          params.data?.dimension?.buildUpArea
            ? getAreaUnit(
              params.data?.dimension?.buildUpAreaId,
              this.areaSizeUnits
            )?.unit
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p> ${params.value[0]}  ${params.value[1]}</p>`;
        },
      },
      {
        headerName: 'Saleable Area',
        field: 'Saleable Area',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.dimension?.saleableArea
            ? params.data?.dimension?.saleableArea
            : '--',
          params.data?.dimension?.saleableArea
            ? getAreaUnit(
              params.data?.dimension?.saleableAreaId,
              this.areaSizeUnits
            )?.unit
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p> ${params.value[0]}  ${params.value[1]}</p>`;
        },
      },
      {
        headerName: 'Notes',
        field: 'Notes',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.notes],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params.value}</p>`;
        },
      },
      {
        headerName: 'Possession Availability',
        field: 'Possession Availability',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [params.data.possessionDate],
        cellRenderer: (params: any) => {
          const possessionDate = params.value[0];
          if (possessionDate) {
            const isReadyToMove = getTimeZoneDate(possessionDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') <= getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
            return `<p>${isReadyToMove
              ? 'Ready To Move'
              : getTimeZoneDate(possessionDate,
                this.userData?.timeZoneInfo?.baseUTcOffset,
                'dayMonthYear')
              }</p>`;
          } else {
            return '';
          }
        },
      },
      {
        headerName: 'Brokerage Amount',
        field: 'Brokerage Amount',
        cellClass: 'cursor-pointer',
        hide: true,
        valueGetter: (params: any) => [
          params.data?.monetaryInfo?.brokerage,
          params.data?.monetaryInfo?.brokerage
            ? params.data?.monetaryInfo?.brokerageCurrency
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p>
          ${params.value[1] == 'None' || params.value[1] == '%'
              ? ''
              : [params.value[1]]
            }
          ${params.value[0] ? params.value[0] : ''}
          ${params.value[1] == 'None' || params.value[1] !== '%'
              ? ''
              : [params.value[1]]
            }</p>`;
        },
      },
      {
        headerName: 'Matching Leads',
        field: 'Matching Leads',
        hide: true,
        filter: false,
        maxWidth: 110,
        minWidth: 110,
        valueGetter: (params: any) => ['Properties'],
        cellRenderer: MatchingLeadsComponent,
      },
      {
        headerName: 'Created',
        field: 'Created',
        minWidth: 200,
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.allUserList, true) ||
          '',
          params.data?.createdOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Modified',
        field: 'Modified',
        minWidth: 200,
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.allUserList,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? 'At ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'fullDateTime'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
            <p>${params.value[1]}</p>
            <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value
              ? '(' + this.userData?.timeZoneInfo?.timeZoneName + ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Possession Type',
        field: 'Possession Type',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any) => [
          params.data?.possesionType
            ? PossessionType[params.data?.possesionType]
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value[0]}</p>`;
        },
      },
    ];
    if (this.canViewOwner) {
      this.gridOptions.columnDefs.push({
        headerName: 'Owner Info',
        field: 'ownerInfo',
        hide: true,
        cellClass: 'cursor-pointer',
        valueGetter: (params: any): string => {
          const ownerDetails = params.data?.propertyOwnerDetails;
          if (Array.isArray(ownerDetails) && ownerDetails.length > 0) {
            return ownerDetails.map((owner: any) => owner.name).filter((name: string) => name).join(', ');
          }
          return '';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all" title="${params.value}">${params.value}</p>`;
        },
        width: 300,
        minWidth: 250,
      });
    }
    this.gridOptions.columnDefs.push({
      headerName: 'Actions',
      field: 'Actions',
      filter: false,
      cellRenderer: PropertiesActionGridComponent,
      maxWidth: 220,
      minWidth: 220,
      menuTabs: [],
    });
    if (
      (this.currentPath === '/properties/manage-properties' &&
        this.isViewAllProperties &&
        (this.canBulkReassign || this.canBulkDelete || this.canBulkShare)) ||
      (!this.isViewAllProperties &&
        this.currentPath === '/properties/manage-properties' &&
        (this.canBulkRestore || this.canPermanentDelete))
    ) {
      this.gridOptions.columnDefs.unshift({
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        resizable: false,
        maxWidth: 50,
      });
    }

    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    if (this.isViewAllProperties) {
      this.gridOptions.rowData = this.rowData;
    } else {
      this.gridOptions.rowData = this.archivedProperties;
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.columns = this.gridColumnApi.getColumns();
    this.columns = this.columns.map((column: any) => {
      return {
        label: column.getColDef().headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(3, this.columns.length - 1)
      .sort((a: any, b: any) => a?.label.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myColumnStateProperty'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    this.gridOptions.onColumnMoved = this.onColumnMoved;

    let columnData = localStorage
      .getItem('manage-properties-columns')
      ?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnMoved(params: any) {
    var columnState = JSON.stringify(params.columnApi.getColumnState());
    localStorage.setItem('myColumnStateProperty', columnState);
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns?.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
    this.trackingService.trackFeature(`Web.Property.Default.Column.Click`);
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-properties-columns', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem('myColumnStateProperty', JSON.stringify(columnState));
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onCellClicked(event: CellClickedEvent) {
    const userName: any = JSON.parse(
      localStorage.getItem('userDetails')
    )?.preferred_username;

    const headerName = event.colDef.headerName;
    if (
      headerName !== 'Status' &&
      headerName !== 'Matching Leads' &&
      headerName !== 'Leads Count' &&
      headerName !== 'Actions' &&
      headerName !== 'Site Visit Done' &&
      headerName !== 'Meeting Done' &&
      headerName !== 'Data Count' &&
      headerName !== undefined &&
      event.data.serialNo
    ) {
      window.open(
        `external/property-preview/${userName}/${event.data.serialNo}`,
        '_blank'
      );
    }
  }

  openAdvFiltersModal() {
    let initialState: any = {
      class: 'ip-modal-unset  top-full-modal',
      initialState: {
        applyAdvancedFilter: this.applyAdvancedFilter.bind(this),
        onClearAllFilters: this.reset.bind(this),
        appliedFilter: this.appliedFilter,
      },
    };
    const modalRef = this.modalService.show(
      PropertyAdvanceFilterComponent,
      initialState
    );
    this.trackingService.trackFeature(
      `Web.Property.Button.AdvanceFilter.Click`
    );
  }

  convertFlatToNested(obj: any): any {
    const result: any = {};
    for (const key in obj) {
      if (key.includes('.')) {
        const [outer, inner] = key.split('.');
        result[outer] = result[outer] || {};
        result[outer][inner] = obj[key];
      } else {
        result[key] = obj[key];
      }
    }
    return result;
  }

  exportPropertyReport() {
    this._store.dispatch(new FetchPropertyExportSuccess(''));
    this.trackingService.trackFeature(`Web.Property.Button.Export.Click`);
    let payload: any = { ...this.filtersPayload };
    payload = this.convertFlatToNested(payload);
    let initialState: any = {
      payload: {
        ...payload,
        timeZoneId:
          this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
        baseUTcOffset:
          this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };
    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (!this.searchTerm) {
        return;
      }
      this.trackingService.trackFeature(
        `Web.Property.DataEntry.Search.DataEntry`
      );
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  openPropertyTracker() {
    if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: 'property',
      };
      this._store.dispatch(new FetchPropertyExcelUploadedList(1, 10));
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedTrackerOption === 'export') {
      this._store.dispatch(new FetchExportPropertyStatus(1, 10));
      this.modalService.show(ExportPropertyTrackerComponent, {
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
      });
    }
    this.selectedTrackerOption = '';
  }

  navigateToAddProperty() {
    this.trackingService.trackFeature(`Web.Property.Button.AddProperty.Click`);
    this.router.navigate(['properties/add-property']);
  }

  openPropertyBulkUpload() {
    if (this.selectedOption === 'bulkUpload') {
      this.trackingService.trackFeature(`Web.Property.Button.BulkUpload.Click`);
      this.router.navigate(['properties/bulk-upload']);
    }
    this.selectedOption = '';
  }

  onTrackerChange(selectedValue: string) {
    if (selectedValue === 'bulkUpload') {
      this.openPropertyTracker();
      this.trackingService.trackFeature(
        `Web.Property.Button.TrackerBulkUpload.Click`
      );
    } else if (selectedValue === 'export') {
      this.openPropertyTracker();
      this.trackingService.trackFeature(
        `Web.Property.Button.TrackerExport.Click`
      );
    }
    this.selectedTrackerOption = null;
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
