import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import {
    ExistReportAutomationSuccess,
    FetchActivityExportSuccess,
    FetchAgencyExportSuccess,
    FetchCallExportSuccess,
    FetchCityReportsCountSuccess,
    FetchCityReportsSuccess,
    FetchExportReportAutomationStatusSuccess,
    FetchMeetingSiteVisitExportSuccess,
    FetchProjectExportSuccess,
    FetchProjectSubstatusExportSuccess,
    FetchReceivedDateExportSuccess,
    FetchReportAutomationListSuccess,
    FetchReportCampaignSubStatusExportSuccess,
    FetchReportCampaignSubStatusSuccess,
    FetchReportCampaignSubStatusTotalCountSuccess,
    FetchReportCpSubStatusExportSuccess,
    FetchReportCpSubStatusSuccess,
    FetchReportCpSubStatusTotalCountSuccess,
    FetchReportExportTrackerSuccess,
    FetchReportFlagCountSuccess,
    FetchReportsActivity10Success,
    FetchReportsActivity11Success,
    FetchReportsActivity12Success,
    FetchReportsActivity1Success,
    FetchReportsActivity2Success,
    FetchReportsActivity9Success,
    FetchReportsActivityTotalCountSuccess,
    FetchReportsAgencyCustomSuccess,
    FetchReportsAgencyCustomTotalCountSuccess,
    FetchReportsAgencySuccess,
    FetchReportsAgencyTotalCountSuccess,
    FetchReportsCallSuccess,
    FetchReportsCallTotalCountSuccess,
    FetchReportsCustomProjectsSuccess,
    FetchReportsCustomProjectsTotalCountSuccess,
    FetchReportsCustomSourcesSuccess,
    FetchReportsCustomSourceTotalCountSuccess,
    FetchReportsCustomSubSourcesSuccess,
    FetchReportsMeetingSiteVisitLevel1Success,
    FetchReportsMeetingSiteVisitLevel2Success,
    FetchReportsMeetingSiteVisitTotalCountSuccess,
    FetchReportsProjectSubstatusSuccess,
    FetchReportsProjectSuccess,
    FetchReportsProjectTotalCountSuccess,
    FetchReportsProjSubTotalCountSuccess,
    FetchReportsReceivedDateSuccess,
    FetchReportsReceivedDateTotalCountSuccess,
    FetchReportsSourcesSuccess,
    FetchReportsSourceTotalCountSuccess,
    FetchReportsSubSourcesSuccess,
    FetchReportsSubSourceTotalCountSuccess,
    FetchReportsSubStatusSuccess,
    FetchReportsSubStatusTotalCountSuccess,
    FetchReportsSubTotalCountSuccess,
    FetchReportsUserMeetingSiteSuccess,
    FetchReportsUserMeetingSiteTotalCountSuccess,
    FetchReportsUserSourceSuccess,
    FetchReportsUserSourceTotalCountSuccess,
    FetchReportsUserSubSourceSuccess,
    FetchReportsUserSubSourceTotalCountSuccess,
    FetchReportsUserSuccess,
    FetchReportsUserTotalCountSuccess,
    FetchSourceExportSuccess,
    FetchSubReportExportSuccess,
    FetchSubReportSuccess,
    FetchSubSourceExportSuccess,
    FetchSubStatusExportSuccess,
    FetchUserExportSuccess,
    FetchUserMeetingSiteExportSuccess,
    FetchUserSourceExportSuccess,
    FetchUserSubSourceExportSuccess,
    FetchUserWithRoleSuccess,
    ReportsActionTypes,
    UpdateActivityFilterPayload,
    UpdateAgencyFilterPayload,
    UpdateAllActivityFilterPayload,
    UpdateCallFilterPayload,
    UpdateCityFilterPayload,
    UpdateMeetingSiteVisitFilterPayload,
    UpdateProjectFilterPayload,
    UpdateProjectSubstatusFilterPayload,
    UpdateReceivedDateFilterPayload,
    UpdateReportAutomationsFiltersPayload,
    UpdateReportCampaignSubStatusFilterPayload,
    UpdateReportCpSubStatusFilterPayload,
    UpdateSourcesFilterPayload,
    UpdateSubReportFilterPayload,
    UpdateSubSourcesFilterPayload,
    UpdateSubStatusFilterPayload,
    UpdateUserFilterPayload,
    UpdateUserMeetingSiteFilterPayload,
    UpdateUserSourceFilterPayload,
    UpdateUserSubSourceFilterPayload
} from './reports.actions';

export interface ReportAutomationFiltersPayload {
    PageNumber: number;
    PageSize: number,
    path: string,
}

export type ReportsState = {
    receivedDateExport: any;
    receivedDateTotalCount: number;
    receivedDate: any;
    receivedDateFiltersPayload: any;
    cityFilterPayload: any;
    projectSubstatusExport: any;
    projectSubstatusTotalCount: number;
    projectSubstatus: any;
    projectSubstatusFiltersPayload: any;
    callExport: any;
    callFiltersPayload: any;
    call?: any;
    callTotalCount: number;
    subReportFiltersPayload: any;
    subReport?: any;
    isSubReportLoading: boolean;
    subReportTotalCount: number;
    subReportExport: any;
    activity1: any;
    activity2: any;
    activity3: any;
    activity4: any;
    activity5: any;
    activity6: any;
    activity7: any;
    activity9: any;
    activity10: any;
    activity11: any;
    activity12: any;
    activityExport: any;
    activityTotalCount: number;
    activityFiltersPayload: any;
    allActivityFiltersPayload: any;
    users?: any;
    customUsers: any,
    userSource?: any;
    userMeetingSite?: any;
    userSubSource?: any;
    userFiltersPayload: any;
    usersTotalCount: number;
    customUsersTotalCount: number;
    userExport: any;
    projects?: any;
    projectFiltersPayload: any;
    projectTotalCount: number;
    projectExport: any;
    sources?: any;
    sourcesCustom: any;
    projectsCustom: any;
    sourcesFiltersPayload: any;
    sourcesTotalCount: number;
    customSourcesTotalCount: number;
    customProjectsTotalCount: number;
    sourceExport: any;
    subSources?: any;
    customSubSource?: any;
    subSourcesFiltersPayload: any;
    subSourcesTotalCount: number;
    customSubSourcesTotalCount: number;
    subSourceExport: any;
    agency?: any;
    agencyCustom: any,
    agencyFiltersPayload: any;
    agencyTotalCount: number;
    customAgencyTotalCount: number;
    agencyExport: any;
    meetingSiteVisit?: any;
    meetingSiteVisitFiltersPayload: any;
    userSourceFiltersPayload: any
    userSourceTotalCount: number;
    userSourceExport: any;
    userMeetingSiteFiltersPayload: any;
    userMeetingSiteTotalCount: number;
    userMeetingSiteExport: any;
    userSubSourceFiltersPayload: any
    userSubSourceTotalCount: number;
    userSubSourceExport: any;
    meetingSiteVisitTotalCount: number;
    meetingSiteVisitExport: any;
    subStatus?: any;
    subStatusFiltersPayload: any;
    subStatusTotalCount: number;
    subStatusExport: any;
    exportStatus: any;
    isExportStatusLoading: boolean;
    level9IsLoading: boolean;
    level10IsLoading: boolean;
    level11IsLoading: boolean;
    level12IsLoading: boolean;
    isProjectSubstatusLoading: boolean;
    isVisitMeetingLevel1Loading: boolean;
    isVisitMeetingLevel2Loading: boolean;
    isSubSourceReportLoading: boolean;
    isCustomSubSourceReportLoading: boolean;
    isCallReportLoading: boolean;
    usersIsLoading: boolean;
    customUsersIsLoading: boolean;
    userSourceIsLoading: boolean;
    userMeetingSiteIsLoading: boolean;
    userSubSourceIsLoading: boolean;
    isProjectsLoading?: boolean;
    isSourcesLoading?: boolean;
    isCustomSourcesLoading: boolean;
    isCustomProjectsLoading: boolean,
    agencyIsLoading?: boolean;
    agencyCustomIsLoading: boolean;
    isSubStatusLoading?: boolean;
    isReceivedDateLoading: boolean;
    flagCount: any;
    isReportSubStatusLoading: boolean;
    isCityReportsLoading: boolean;
    cityReports: any;
    cityReportsTotalCount: any;
    // -------------------- REPORT AUTOMATION ----------------------------
    reportAutomationFiltersPayload: ReportAutomationFiltersPayload;
    reportAutomationList: any[];
    reportAutomationExcelUploadedList: any[];
    reportAutomationExportData: any[];
    reportAutomationTypeList: any[];
    reportAutomationExist: boolean;
    isUserWithRoleLoading: boolean;
    userWithRole: any[];
    isReportAutomationExportStatusLoading: boolean;
    isReportAutomationExcelUploadedListLoading: boolean;
    isReportAutomationTypeLoading: boolean;
    isReportAutomationExistLoading: boolean
    isReportAutomationLoading: boolean;
    pageSize: number;
    pageNumber: number;
    isReportCampaignSubStatusLoading: boolean;
    reportCampaignSubStatus: any;
    reportCampaignSubStatusTotalCount: number;
    reportCampaignSubStatusExport: any;
    campaignSubStatusFiltersPayload: any;
    isReportCpSubStatusLoading: boolean;
    reportCpSubStatus: any;
    reportCpSubStatusTotalCount: number;
    reportCpSubStatusExport: any;
    cpSubStatusFiltersPayload: any;
};

const initialState: ReportsState = {
    meetingSiteVisit: [],
    users: [],
    customUsers: [],
    userSource: [],
    userSubSource: [],
    usersIsLoading: true,
    customUsersIsLoading: true,
    userSourceIsLoading: true,
    userMeetingSite: [],
    userMeetingSiteIsLoading: true,
    userSubSourceIsLoading: true,
    projects: [],
    isProjectsLoading: true,
    sources: [],
    sourcesCustom: [],
    projectsCustom: [],
    isSourcesLoading: true,
    isCustomSourcesLoading: true,
    isCustomProjectsLoading: true,
    subSources: [],
    customSubSource: [],
    agency: [],
    agencyCustom: [],
    receivedDate: [],

    agencyIsLoading: true,
    agencyCustomIsLoading: true,

    activity1: [],
    activity2: [],
    activity3: [],
    activity4: [],
    activity5: [],
    activity6: [],
    activity7: [],
    activity9: [],
    activity10: [],
    activity11: [],
    activity12: [],
    subStatus: [],
    isSubStatusLoading: true,
    subReport: [],
    isSubReportLoading: true,
    exportStatus: [],
    isExportStatusLoading: true,
    projectSubstatus: [],
    call: [],
    projectSubstatusTotalCount: 0,
    callTotalCount: 0,
    meetingSiteVisitTotalCount: 0,
    userSourceTotalCount: 0,
    userMeetingSiteTotalCount: 0,
    userSubSourceTotalCount: 0,
    usersTotalCount: 0,
    customUsersTotalCount: 0,
    projectTotalCount: 0,
    sourcesTotalCount: 0,
    customSourcesTotalCount: 0,
    customProjectsTotalCount: 0,
    subSourcesTotalCount: 0,
    customSubSourcesTotalCount: 0,
    agencyTotalCount: 0,
    customAgencyTotalCount: 0,
    activityTotalCount: 0,
    subStatusTotalCount: 0,
    subReportTotalCount: 0,
    receivedDateTotalCount: 0,

    userFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/user/new/status',
    },
    userExport: {},
    projectFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/project/status/new',
    },
    projectExport: {},
    sourcesFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/source/status/new',
    },
    sourceExport: {},
    subSourcesFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/subsource/status/new',
    },
    subSourceExport: {},
    agencyFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/agency/status/new',
    },
    agencyExport: {},
    meetingSiteVisitFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/user/meetingandvisit/level1',
    },
    meetingSiteVisitExport: {},
    userMeetingSiteFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/user/meetingandvisit/new/level',
    },
    userMeetingSiteExport: {},
    userSourceFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/uservssource',
    },
    userSourceExport: {},
    userSubSourceFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/uservssubsource',
    },
    userSubSourceExport: {},
    activityExport: {},
    activityFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/activity/level9',
        // fromDate: new Date(),
        // toDate: new Date(),
    },
    allActivityFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        // fromDate: new Date(),
        // toDate: new Date(),
    },
    subStatusFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/substatus',
    },
    subStatusExport: {},
    subReportFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/substatus/bysubsource/updated',
    },
    subReportExport: {},
    projectSubstatusExport: {},

    projectSubstatusFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/project/bysubstatus/updated',
    },
    callExport: {},
    callFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/user/call-log/new',
    },
    isProjectSubstatusLoading: true,
    isReceivedDateLoading: true,
    isVisitMeetingLevel1Loading: true,
    isVisitMeetingLevel2Loading: true,
    isSubSourceReportLoading: true,
    isCustomSubSourceReportLoading: true,
    isCallReportLoading: true,
    receivedDateExport: {},
    receivedDateFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/datewisesource',
    },
    cityFilterPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/cityvslead',
    },
    isCityReportsLoading: true,
    flagCount: [],
    isReportSubStatusLoading: false,
    level9IsLoading: true,
    level10IsLoading: true,
    level11IsLoading: true,
    level12IsLoading: true,
    cityReports: {},
    cityReportsTotalCount: 0,
    // -------------------- REPORT AUTOMATION ----------------------------
    reportAutomationFiltersPayload: {
        PageNumber: 1,
        PageSize: 10,
        path: 'report/reportconfigurations'
    },
    reportAutomationList: [],
    reportAutomationExcelUploadedList: [],
    reportAutomationExportData: [],
    reportAutomationTypeList: [],
    reportAutomationExist: false,
    isReportAutomationExportStatusLoading: false,
    isReportAutomationExcelUploadedListLoading: false,
    isReportAutomationTypeLoading: false,
    isReportAutomationExistLoading: false,
    isReportAutomationLoading: false,
    isUserWithRoleLoading: false,
    userWithRole: [],
    pageSize: 10,
    pageNumber: 1,

    isReportCampaignSubStatusLoading: false,
    reportCampaignSubStatus: [],
    reportCampaignSubStatusTotalCount: 0,
    reportCampaignSubStatusExport: {},
    campaignSubStatusFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/campaign/bysubstatus',
    },
    isReportCpSubStatusLoading: false,
    reportCpSubStatus: [],
    reportCpSubStatusTotalCount: 0,
    reportCpSubStatusExport: {},
    cpSubStatusFiltersPayload: {
        pageNumber: 1,
        pageSize: 50,
        path: 'report/channelpartner/bysubstatus',
    },
};
export function reportsReducer(
    state: ReportsState = initialState,
    action: Action
): ReportsState {
    switch (action.type) {
        case ReportsActionTypes.FETCH_REPORTS_USER:
            return {
                ...state,
                usersIsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SUCCESS:
            return {
                ...state,
                users: (action as FetchReportsUserSuccess).response.items,
                usersIsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                usersTotalCount: (action as FetchReportsUserTotalCountSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER:
            return {
                ...state,
                customUsersIsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER_SUCCESS:
            return {
                ...state,
                customUsers: (action as FetchReportsUserSuccess).response.items,
                customUsersIsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_USER_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                customUsersTotalCount: (action as FetchReportsUserTotalCountSuccess).response,
            };
        case ReportsActionTypes.UPDATE_USER_FILTER_PAYLOAD:
            return {
                ...state,
                userFiltersPayload: (action as UpdateUserFilterPayload).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_EXPORT_SUCCESS:
            return {
                ...state,
                userExport: (action as FetchUserExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT:
            return {
                ...state,
                isProjectsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT_SUCCESS:
            return {
                ...state,
                projects: (action as FetchReportsProjectSuccess).response.items,
                isProjectsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                projectTotalCount: (action as FetchReportsProjectTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_PROJECT_FILTER_PAYLOAD:
            return {
                ...state,
                projectFiltersPayload: (action as UpdateProjectFilterPayload).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT_EXPORT_SUCCESS:
            return {
                ...state,
                projectExport: (action as FetchProjectExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_SOURCES:
            return {
                ...state,
                isSourcesLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_SOURCES_SUCCESS:
            return {
                ...state,
                sources: (action as FetchReportsSourcesSuccess).response.items,
                isSourcesLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                sourcesTotalCount: (action as FetchReportsSourceTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCES:
            return {
                ...state,
                isCustomSourcesLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCES_SUCCESS:

            return {
                ...state,
                sourcesCustom: (action as FetchReportsCustomSourcesSuccess).response.items,
                isCustomSourcesLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                customSourcesTotalCount: (action as FetchReportsCustomSourceTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS:
            return {
                ...state,
                isCustomProjectsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS_SUCCESS:

            return {
                ...state,
                projectsCustom: (action as FetchReportsCustomProjectsSuccess).response.items,
                isCustomProjectsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_PROJECTS_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                customProjectsTotalCount: (action as FetchReportsCustomProjectsTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_SOURCES_FILTER_PAYLOAD:
            return {
                ...state,
                sourcesFiltersPayload: (action as UpdateSourcesFilterPayload).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_SOURCE_EXPORT_SUCCESS:
            return {
                ...state,
                sourceExport: (action as FetchSourceExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_SOURCES:
            return {
                ...state,
                isSubSourceReportLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_SOURCES_SUCCESS:
            return {
                ...state,
                subSources: (action as FetchReportsSubSourcesSuccess).response.items,
                isSubSourceReportLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                subSourcesTotalCount: (action as FetchReportsSubSourceTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCES:
            return {
                ...state,
                isCustomSubSourceReportLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCES_SUCCESS:
            return {
                ...state,
                customSubSource: (action as FetchReportsCustomSubSourcesSuccess).response.items,
                isCustomSubSourceReportLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_CUSTOM_SUB_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                customSubSourcesTotalCount: (action as FetchReportsSubSourceTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_SUB_SOURCES_FILTER_PAYLOAD:
            return {
                ...state,
                subSourcesFiltersPayload: (action as UpdateSubSourcesFilterPayload)
                    .filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_SOURCE_EXPORT_SUCCESS:
            return {
                ...state,
                subSourceExport: (action as FetchSubSourceExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY:
            return {
                ...state,
                agencyIsLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY_SUCCESS:
            return {
                ...state,
                agency: (action as FetchReportsAgencySuccess).response.items,
                agencyIsLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM:
            return {
                ...state,
                agencyCustomIsLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM_SUCCESS:
            return {
                ...state,
                agencyCustom: (action as FetchReportsAgencyCustomSuccess).response.items,
                agencyCustomIsLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                agencyTotalCount: (action as FetchReportsAgencyTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY_CUSTOM_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                customAgencyTotalCount: (action as FetchReportsAgencyCustomTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_AGENCY_FILTER_PAYLOAD:
            return {
                ...state,
                agencyFiltersPayload: (action as UpdateAgencyFilterPayload).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_AGENCY_EXPORT_SUCCESS:
            return {
                ...state,
                agencyExport: (action as FetchAgencyExportSuccess).response,
            };
        case ReportsActionTypes.CLEAN_MEETING_VISIT_REPORT_LIST:
            return {
                ...state,
                meetingSiteVisit: [],
            };
        case ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1:
            return {
                ...state,
                isVisitMeetingLevel1Loading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2:
            return {
                ...state,
                isVisitMeetingLevel2Loading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL1_SUCCESS:
            let newMeetingSiteVisitLevel1 = (action as FetchReportsMeetingSiteVisitLevel1Success).response;
            if (state.meetingSiteVisit.length === 0) {
                return {
                    ...state,
                    meetingSiteVisit: newMeetingSiteVisitLevel1,
                    isVisitMeetingLevel1Loading: false,
                };
            } else {
                let mergedMeetingSiteVisit = state.meetingSiteVisit.map((item: any) => {
                    let newItem = newMeetingSiteVisitLevel1.find((newItem: any) => newItem.id === item.id);
                    return newItem ? { ...item, ...newItem } : item;
                });
                return {
                    ...state,
                    meetingSiteVisit: mergedMeetingSiteVisit,
                    isVisitMeetingLevel1Loading: false,
                };
            }
        case ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_LEVEL2_SUCCESS:
            let newMeetingSiteVisitLevel2 = (action as FetchReportsMeetingSiteVisitLevel2Success).response;
            if (state.meetingSiteVisit.length === 0) {
                return {
                    ...state,
                    meetingSiteVisit: newMeetingSiteVisitLevel2,
                    isVisitMeetingLevel2Loading: false,
                };
            } else {
                let mergedMeetingSiteVisit = state.meetingSiteVisit.map((item: any) => {
                    let newItem = newMeetingSiteVisitLevel2.find((newItem: any) => newItem.id === item.id);
                    return newItem ? { ...item, ...newItem } : item;
                });
                return {
                    ...state,
                    meetingSiteVisit: mergedMeetingSiteVisit,
                    isVisitMeetingLevel2Loading: false,
                };
            }
        case ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                meetingSiteVisitTotalCount: (
                    action as FetchReportsMeetingSiteVisitTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_MEETING_SITE_VISIT_FILTER_PAYLOAD:
            return {
                ...state,
                meetingSiteVisitFiltersPayload: (
                    action as UpdateMeetingSiteVisitFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_MEETING_SITE_VISIT_EXPORT_SUCCESS:
            return {
                ...state,
                meetingSiteVisitExport: (action as FetchMeetingSiteVisitExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY1_SUCCESS:
            return {
                ...state,
                activity1: (action as FetchReportsActivity1Success).response.items,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY2_SUCCESS:
            return {
                ...state,
                activity2: (action as FetchReportsActivity2Success).response.items,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY9:
            return {
                ...state,
                activity1: [],
                activity2: [],
                level9IsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY9_SUCCESS:
            return {
                ...state,
                activity9: (action as FetchReportsActivity9Success).response.items,
                level9IsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY10:
            return {
                ...state,
                level10IsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY10_SUCCESS:
            return {
                ...state,
                activity10: (action as FetchReportsActivity10Success).response.items,
                level10IsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY11:
            return {
                ...state,
                level11IsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY11_SUCCESS:
            return {
                ...state,
                activity11: (action as FetchReportsActivity11Success).response.items,
                level11IsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY12:
            return {
                ...state,
                level12IsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY12_SUCCESS:
            return {
                ...state,
                activity12: (action as FetchReportsActivity12Success).response.items,
                level12IsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                activityTotalCount: (action as FetchReportsActivityTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_ACTIVITY_FILTER_PAYLOAD:
            return {
                ...state,
                activityFiltersPayload: (action as UpdateActivityFilterPayload).filter,
            };
        case ReportsActionTypes.UPDATE_ALL_ACTIVITY_FILTER_PAYLOAD:
            return {
                ...state,
                allActivityFiltersPayload: (action as UpdateAllActivityFilterPayload).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_ACTIVITY_EXPORT_SUCCESS:
            return {
                ...state,
                activityExport: (action as FetchActivityExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_EXPORT_TRACKER:
            return {
                ...state,
                isExportStatusLoading: true,
            };
        case ReportsActionTypes.FETCH_EXPORT_TRACKER_SUCCESS:
            return {
                ...state,
                exportStatus: (action as FetchReportExportTrackerSuccess).response,
                isExportStatusLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_STATUS:
            return {
                ...state,
                isSubStatusLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_SUCCESS:
            return {
                ...state,
                subStatus: (action as FetchReportsSubStatusSuccess).response.items,
                isSubStatusLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                subStatusTotalCount: (action as FetchReportsSubStatusTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_SUB_STATUS_FILTER_PAYLOAD:
            return {
                ...state,
                subStatusFiltersPayload: (action as UpdateSubStatusFilterPayload)
                    .filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_STATUS_EXPORT_SUCCESS:
            return {
                ...state,
                subStatusExport: (action as FetchSubStatusExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_SUB_REPORT:
            return {
                ...state,
                isSubReportLoading: true
            };
        case ReportsActionTypes.FETCH_SUB_REPORT_SUCCESS:
            return {
                ...state,
                subReport: (action as FetchSubReportSuccess).response.items,
                isSubReportLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_SUB_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                subReportTotalCount: (action as FetchReportsSubTotalCountSuccess)
                    .response,
            };
        case ReportsActionTypes.UPDATE_SUB_REPORT_FILTER_PAYLOAD:
            return {
                ...state,
                subReportFiltersPayload: (action as UpdateSubReportFilterPayload)
                    .filter,
            };
        case ReportsActionTypes.FETCH_SUB_REPORT_EXPORT_SUCCESS:
            return {
                ...state,
                subReportExport: (action as FetchSubReportExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS:
            return {
                ...state,
                isProjectSubstatusLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS_SUCCESS:
            return {
                ...state,
                projectSubstatus: (action as FetchReportsProjectSubstatusSuccess)
                    .response.items,
                isProjectSubstatusLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJ_SUB_STATUS_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                projectSubstatusTotalCount: (
                    action as FetchReportsProjSubTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_PROJECT_SUB_STATUS_FILTER_PAYLOAD:
            return {
                ...state,
                projectSubstatusFiltersPayload: (
                    action as UpdateProjectSubstatusFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_PROJECT_SUB_STATUS_EXPORT_SUCCESS:
            return {
                ...state,
                projectSubstatusExport: (action as FetchProjectSubstatusExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_CALL:
            return {
                ...state,
                isCallReportLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_CALL_SUCCESS:
            return {
                ...state,
                call: (action as FetchReportsCallSuccess).response.items,
                isCallReportLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_CALL_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                callTotalCount: (action as FetchReportsCallTotalCountSuccess).response,
            };
        case ReportsActionTypes.UPDATE_CALL_FILTER_PAYLOAD:
            return {
                ...state,
                callFiltersPayload: (action as UpdateCallFilterPayload).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_CALL_EXPORT_SUCCESS:
            return {
                ...state,
                callExport: (action as FetchCallExportSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE:
            return {
                ...state,
                isReceivedDateLoading: true
            }
        case ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_SUCCESS:
            return {
                ...state,
                receivedDate: (action as FetchReportsReceivedDateSuccess)
                    .response.items,
                isReceivedDateLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                receivedDateTotalCount: (
                    action as FetchReportsReceivedDateTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_RECEIVED_DATE_FILTER_PAYLOAD:
            return {
                ...state,
                receivedDateFiltersPayload: (
                    action as UpdateReceivedDateFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_RECEIVED_DATE_EXPORT_SUCCESS:
            return {
                ...state,
                receivedDateExport: (action as FetchReceivedDateExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_FLAG_COUNT_SUCCESS:
            return {
                ...state,
                flagCount: (action as FetchReportFlagCountSuccess).response,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SOURCE:
            return {
                ...state,
                userSourceIsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_SUCCESS:
            return {
                ...state,
                userSource: (action as FetchReportsUserSourceSuccess).response.items,
                userSourceIsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                userSourceTotalCount: (
                    action as FetchReportsUserSourceTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_USER_SOURCE_FILTER_PAYLOAD:
            return {
                ...state,
                userSourceFiltersPayload: (
                    action as UpdateUserSourceFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SOURCE_EXPORT_SUCCESS:
            return {
                ...state,
                userSourceExport: (action as FetchUserSourceExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE:
            return {
                ...state,
                userSubSourceIsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_SUCCESS:
            return {
                ...state,
                userSubSource: (action as FetchReportsUserSubSourceSuccess).response.items,
                userSubSourceIsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                userSubSourceTotalCount: (
                    action as FetchReportsUserSubSourceTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_USER_SUB_SOURCE_FILTER_PAYLOAD:
            return {
                ...state,
                userSubSourceFiltersPayload: (
                    action as UpdateUserSubSourceFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_SUB_SOURCE_EXPORT_SUCCESS:
            return {
                ...state,
                userSubSourceExport: (action as FetchUserSubSourceExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE:
            return {
                ...state,
                userMeetingSiteIsLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_SUCCESS:
            return {
                ...state,
                userMeetingSite: (action as FetchReportsUserMeetingSiteSuccess).response.items,
                userMeetingSiteIsLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                userMeetingSiteTotalCount: (
                    action as FetchReportsUserMeetingSiteTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_USER_MEETING_SITE_FILTER_PAYLOAD:
            return {
                ...state,
                userMeetingSiteFiltersPayload: (
                    action as UpdateUserMeetingSiteFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_USER_MEETING_SITE_EXPORT_SUCCESS:
            return {
                ...state,
                userMeetingSiteExport: (action as FetchUserMeetingSiteExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_CITY_REPORTS:
            return {
                ...state,
                isCityReportsLoading: true
            }
        case ReportsActionTypes.FETCH_CITY_REPORTS_SUCCESS:
            return {
                ...state,
                cityReports: (action as FetchCityReportsSuccess)?.resp?.items,
                isCityReportsLoading: false
            }
        case ReportsActionTypes.FETCH_CITY_REPORTS_COUNT_SUCCESS:
            return {
                ...state,
                cityReportsTotalCount: (action as FetchCityReportsCountSuccess)?.resp
            }
        case ReportsActionTypes.UPDATE_CITY_FILTER_PAYLOAD:
            return {
                ...state,
                cityFilterPayload: (action as UpdateCityFilterPayload)?.payload
            }
        // --------------------- REPORT AUTOMATION ----------------------------
        case ReportsActionTypes.FETCH_REPORT_AUTOMATION:
            return {
                ...state,
                isReportAutomationLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORT_AUTOMATION_SUCCESS:
            return {
                ...state,
                reportAutomationList: (action as FetchReportAutomationListSuccess).response,
                isReportAutomationLoading: false,
            };
        case ReportsActionTypes.FETCH_EXPORT_REPORT_AUTOMATION_STATUS:
            return {
                ...state,
                isReportAutomationExportStatusLoading: true,
            };
        case ReportsActionTypes.FETCH_EXPORT_REPORT_AUTOMATION_STATUS_SUCCESS:
            return {
                ...state,
                reportAutomationExportData: (action as FetchExportReportAutomationStatusSuccess)
                    .response,
                isReportAutomationExportStatusLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORT_AUTOMATION_TYPE:
            return {
                ...state,
                isReportAutomationTypeLoading: true,
            };
        case ReportsActionTypes.FETCH_REPORT_AUTOMATION_TYPE_SUCCESS:
            return {
                ...state,
                reportAutomationTypeList: (action as FetchReportAutomationListSuccess).response,
                isReportAutomationTypeLoading: false,
            };

        case ReportsActionTypes.UPDATE_REPORT_AUTOMATION_PAYLOAD:
            return {
                ...state,
                reportAutomationFiltersPayload: (action as UpdateReportAutomationsFiltersPayload).payload,
            };
        case ReportsActionTypes.EXIST_REPORT_AUTOMATION:
            return {
                ...state,
                isReportAutomationExistLoading: true,
            };
        case ReportsActionTypes.EXIST_REPORT_AUTOMATION_SUCCESS:
            return {
                ...state,
                reportAutomationExist: (action as ExistReportAutomationSuccess).response,
                isReportAutomationExistLoading: false,
            };
        case ReportsActionTypes.FETCH_USER_WITH_ROLE:
            return {
                ...state,
                isUserWithRoleLoading: true,
            };
        case ReportsActionTypes.FETCH_USER_WITH_ROLE_SUCCESS:
            return {
                ...state,
                userWithRole: (action as FetchUserWithRoleSuccess).response,
                isUserWithRoleLoading: false,
            };
        case ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS:
            return {
                ...state,
                isReportCampaignSubStatusLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS_SUCCESS:
            return {
                ...state,
                reportCampaignSubStatus: (action as FetchReportCampaignSubStatusSuccess)
                    .response.items,
                isReportCampaignSubStatusLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                reportCampaignSubStatusTotalCount: (
                    action as FetchReportCampaignSubStatusTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_CAMPAIGN_SUBSTATUS_FILTER_PAYLOAD:
            return {
                ...state,
                campaignSubStatusFiltersPayload: (
                    action as UpdateReportCampaignSubStatusFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_CAMPAIGN_SUBSTATUS_EXPORT_SUCCESS:
            return {
                ...state,
                reportCampaignSubStatusExport: (action as FetchReportCampaignSubStatusExportSuccess)
                    .response,
            };
        case ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS:
            return {
                ...state,
                isReportCpSubStatusLoading: true
            };
        case ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS_SUCCESS:
            return {
                ...state,
                reportCpSubStatus: (action as FetchReportCpSubStatusSuccess)
                    .response.items,
                isReportCpSubStatusLoading: false
            };
        case ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS_TOTAL_COUNT_SUCCESS:
            return {
                ...state,
                reportCpSubStatusTotalCount: (
                    action as FetchReportCpSubStatusTotalCountSuccess
                ).response,
            };
        case ReportsActionTypes.UPDATE_CP_SUBSTATUS_FILTER_PAYLOAD:
            return {
                ...state,
                cpSubStatusFiltersPayload: (
                    action as UpdateReportCpSubStatusFilterPayload
                ).filter,
            };
        case ReportsActionTypes.FETCH_REPORTS_CP_SUBSTATUS_EXPORT_SUCCESS:
            return {
                ...state,
                reportCpSubStatusExport: (action as FetchReportCpSubStatusExportSuccess)
                    .response,
            };
        default:
            return state;
    }
}

export const selectFeature = (state: AppState) => state.reports;

export const getReportsUsersListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.usersIsLoading;
    }
);

export const getReportsCustomUsersListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.customUsersIsLoading;
    }
);

export const getUserFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.userFiltersPayload
);

export const getReportsUsersList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.users,
            totalCount: state.usersTotalCount,
        };
    }
);

export const getReportsCustomUsersList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.customUsers;
    }
);

export const getReportsCustomUsersListTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.customUsersTotalCount;
    }
);

export const getUserExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.userExport
);

export const getProjectFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.projectFiltersPayload
);

export const getReportsProjectList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.projects,
            totalCount: state.projectTotalCount,
        };
    }
);

export const getReportsProjectListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isProjectsLoading;
    }
);

export const getProjectExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.projectExport
);

export const getSourcesFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.sourcesFiltersPayload
);

export const getReportsSourcesList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.sources,
            totalCount: state.sourcesTotalCount,
        };
    }
);

export const getReportsSourcesListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isSourcesLoading;
    }
);

export const getReportsSourcesCustomList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.sourcesCustom;
    }
);

export const getReportsCustomSourcesListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isCustomSourcesLoading;
    }
);

export const getReportsCustomSourcesTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.customSourcesTotalCount;
    }
);

export const getSourceExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.sourceExport
);

export const getSubSourcesFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.subSourcesFiltersPayload
);

export const getReportsSubSourcesList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.subSources,
            totalCount: state.subSourcesTotalCount,
        };
    }
);

export const getReportsSubSourcesListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isSubSourceReportLoading;
    }
);

export const getReportsCustomSubSourcesList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.customSubSource;
    }
);

export const getReportsCustomSubSourcesTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.customSubSourcesTotalCount;
    }
);

export const getReportsCustomSubSourcesListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isCustomSubSourceReportLoading;
    }
);

export const getSubSourceExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.subSourceExport
);

export const getAgencyFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.agencyFiltersPayload
);

export const getReportsAgencyList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.agency,
            totalCount: state.agencyTotalCount,
        };
    }
);

export const getReportsAgencyCustomList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.agencyCustom;
    }
);

export const getReportsAgencyCustomListTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.customAgencyTotalCount;
    }
);

export const getReportsAgencyListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.agencyIsLoading;
    }
);

export const getReportsAgencyCustomListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.agencyCustomIsLoading;
    }
);

export const getAgencyExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.agencyExport
);

export const getExportStatus = createSelector(
    selectFeature,
    (state: ReportsState) => state.exportStatus
);

export const getExportStatusIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isExportStatusLoading
);

export const getMeetingSiteVisitFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.meetingSiteVisitFiltersPayload
);

export const getReportsMeetingSiteVisitList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.meetingSiteVisit,
            totalCount: state.meetingSiteVisitTotalCount,
        };
    }
);

export const getMeetingSiteVisitExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.meetingSiteVisitExport
);

export const getActivityFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.activityFiltersPayload
);

export const getActivityReportsTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.activityTotalCount;
    }
);

export const getReportsActivity1List = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.activity1,
            totalCount: state.activityTotalCount,
        };
    }
);

export const getReportsActivity2List = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items2: state.activity2,
        };
    }
);


export const getReportsActivity9List = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.activity9;
    }
);

export const getReportsActivity10List = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.activity10;
    }
);

export const getReportsActivity11List = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.activity11;
    }
);

export const getReportsActivity12List = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.activity12;
    }
);

export const getLevel9IsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.level9IsLoading
);

export const getLevel10IsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.level10IsLoading
);
export const getLevel11IsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.level11IsLoading
);
export const getLevel12IsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.level12IsLoading
);

export const getSubReportFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.subReportFiltersPayload
);

export const getSubReportList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.subReport,
            totalCount: state.subReportTotalCount,
            isLoading: state.isSubReportLoading
        };
    }
);

export const getSubReportExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.subReportExport
);

export const getSubStatusFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.subStatusFiltersPayload
);

export const getReportsSubStatusList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.subStatus,
            totalCount: state.subStatusTotalCount,
        };
    }
);

export const getReportsSubStatusListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isSubStatusLoading;
    }
);

export const getSubStatusExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.subStatusExport
);

export const getProjectSubstatusFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.projectSubstatusFiltersPayload
);

export const getReportsProjectSubstatusList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.projectSubstatus,
            totalCount: state.projectSubstatusTotalCount,
        };
    }
);

export const getReportsProjectSubstatusListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isProjectSubstatusLoading;
    }
);

export const getReportsMeetingSiteVisitLevel1ListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isVisitMeetingLevel1Loading;
    }
);

export const getReportsMeetingSiteVisitLevel2ListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isVisitMeetingLevel2Loading;
    }
);

export const getCallFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.callFiltersPayload
);

export const getReportsCallList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.call,
            totalCount: state.callTotalCount,
        };
    }
);

export const getReportsCallListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.isCallReportLoading;
    }
);

export const getProjectSubstatusExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.projectSubstatusExport
);

export const getCallExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.callExport
);

export const getReceivedDateFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.receivedDateFiltersPayload
);

export const getReportsReceivedDateList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.receivedDate,
            totalCount: state.receivedDateTotalCount,
        };
    }
);

export const getReceivedDateExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.receivedDateExport
);

export const getReportsReceivedDateListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isReceivedDateLoading;
    }
);

export const getFlagCounts = createSelector(
    selectFeature,
    (state: ReportsState) => state.flagCount
);

export const getisReportSubStatusLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isReportSubStatusLoading
);

export const getUserSourceFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.userSourceFiltersPayload
);

export const getReportsUserSourceList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.userSource,
            totalCount: state.userSourceTotalCount,
        };
    }
);

export const getReportsUsersSourceListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.userSourceIsLoading;
    }
);

export const getUserSourceExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.userSourceExport
);

export const getUserSubSourceFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.userSubSourceFiltersPayload
);

export const getReportsUserSubSourceList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.userSubSource,
            totalCount: state.userSubSourceTotalCount,
        };
    }
);

export const getReportsUsersSubSourceListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.userSubSourceIsLoading;
    }
);

export const getUserSubSourceExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.userSubSourceExport
);

export const getAllActivityFilterPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.allActivityFiltersPayload
);

export const getReportsProjectsCustomList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.projectsCustom;
    }
);
export const getReportsProjectsCustomListTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.customProjectsTotalCount;
    }
);
export const getReportsCustomProjectsListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isCustomProjectsLoading;
    }
);

export const getUserMeetingSiteFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.userMeetingSiteFiltersPayload
);

export const getReportsUserMeetingSiteList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.userMeetingSite,
            totalCount: state.userMeetingSiteTotalCount,
        };
    }
);

export const getReportsUsersMeetingSiteListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state?.userMeetingSiteIsLoading;
    }
);


export const getUserMeetingSiteExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.userMeetingSiteExport
);

export const getIsCityReportsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isCityReportsLoading
)


export const getCityReports = createSelector(
    selectFeature,
    (state: ReportsState) => state.cityReports
)

export const getCityReportsTotalCount = createSelector(
    selectFeature,
    (state: ReportsState) => state.cityReportsTotalCount
)

export const getCityReportsFilter = createSelector(
    selectFeature,
    (state: ReportsState) => state.cityFilterPayload
)
// --------------- REPORT AUTOMATION ----------------------------
export const getReportAutomationList = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportAutomationList
);

export const getIsReportAutomationLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isReportAutomationLoading
);


export const getReportAutomationTypeList = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportAutomationTypeList
);

export const getIsReportAutomationTyupeLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isReportAutomationTypeLoading
);

export const getReportAutomationFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportAutomationFiltersPayload
);

export const getReportAutomationExcelUploadedList = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportAutomationExcelUploadedList
);

export const getIsReportAutomationExcelUploadedListLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isReportAutomationExcelUploadedListLoading
);

export const getReportAutomationExportData = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportAutomationExportData
);
export const getIsReportAutomationExportStatusLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isReportAutomationExportStatusLoading
);

export const getReportAutomationExist = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportAutomationExist
);

export const getUserWithRole = createSelector(
    selectFeature,
    (state: ReportsState) => state.userWithRole
);
export const getIsUserWithRoleLoading = createSelector(
    selectFeature,
    (state: ReportsState) => state.isUserWithRoleLoading
);

export const getCampaignSubStatusFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.campaignSubStatusFiltersPayload
);

export const getReportsCampaignSubStatusList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.reportCampaignSubStatus,
            totalCount: state.reportCampaignSubStatusTotalCount,
        };
    }
);

export const getReportsCampaignSubStatusListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isReportCampaignSubStatusLoading;
    }
);

export const getCampaignSubStatusExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportCampaignSubStatusExport
);

export const getReportCpSubStatusFiltersPayload = createSelector(
    selectFeature,
    (state: ReportsState) => state.cpSubStatusFiltersPayload
);

export const getReportsCpSubStatusList = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return {
            items: state.reportCpSubStatus,
            totalCount: state.reportCpSubStatusTotalCount,
        };
    }
);

export const getReportsCpSubStatusListIsLoading = createSelector(
    selectFeature,
    (state: ReportsState) => {
        return state.isReportCpSubStatusLoading;
    }
);

export const getCpSubStatusExport = createSelector(
    selectFeature,
    (state: ReportsState) => state.reportCpSubStatusExport
);