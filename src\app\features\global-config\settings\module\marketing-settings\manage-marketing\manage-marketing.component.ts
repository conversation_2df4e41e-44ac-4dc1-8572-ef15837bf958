import { Component, EventEmitter, OnInit } from '@angular/core';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs/operators';
import { AppState } from 'src/app/app.reducer';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchAgencyExcelUploadedList, FetchAgencyExportTracker, FetchCampaignExcelUploadedList, FetchCampaignExportTracker, FetchChannelPartnerExcelUploadedList, FetchChannelPartnerExportTracker } from 'src/app/reducers/manage-marketing/marketing.action';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { ExportMarketingTrackerComponent } from 'src/app/shared/components/export-marketing-tracker/export-marketing-tracker.component';
import { AddAgencyComponent } from '../agency-name/add-agency-name/add-agency/add-agency.component';
import { AddCampaignComponent } from '../campaign-name/add-campaign/add-campaign.component';
import { AddChannelPartnerComponent } from '../channel-partners/add-channel-partner/add-channel-partner/add-channel-partner.component';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';

@Component({
  selector: 'manage-marketing',
  templateUrl: './manage-marketing.component.html',
})
export class ManageMarketingComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  selectedSection: string = 'Agency Name';
  selectedTrackerOption: string | null = null;

  canView: boolean;
  showLeftNav: boolean;

  constructor(
    private store: Store<AppState>,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    public router: Router,
    public notificationService: NotificationsService,
    public shareDataService: ShareDataService
  ) {
    this.store.dispatch(new FetchUsersListForReassignment());
    this.metaTitle.setTitle('CRM | Global Config | Marketing');
    this.headerTitle.setLangTitle('Manage Marketing');
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.GlobalSettings.View')) {
          this.canView = true;
        }
      });

  }

  ngOnInit() {
    this.shareDataService.getMarketingSelectedTab().subscribe((selectedTab: any) => {
      if (selectedTab === null || selectedTab === 'Agency Name') {
        this.selectedSection = 'Agency Name';
      } else if (selectedTab === 'Channel partner') {
        this.selectedSection = 'Channel partner';
      } else {
        this.selectedSection = 'Campaign name';
      }
    });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  openAgencyNameModal() {
    let initialState: any = {
      isAddNewStatus: true
    };
    this.modalRef = this.modalService.show(AddAgencyComponent, {
      class: 'right-modal modal-350 ip-modal-unset',
      initialState,
    });
  }


  openCampaignNameModal() {
    let initialState: any = {
      isAddNewStatus: true
    };
    this.modalRef = this.modalService.show(AddCampaignComponent, {
      class: 'up-modal modal-350', ignoreBackdropClick: true,
      initialState,
    });
  }


  openChannelPartnerModal() {
    let initialState: any = {
      isAddNewStatus: true
    };
    this.modalRef = this.modalService.show(AddChannelPartnerComponent, {
      class: 'right-modal modal-350 ip-modal-unset',
      initialState,
    });
  }

  openLeadsTracker() {
    if (this.selectedTrackerOption === 'bulkUpload') {
      let initialState: any = {
        fieldType: this.selectedSection,
      };
      if (this.selectedSection === 'Agency Name') {
        this.store.dispatch(new FetchAgencyExcelUploadedList(1, 10));
      } else if (this.selectedSection === 'Campaign name') {
        this.store.dispatch(new FetchCampaignExcelUploadedList(1, 10));
      } else {
        this.store.dispatch(new FetchChannelPartnerExcelUploadedList(1, 10));
      }
      this.modalService.show(ExcelUploadedStatusComponent, {
        class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
        initialState,
      });
    } else if (this.selectedTrackerOption === 'export') {
      let initialState: any = {
        fieldType: this.selectedSection,
      };
      if (this.selectedSection === 'Agency Name') {
        this.store.dispatch(new FetchAgencyExportTracker(1, 10));
      } else if (this.selectedSection === 'Campaign name') {
        this.store.dispatch(new FetchCampaignExportTracker(1, 10));
      } else {
        this.store.dispatch(new FetchChannelPartnerExportTracker(1, 10));
      }
      this.modalService.show(ExportMarketingTrackerComponent, {
        class: 'modal-900 modal-dialog-centered h-100 tb-modal-unset',
        initialState
      });
    }
    this.selectedTrackerOption = '';
  }

  uploadMarketingBulk($event: any, data: string) {
    const queryParams = { query: data };
    this.router.navigate(['global-config/marketing-bulk-upload'], { queryParams });
  }

  selectedTab() {
    this.shareDataService.setMarketingSelectedTab(this.selectedSection);
  }

  ngOnDestroy() {
    this.stopper.complete();
  }
}