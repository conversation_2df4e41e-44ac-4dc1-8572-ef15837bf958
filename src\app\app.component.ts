import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnI<PERSON>t,
  Renderer2,
} from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { SwUpdate } from '@angular/service-worker';
import FingerprintJS from '@fingerprintjs/fingerprintjs';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { getMessaging, getToken } from 'firebase/messaging';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { DeviceDetectorService } from 'ngx-device-detector';
import { AnimationOptions } from 'ngx-lottie';
import { filter, take, takeUntil } from 'rxjs';

import { environment } from '../environments/environment';
import { APP_VERSION, INDEXEDDB_STORES } from './app.constants';
import { AppState } from './app.reducer';
import { isEmptyObject } from './core/utils/common.util';
import { SupportComponent } from './features/login/support/support.component';
import { FetchFeaturesList } from './reducers/analytics/analytics.actions';
import { FetchGlobalSettingsAnonymous } from './reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from './reducers/global-settings/global-settings.reducer';
import { UpdateLeadCustomStatusEnabled } from './reducers/lead/lead.actions';
import { FetchModifiedDatesList, FetchPropertyTypesList } from './reducers/master-data/master-data.actions';
import { getPropertyTypes } from './reducers/master-data/master-data.reducer';
import { UpdatePermissions } from './reducers/permissions/permissions.actions';
import { FetchUserProfile } from './reducers/teams/teams.actions';
import { getUserProfile } from './reducers/teams/teams.reducer';
import { PnsService } from './services/controllers/PnsRegistration.service';
import { ClarityService } from './services/shared/clarity.service';
import { NetworkStatusService } from './services/shared/network-status.service';
import { PermissionsService } from './services/shared/permissions.service';
import { SignalRService } from './services/shared/web-socket.service';
import { RefreshAppComponent } from './shared/components/refresh-app/refresh-app.component';
import { FetchProducts } from './reducers/this/products/products.actions';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  currentLang: string = localStorage.getItem('locale') || 'en';
  title: string = 'CRM';
  showAppLayout: boolean = true;
  userDetails = '';
  userId = '';
  options = {
    timeOut: 3000,
    showProgressBar: false,
    pauseOnHover: true,
    clickToClose: true,
    maxStack: 3,
  };
  allPermissions: any;
  userData: any;
  private supportModal: BsModalRef | null = null;
  isCopyPasteEnabled: boolean;
  isScreenshotEnabled: boolean;
  visitorId: string;
  browserDetails: any;
  isOnline!: boolean;
  warn: AnimationOptions = { path: 'assets/animations/warning.json' };

  constructor(
    private SignalRService: SignalRService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private translate: TranslateService,
    private store: Store<AppState>,
    private permissionsService: PermissionsService,
    private afs: AngularFirestore,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    private swUpdate: SwUpdate,
    private renderer: Renderer2,
    private _notificationsService: NotificationsService,
    private PnsService: PnsService,
    private deviceService: DeviceDetectorService,
    private networkStatusService: NetworkStatusService,
    private clarityService: ClarityService
  ) { }

  /**
   * Simple clickjacking prevention
   */
  private preventClickjacking(): void {
    if (window.self !== window.top) {
      document.body.innerHTML = 'This page cannot be displayed in a frame';
    }
  }

  ngOnInit(): void {
    // Enhanced clickjacking protection
    this.preventClickjacking();

    this.createDb();
    this.translate.setDefaultLang('en');
    this.translate.use(this.currentLang);

    if (environment.production) {
      this.clarityService.loadClarity();

      this.store.dispatch(new FetchFeaturesList());
    }

    this.swUpdate.available.subscribe((event) => {
      this.swUpdate.activateUpdate().then(() => {
        this.updateVersion();
      });
    });

    this.networkStatusService.getNetworkStatus().subscribe((status) => {
      this.isOnline = status;
    });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(
        filter((data: any) => data && !!Object.keys(data).length),
        take(1)
      )
      .subscribe((data: any) => {
        if (!JSON.parse(localStorage.getItem('propertyType') || '[]').length) {
          data?.shouldEnablePropertyListing
            ? this.store.dispatch(new FetchPropertyTypesList('listing'))
            : this.store.dispatch(new FetchPropertyTypesList());
          this.store.select(getPropertyTypes).subscribe((data: any) => {
            if (data?.length) {
              let propertyTypeList = JSON.stringify(data);
              localStorage.setItem('propertyType', propertyTypeList);
            }
          });
        }
      });

    this.store.select(getGlobalSettingsAnonymous).subscribe((data: any) => {
      this.handleGlobalSettings(data);
    });

    // this.checkVersion()

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd && !this.userId) {
        this.userDetails = localStorage.getItem('userDetails');
        this.userId = JSON.parse(this.userDetails)?.sub;
        if (this.userId) {
          this.store.dispatch(new FetchUserProfile(this.userId));
          this.store
            .select(getUserProfile)
            .pipe(takeUntil(this.stopper))
            .subscribe((item: any) => {
              this.userData = item;
              if (
                item &&
                this.userId &&
                !isEmptyObject(item) &&
                item?.userId === this.userId
              ) {
                this.checkNotificationSupport();
                // Check if user has Admin role
                const isAdmin = item.rolePermission.some(
                  (permissions: Record<string, any>) =>
                    permissions.name.toLowerCase() === 'admin'
                );
                localStorage.setItem('isAdmin', isAdmin.toString());

                this.allPermissions = Object.values(
                  item.rolePermission.filter(
                    (permissions: Record<string, any>) =>
                      permissions.name.toLowerCase() != 'default'
                  )
                ).map((data: any) => {
                  return data.permissions;
                });
                const flatArray = this.allPermissions.flat();
                const distinctPermissions: any = [...new Set(flatArray)];
                this.store.dispatch(new UpdatePermissions(distinctPermissions));
                localStorage.setItem('userPermissions', distinctPermissions);
                this.permissionsService.routeToUrl(
                  this.router.url,
                  distinctPermissions
                );
              }
            });
          this.store.dispatch(new FetchGlobalSettingsAnonymous());
          // this.store.dispatch(new FetchProducts());
          // if (environment.production) {
          //   this.createAnalyticsRecord();
          // }
          this.connectWebSocket();
        } else {
          localStorage.clear();
          if (
            !window.location.pathname.includes('/login') &&
            !window.location.pathname.includes('/no-auth') &&
            !window.location.pathname.includes('/external')
          ) {
            window.location.href = '/login';
          }
        }
        const { hideAppLayout = false } =
          this.activatedRoute?.snapshot?.firstChild?.data;
        this.showAppLayout = !hideAppLayout;
      }
      if (this.userId) {
        this.store.dispatch(new FetchModifiedDatesList());
      }
    });

  }

  updateVersion(): void {
    this.afs
      .collection('versioning')
      .doc('versioning')
      .valueChanges()
      .subscribe((data: any) => {
        if (this.modalRef) {
          this.modalRef.hide();
        }
        if (data?.version?.stableVersion > APP_VERSION) {
          document.location.reload();
        }
        if (data?.version?.forceVersion > APP_VERSION) {
          let initialState: any = {
            message: 'GLOBAL.mandatoryUpdateAvailable',
            callBackYes: () => {
              document.location.reload();
            },
          };
          this.modalRef = this.modalService.show(RefreshAppComponent, {
            class: 'modal-500 modal-dialog-centered ip-modal-unset',
            ignoreBackdropClick: true,
            keyboard: false,
            initialState,
          });
          return;
        }
        if (data?.version?.clearCacheVersion > APP_VERSION) {
          this.deleteDb('CachingDb').then(() => {
            document.location.reload();
          }).catch((error) => {
            console.error('Error deleting IndexedDB:', error);
            document.location.reload();
          });
        }
      });
  }

  openSupport() {
    if (!this.supportModal) {
      this.supportModal = this.modalService.show(SupportComponent, {
        class: 'modal-300 modal-dialog-centered',
      });

      this.supportModal.onHidden.subscribe(() => {
        this.supportModal = null;
      });
    }
  }

  connectWebSocket() {
    this.SignalRService.startConnection();
  }

  checkNotificationSupport() {
    if (
      'serviceWorker' in navigator &&
      ('PushManager' in window || 'Notification' in window)
    ) {
      this.requestNotificationPermission();
    } else {
      console.error('This browser does not support push notifications.');
    }
  }

  requestNotificationPermission() {
    if (Notification.permission === 'granted') {
      this.registerToken();
    } else if (Notification.permission !== 'denied') {
      Notification.requestPermission().then((permission: string) => {
        if (permission === 'granted') {
          this.registerToken();
          return;
        }
        this._notificationsService.alert('Notification permission denied.');
      });
    }
  }

  registerToken() {
    const storedToken = localStorage.getItem('fcmToken');
    const messaging = getMessaging();

    getToken(messaging, { vapidKey: environment.firebaseConfig.vapidKey })
      .then((currentToken: string | null) => {
        if (currentToken) {
          if (currentToken !== storedToken) {
            this.sendTokenToBackend(currentToken);
            localStorage.removeItem('fcmToken');
          }
        } else {
          console.error('No registration token available.');
        }
      })
      .catch((err) => {
        console.error('An error occurred while retrieving token: ', err);
      });
  }

  async sendTokenToBackend(token: string) {
    try {
      const fp = await FingerprintJS.load();
      const result = await fp.get();
      this.visitorId = result.visitorId;
      const userDetails = localStorage.getItem('userDetails');
      if (!userDetails)
        throw new Error('User details not found in localStorage.');
      const username = JSON.parse(userDetails).preferred_username;
      const tokenPayload = {
        token: token,
        username: username,
        deviceUDID: this.visitorId,
        isActive: true,
        isTablet: false,
        platform: 2,
      };

      const tokenResponse: any = await this.PnsService.sendToken(
        tokenPayload
      ).toPromise();
      if (tokenResponse.succeeded) {
        localStorage.setItem('fcmToken', token);
      }

      const deviceDetails: any = {
        operatingSystem: 2,
        osVersion: this.deviceService.getDeviceInfo().os_version,
        deviceModel: null,
        deviceUDID: this.visitorId,
        deviceManufacturer: this.deviceService.getDeviceInfo().device,
        deviceType: this.deviceService.getDeviceInfo().deviceType,
        browserName: this.deviceService.getDeviceInfo().browser,
        browserVersion: this.deviceService.getDeviceInfo().browser_version,
        deviceName: null,
        deviceTimeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        languageCode: navigator.language || null,
        isTablet: this.deviceService.isTablet(),
        isEmulator: false,
        isDebug: false,
        environment: 'Browser',
        countryCode: null,
        ipAddress: null,
        longitude: null,
        latitude: null,
      };

      const deviceResponse = await this.PnsService.deviceInfo(
        deviceDetails
      ).toPromise();
    } catch (error) {
      console.error('Error sending token or fetching visitor ID:', error);
    }
  }

  handleGlobalSettings(data: any) {
    if (Object.keys(data).length) {
      this.store.dispatch(
        new UpdateLeadCustomStatusEnabled(data?.isCustomStatusEnabled)
      );
      this.isCopyPasteEnabled = data?.isCopyPasteEnabled;
      this.isScreenshotEnabled = data?.isScreenshotEnabled;

      if (this.isCopyPasteEnabled) {
        this.renderer.removeClass(document.body, 'copy-prevent');
      } else {
        this.renderer.addClass(document.body, 'copy-prevent');
      }
    }
  }

  // createAnalyticsRecord() {
  //   const options = { method: 'GET' };
  //   fetch('https://api.ipify.org/?format=json', options)
  //     .then((response) => response.json())
  //     .then((response) => {
  //       let record: any = {};
  //       record.ipAddress = response.ip;
  //       record = {
  //         ...record,
  //         device: this.deviceService.getDeviceInfo().device,
  //         deviceType: this.deviceService.getDeviceInfo().deviceType,
  //         os: this.deviceService.getDeviceInfo().os,
  //         osVersion: this.deviceService.getDeviceInfo().os_version,
  //         browser: this.deviceService.getDeviceInfo().browser,
  //         browserVersion: this.deviceService.getDeviceInfo().browser_version,
  //         orientation: this.deviceService.getDeviceInfo().orientation,
  //         isMobileApp: false,
  //         isWebApp: true,
  //         userAgent: navigator.userAgent,
  //         platform: navigator.platform,
  //       };
  //       if (navigator.geolocation) {
  //         navigator.geolocation.getCurrentPosition(
  //           (position) => {
  //             record = {
  //               ...record,
  //               latitude: String(position.coords.latitude),
  //               longitude: String(position.coords.longitude),
  //             };
  //             this.store.dispatch(new AddAnalyticsRecord(record));
  //           },
  //           () => {
  //             this.store.dispatch(new AddAnalyticsRecord(record));
  //           }
  //         );
  //       } else {
  //         this.store.dispatch(new AddAnalyticsRecord(record));
  //       }
  //     })
  //     .catch((err) => console.error(err));
  // }

  createDb() {
    if (!('indexedDB' in window)) {
      console.log('This browser does not support IndexedDB');
      return;
    }
    const allStores = INDEXEDDB_STORES;
    let request = window.indexedDB.open('CachingDb');

    request.onupgradeneeded = (event: any) => {
      const db = event.target.result;
      console.log(`Database upgrade from version ${event.oldVersion} to ${event.newVersion}`);

      allStores.forEach(storeName => {
        if (!db.objectStoreNames.contains(storeName)) {
          db.createObjectStore(storeName, { keyPath: 'id' });
        }
      });
    };

    request.onsuccess = (event: any) => {
      const db = event.target.result;
      db.close();
    };

    request.onerror = (event: any) => {
      console.error('❌ IndexedDB CachingDb creation error:', event.target.error);
    };

    request.onblocked = () => {
      console.warn('⚠️ Database upgrade blocked - please close other tabs with this application');
    };
  }


  deleteDb(dbName: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const deleteRequest = indexedDB.deleteDatabase(dbName);

      deleteRequest.onsuccess = () => {
        console.log(`[IndexedDB] Database "${dbName}" deleted successfully.`);
        resolve();
      };

      deleteRequest.onerror = (event: any) => {
        console.error(`[IndexedDB] Failed to delete "${dbName}".`, event);
        reject(deleteRequest.error);
      };

      deleteRequest.onblocked = () => {
        console.warn(`[IndexedDB] Delete blocked: Please close all open connections to "${dbName}".`);
      };
    });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
