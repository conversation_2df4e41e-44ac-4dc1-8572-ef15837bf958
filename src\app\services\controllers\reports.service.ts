import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
import { CommonService } from '../shared/common.service';
@Injectable({
    providedIn: 'root',
})
export class ReportsService extends BaseService<any> {
    public page: number;
    public count: number;
    serviceBaseUrl: string;
    constructor(private http: HttpClient, private commonService: CommonService) {
        super(http);
        this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
    }

    getResourceUrl(): string {
        return 'report';
    }

    exportUserStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/status/export/email`, payload);
    }

    customExportUserStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/status/export/custom`, payload);
    }


    exportProjectStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/project/status/export/email`, payload);
    }

    exportSourceStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/source/status/export/email`, payload);
    }

    exportCustomSourceStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/source/status/export/custom`, payload);
    }

    exportCustomProjectStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/project/status/export/custom`, payload);
    }

    exportSubSourceStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/subsource/status/export/email`, payload);
    }

    exportCustomSubSourceStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/subsource/status/export/custom`, payload);
    }

    exportAgencyStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/agency/status/export/email`, payload);
    }

    exportCustomAgencyStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/agency/status/export/custom`, payload);
    }

    exportUserMeetingandvisit(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/meetingandvisit/export/email`, payload);
    }

    exportReceivedDate(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/datewisesource/export/email`, payload);
    }

    exportActivity(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/activity/export/email`, payload);
    }

    exportSubReport(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/substatus/bysubsource/export/email`, payload);
    }

    exportSubStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/substatus/export/email`, payload);
    }

    getReportsExportStatus(payload: any, pageNumber: number, pageSize: number) {
        return this.http.get(`${this.serviceBaseUrl}/export/tracker?TrackerPermission=${payload?.trackerPermission}&PageNumber=${pageNumber}&PageSize=${pageSize}`);
    }

    exportProjectSubstatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/project/bysubstatus/export/email`, payload);
    }

    exportCallStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/call-log/export/email`, payload);
    }

    getFlagsCounts(payload: any) {
        return this.commonService.getModuleListByAdvFilter(payload);
    }

    exportUserSource(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/uservssource/export/email`, payload);
    }

    exportUserSubSource(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/uservssubsource/export/email`, payload);
    }

    exportUserMeetingSite(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/user/meetingandvisit/newlevel/export/email`, payload);
    }

    exportCityReport(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/cityvslead/export/email`, payload);
    }

    // ------------------------ REPORT AUTOMATION -------------------------

    existReportAutomation(reportAutomationName: string) {
        return this.http.get(
            `${this.serviceBaseUrl}/reportconfig-exist?displayName=${reportAutomationName}`
        );
    }

    getReportAutomationType() {
        return this.http.get(`${this.serviceBaseUrl}/reporttypes`);
    }

    getExportReportAutomationStatus(pageNumber: number, pageSize: number) {
        return this.http.get(`${this.serviceBaseUrl}/reportconfiguration/export/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`);
    }

    addReportAutomation(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/reportconfig`, payload);
    }

    updateReportAutomation(payload: any) {
        return this.http.put(`${this.serviceBaseUrl}/reportconfig`, payload);
    }

    deleteReportAutomation(ids: any) {
        return this.http.delete(`${this.serviceBaseUrl}/reportconfig?Ids=${ids}`);
    }

    disableReportAutomation(data: any) {
        return this.http.delete(`${this.serviceBaseUrl}/reportconfig?Ids=${data?.id}&DeleteOnlyJob=${data?.isEnabled}`);
    }

    fetchUserBaseOnRole(RoleIds: any) {
        const roleParams = RoleIds.map((id: any) => `RoleIds=${id}`).join('&');
        return this.http.get(`${env.baseURL}${env.apiURL}/user/usersbyroleids?${roleParams}`);
    }

    getReportAutomationById(id: any) {
        return this.http.get(`${this.serviceBaseUrl}/reportconfig/${id}`);
    }

    updateReportAutomationById(id: any, payload: any) {
        return this.http.put(`${this.serviceBaseUrl}/reportconfig/${id}`, payload);
    }

    getUserWithRole() {
        return this.http.get(`${env.baseURL}${env.apiURL}/user/getalluserswithroles`);
    }
    // --------------------- END REPORRT AUTOMATION -----------------------

    exportCampaignSubStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/campaign/bysubstatus/export/email`, payload);
    }

    exportCpSubStatus(payload: any) {
        return this.http.post(`${this.serviceBaseUrl}/channelpartner/bysubstatus/export/email`, payload);
    }
}
