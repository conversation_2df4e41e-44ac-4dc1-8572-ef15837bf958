import { Injectable } from '@angular/core';
import {
  preventClickjacking,
  detectFraming,
  frameBusting,
  initializeClickjackingProtection
} from '../utils/common.util';
import { SecurityConfigService } from './security-config.service';

@Injectable({
  providedIn: 'root'
})
export class ClickjackingPreventionService {
  private monitoringInterval: any;

  constructor(private securityConfig: SecurityConfigService) {
    this.initializeProtection();
  }

  /**
   * Initialize clickjacking protection
   */
  private initializeProtection(): void {
    const config = this.securityConfig.getConfig();
    if (config.clickjackingProtection.enabled) {
      initializeClickjackingProtection();
      this.startContinuousMonitoring();
    }
  }

  /**
   * Enable clickjacking protection
   */
  enableProtection(): void {
    this.securityConfig.enableClickjackingProtection();
    this.initializeProtection();
  }

  /**
   * Disable clickjacking protection (use with caution)
   */
  disableProtection(): void {
    this.securityConfig.disableClickjackingProtection();
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
  }

  /**
   * Check if the application is currently being framed
   */
  isFramed(): boolean {
    return detectFraming();
  }

  /**
   * Manually trigger frame busting
   */
  bustFrame(): void {
    const config = this.securityConfig.getConfig();
    if (config.clickjackingProtection.enabled) {
      frameBusting();
    }
  }

  /**
   * Set allowed origins for framing (for SAMEORIGIN policy)
   */
  setAllowedOrigins(origins: string[]): void {
    this.securityConfig.setAllowedOrigins(origins);
  }

  /**
   * Check if current origin is allowed
   */
  isOriginAllowed(origin: string): boolean {
    return this.securityConfig.isOriginAllowed(origin);
  }

  /**
   * Start continuous monitoring for framing attempts
   */
  private startContinuousMonitoring(): void {
    const config = this.securityConfig.getConfig();
    this.monitoringInterval = setInterval(() => {
      if (config.clickjackingProtection.enabled && this.isFramed()) {
        console.warn('Clickjacking attempt detected - redirecting to break frame');
        preventClickjacking();
      }
    }, config.clickjackingProtection.monitoringInterval);
  }

  /**
   * Get security headers for HTTP requests
   */
  getSecurityHeaders(): { [key: string]: string } {
    const config = this.securityConfig.getConfig();
    return {
      'X-Frame-Options': this.securityConfig.getXFrameOptionsValue(),
      'Content-Security-Policy': this.securityConfig.getCSPHeaderValue(),
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': config.additionalHeaders.referrerPolicy
    };
  }

  /**
   * Get X-Frame-Options header value based on configuration
   */
  getXFrameOptionsHeader(): string {
    return this.securityConfig.getXFrameOptionsValue();
  }

  /**
   * Get Content Security Policy for frame protection
   */
  getFrameCSPHeader(): string {
    return `frame-ancestors ${this.securityConfig.getFrameAncestorsDirective()}`;
  }

  /**
   * Destroy the service and clean up resources
   */
  ngOnDestroy(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
  }
}
