import { Component, EventEmitter, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { filter, takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { ContactType, MarketingType, PropertyDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, getAssignedToDetails, getPages, getTimeZoneDate, onFilterChanged, patchTimeZoneDate, setTimeZoneDate } from 'src/app/core/utils/common.util';
import { DeleteCampaign, FetchCampaignName, UpdateCampaignFiltersPayload } from 'src/app/reducers/manage-marketing/marketing.action';
import { getCampaignFiltersPayload, getCampaignNameList, getCampaignNameListIsLoading, getIsBulkCampaignLoading } from 'src/app/reducers/manage-marketing/marketing.reducer';
import { FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { CampaignActionComponent } from './campaign-action/campaign-action/campaign-action.component';
import { CampaignNameAdvanceFilterComponent } from './campaign-name-advance-filter/campaign-name-advance-filter/campaign-name-advance-filter.component';

@Component({
  selector: 'campaign-name',
  templateUrl: './campaign-name.component.html',
})
export class CampaignNameComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  PageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    PageNumber: 1,
    PageSize: 10,
  };
  appliedFilter: any = {};
  showFilters: boolean = false;
  showLeftNav: boolean = true;
  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;
  columns: any[];
  defaultColumns: any[];
  allCampaignData: any;
  getPages = getPages;
  onFilterChanged = onFilterChanged
  allUsers: any;
  allUsersList: any[];
  campaignListIsLoading: any;

  isBulkDeleteLoading: boolean;
  fetchCalled: any;
  userData: any;
  toDate: any = new Date();
  fromDate: any = new Date();

  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private shareDataService: ShareDataService,
    private modalService: BsModalService,
    private router: Router
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
    this.gridSettings();
  }

  ngOnInit(): void {
    this.store.dispatch(new FetchUsersListForReassignment());
    this.selectedPageSize = 10;
    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const sortedUsers = data?.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
        this.allUsersList = [...this.allUsers];
        this.allUsersList = assignToSort(this.allUsersList, '');
      });

    this.store
      .select(getCampaignFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.PageNumber - 1;
        this.PageSize = this.filtersPayload?.PageSize
        this.selectedPageSize = this.filtersPayload?.PageSize
        this.appliedFilter = {
          ...this.appliedFilter,
          PageNumber: this.filtersPayload?.PageNumber,
          PageSize: this.filtersPayload?.PageSize,
          SearchText: this.filtersPayload?.SearchText,
        };
      });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.appliedFilter = {
      ...this.appliedFilter,
      CampaignNames: this.filtersPayload?.CampaignNames,
      AssociatedLeadsCount: this.filtersPayload?.AssociatedLeadsCount,
      AssociatedPospectsCount: this.filtersPayload?.AssociatedPospectsCount,
      AssociateProperty: this.filtersPayload?.AssociateProperty,
      date: [patchTimeZoneDate(this.filtersPayload?.FromDate, this.userData?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(this.filtersPayload?.ToDate, this.userData?.timeZoneInfo?.baseUTcOffset)],
      DateType: PropertyDateType[this.filtersPayload?.DateType],
      ModifiedBy: this.filtersPayload?.ModifiedBy,
    };

    this.filterFunction();
    this.store
      .select(getCampaignNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allCampaignData = data;
        if (data?.itemsCount === 0 && !this.fetchCalled) {
          this.store.dispatch(
            new FetchCampaignName({
              ...this.filtersPayload,
              path: 'v1/marketing/campaigns',
              PageNumber: 1,
            })
          );
          this.currOffset = 0;
          this.fetchCalled = true;
          setTimeout(() => {
            this.fetchCalled = false;
          }, 1000);
        }
      });

    this.store
      .select(getCampaignNameListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.campaignListIsLoading = loading;
      });
  }

  gridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned mt-8',
        maxWidth: 50,
        suppressMovable: true,
        lockPosition: 'left',
      },
      {
        headerName: 'Campaign Name',
        field: 'Campaign Name',
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        // minWidth: 310,
        wrapText: true,
        autoHeight: true,
        suppressMovable: true,
        lockPosition: 'left',
        valueGetter: (params: any) => params.data?.name || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--'
          return `<p class="mt-20 text-truncate-1 break-all">${value}</p>`;
        },
      },
      {
        headerName: 'Associate Lead',
        field: 'Associate Lead',
        valueGetter: (params: any) => params.data?.leadsCount || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--'
          return `<p>${value}</p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const clickedElement = event?.event?.target;
          if (clickedElement.tagName !== 'P') {
            return;
          }

          const campaignName = event?.data?.name;
          const leadsCount = event?.data?.leadsCount;
          if (!campaignName || !leadsCount) return;
          const isCtrlClick = event?.event?.ctrlKey;
          const navigateTo = () => {
            this.router.navigate(['leads', 'manage-leads'], {
              queryParams: {
                CampaignNames: JSON.stringify([campaignName]),
                isNavigatedFromMarketing: true,
              },
            });
          };
          if (isCtrlClick) {
            window.open(
              `leads/manage-leads?isNavigatedFromMarketing=true&CampaignNames=${encodeURIComponent(
                JSON.stringify([campaignName])
              )}`,
              '_blank'
            );
          } else {
            event.event.preventDefault();
            navigateTo();
          }
        },
      },
      {
        headerName: 'Associate Data',
        field: 'Associate Data',
        valueGetter: (params: any) => params.data?.prospectCount || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--'
          return `<p>${value}</p>`;
        },
        cellClass: 'cursor-pointer',
        onCellClicked: (event: any) => {
          const clickedElement = event?.event?.target;
          if (clickedElement.tagName !== 'P') {
            return;
          }
          const campaignName = event?.data?.name;
          const prospectCount = event?.data?.prospectCount;
          if (!campaignName || !prospectCount) return;
          const isCtrlClick = event?.event?.ctrlKey;
          const navigateTo = () => {
            this.router.navigate(['data', 'manage-data'], {
              queryParams: {
                CampaignNames: JSON.stringify([campaignName]),
                isNavigatedFromMarketing: true,
              },
            });
          };
          if (isCtrlClick) {
            window.open(
              `data/manage-data?isNavigatedFromMarketing=true&CampaignNames=${encodeURIComponent(
                JSON.stringify([campaignName])
              )}`,
              '_blank'
            );
          } else {
            event.event.preventDefault();
            navigateTo();
          }
        },
      },
      {
        headerName: 'Template Name',
        field: 'Template Name',
        hide: true,
        valueGetter: (params: any) => params.data?.templateNames?.map((item: any) => item).join(', ') || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--'
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Contact Type',
        field: 'Contact Type',
        hide: true,
        valueGetter: (params: any) => params.data?.contactTypes?.map((item: any) => ContactType[item]) || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--'
          return `<p>${value}</p>`;
        },
      },
      {
        hide: true,
        minWidth: 195,
        headerName: 'Created',
        field: 'Created',
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.allUsersList, true) ||
          '',
          params.data?.createdOn
            ? getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ) +
            ' at ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
          <p class='fw-400'>${params.value[1]}</p>
                      <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        hide: true,
        minWidth: 195,
        headerName: 'Modified',
        field: 'Modified',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.allUsersList,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ) +
            ' at ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
                  <p class='fw-400'>${params.value[1]}</p>
                              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Actions',
        maxWidth: 110,
        filter: false,
        cellRenderer: CampaignActionComponent,
      },
    ];
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params?.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
  }

  assignCount() {
    this.PageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: 1,
    };
    this.store.dispatch(new UpdateCampaignFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchCampaignName(this.filtersPayload));
    this.currOffset = 0;
  }

  hiddenKeys: string[] = ['PageSize', 'PageNumber'];
  hasValue(value: any): boolean {
    if (value === null || value === undefined) {
      return false;
    }
    const stringValue = String(value);
    return stringValue.trim().length > 0;
  }

  isKeyVisible(key: string): boolean {
    return !this.hiddenKeys.includes(key);
  }

  getArrayOfFilters(key: string, values: string | string[]) {
    if (
      ['PageSize', 'PageNumber', 'SearchText'].includes(key) ||
      values?.length === 0
    ) {
      return [];
    } else if (key === 'date' && Array.isArray(values) && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(new Date(values[0]), this.userData?.timeZoneInfo?.baseUTcOffset);
        this.fromDate = setTimeZoneDate((new Date(values[1])), this.userData?.timeZoneInfo?.baseUTcOffset);
        const formattedToDate = getTimeZoneDate(this.toDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear');
        const formattedFromDate = getTimeZoneDate(this.fromDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear')
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return [];
      }
    } else if (typeof values === 'string') {
      return values?.toString()?.split(',');
    }
    return values;
  }

  onRemoveFilter(key: string, value: any) {
    if (key === 'DateType' || key === 'date') {
      this.appliedFilter['DateType'] = null;
      this.appliedFilter['date'] = null;
    }
    else if (Array.isArray(this.appliedFilter[key])) {
      const valueToRemove = value.toString().trim().toLowerCase();
      this.appliedFilter[key] = this.appliedFilter[key]?.filter((item: any) => {
        return item.toString().trim().toLowerCase() !== valueToRemove;
      });
    } else if (
      typeof this.appliedFilter[key] === 'string' ||
      typeof this.appliedFilter[key] === 'number' ||
      typeof this.appliedFilter[key] === 'boolean'
    ) {
      this.appliedFilter[key] = null;
    }
    this.filterFunction();
  }

  filterFunction() {
    if (
      this.appliedFilter?.CampaignNames ||
      this.appliedFilter?.AssociatedLeadsCount ||
      this.appliedFilter?.AssociatedPospectsCount ||
      this.appliedFilter?.AssociateProperty ||
      this.appliedFilter?.CreatedBy ||
      this.appliedFilter?.DateType ||
      this.appliedFilter?.FromDate ||
      this.appliedFilter?.date?.[0] ||
      this.appliedFilter?.ModifiedBy
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }

    let leadsCounts = this.appliedFilter?.AssociatedLeadsCount?.split('-');
    let prospectsCounts =
      this.appliedFilter?.AssociatedPospectsCount?.split('-');

    let LeadsMinCount =
      this.appliedFilter?.AssociatedLeadsCount === 'above 100'
        ? 100
        : leadsCounts
          ? leadsCounts[0]
          : undefined;
    let LeadsMaxCount =
      this.appliedFilter?.AssociatedLeadsCount === 'above 100'
        ? null
        : leadsCounts
          ? leadsCounts[1]
          : undefined;

    let ProspectMinCount =
      this.appliedFilter?.AssociatedPospectsCount === 'above 100'
        ? 100
        : prospectsCounts
          ? prospectsCounts[0]
          : undefined;
    let ProspectMaxCount =
      this.appliedFilter?.AssociatedPospectsCount === 'above 100'
        ? null
        : prospectsCounts
          ? prospectsCounts[1]
          : undefined;

    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: this.appliedFilter?.PageNumber,
      PageSize: this.appliedFilter?.PageSize,
      SearchText: this.searchTerm,
      CampaignNames: this.appliedFilter?.CampaignNames,
      LeadsMinCount,
      LeadsMaxCount,
      ProspectMinCount,
      ProspectMaxCount,
      AssociatedLeadsCount: this.appliedFilter?.AssociatedLeadsCount,
      AssociatedPospectsCount: this.appliedFilter?.AssociatedPospectsCount,
      AssociateProperty: this.appliedFilter?.AssociateProperty,
      DateType: PropertyDateType[this.appliedFilter?.DateType],
      CreatedBy: this.appliedFilter?.CreatedBy,
      FromDate: setTimeZoneDate(this.appliedFilter?.date?.[0], this.userData?.timeZoneInfo?.baseUTcOffset),
      ToDate: setTimeZoneDate(this.appliedFilter.date?.[1], this.userData?.timeZoneInfo?.baseUTcOffset),
      ModifiedBy: this.appliedFilter?.ModifiedBy,
    };

    this.store.dispatch(new UpdateCampaignFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchCampaignName(this.filtersPayload));

    this.currOffset = 0;
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: e + 1,
    };
    this.store.dispatch(new UpdateCampaignFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchCampaignName(this.filtersPayload));
  }

  deselectOptions() {
    let selectedNodes = this.gridApi?.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  openBulkDeleteModal(bulkDeleteModal: any) {
    this.modalRef = this.modalService.show(bulkDeleteModal, {
      class: 'right-modal modal-500',
    });
  }

  openConfirmDeleteModal(name: string, id: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: name,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeCampaign(id);
        }
      });
    }
  }

  removeCampaign(id: string): void {
    const selectedNodes = this.gridApi?.getSelectedNodes();
    const selectedNode = selectedNodes?.find(
      (lead: any) => lead?.data?.id === id
    );
    if (selectedNode) {
      this.gridApi?.deselectNode(selectedNode);
    }
    const remainingSelectedNodes = this.gridApi?.getSelectedNodes();
    if (!remainingSelectedNodes || remainingSelectedNodes.length === 0) {
      this.modalService.hide();
    }
  }

  deleteCampaign() {
    let initialState: any = {
      type: 'manageMarketingDelete',
      data: {
        buttonContent: 'Delete',
        fieldType: 'Delete',
        heading: `Deleting Campaign(s)?`,
        bulkMessage: `Are you sure you want to delete the <b>"${this.gridApi?.getSelectedNodes()?.length}"</b> selected campaigns?`,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.isClickYesOrNo(true)
        }
      });
    }
  }

  exportCampaign() {
    let { PageNumber, PageSize, ...filteredPayload } = this.filtersPayload;
    let initialState: any = {
      payload: {
        ...filteredPayload,
        exportType: MarketingType.CampaignName,
        path: 'campaign',
      },

      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };

    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  isClickYesOrNo(isClick: boolean) {
    if (isClick) {
      this.isBulkDeleteLoading = true;
      let selectedNodes = this.gridApi?.getSelectedNodes();
      let selectedIds = selectedNodes?.map((node: any) => node.data?.id);
      this.store.dispatch(new DeleteCampaign(selectedIds));
      const loadingSubscription = this.store
        .select(getIsBulkCampaignLoading)
        .pipe(filter((isLoading: boolean) => !isLoading))
        .subscribe(() => {
          this.isBulkDeleteLoading = false;
          this.modalService.hide();
          this.store.dispatch(
            new UpdateCampaignFiltersPayload(this.filtersPayload)
          );
          this.store.dispatch(new FetchCampaignName(this.filtersPayload));
        });
    }
    this.modalRef.hide()
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.filterFunction();
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTerm = null;
      this.filterFunction();
    }
  }

  openAdvFiltersModal() {
    let initialState: any = {
      class: 'modal-600 modal-dialog tb-modal-unset',
      initialState: {
        applyAdvancedFilter: this.applyAdvancedFilter.bind(this),
        onClearAllFilters: this.onClearAllFilters.bind(this),
        appliedFilter: this.appliedFilter,
      },
    };
    const modalRef = this.modalService.show(
      CampaignNameAdvanceFilterComponent,
      initialState
    );
  }

  toggleColumns(params: any): void {
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.userProvidedColDef?.headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(2, this.columns?.length - 1)
      .sort((a: any, b: any) => a?.label?.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myCampaignColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage
      .getItem('manage-campaign-columns')
      ?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-campaign-columns', colData?.toString())
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem(
      'myCampaignColumnState',
      JSON.stringify(
        columnState.map((column: any) => ({
          ...column,
          sort: null,
        }))
      )
    );
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  onClearAllFilters(data: string) {
    if (this.appliedFilter && typeof this.appliedFilter === 'object') {
      Object.keys(this.appliedFilter).forEach((key) => {
        if (key !== 'PageNumber' && key !== 'PageSize') {
          this.appliedFilter[key] = null;
        }
      });
    }

    this.filterFunction();
  }

  applyAdvancedFilter() {
    this.filterFunction();
    this.modalService.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
