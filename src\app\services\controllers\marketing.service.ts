import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class ManageMarketingService extends BaseService<any> {
  serviceBaseUrl: string;

  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'marketing';
  }

  // ------------------- Agency Part ----------------------
  // getAgencyNameList(): Observable<any[]> {
  //   return this.http.get<any[]>(
  //     `${this.serviceBaseUrl}`
  //   );
  // }
  getAgencyLocation(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/agency/addresses`);
  }

  addAgencyName(data: any): Observable<any> {
    return this.http.post(`${this.serviceBaseUrl}/agency`, data);
  }

  deleteAgency(id: string) {
    const ids = {
      body: {
        ids: id,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/agency`, ids);
  }

  updateAgencyName(data: any): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/agency`, data);
  }

  uploadMarketingExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/excel`, formData);
  }

  getAgencyExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/agency/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  uploadAgencyMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/agency/batch`, payload);
  }

  exportAgency(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/agency/export/email`,
      payload
    );
  }

  getAgencyExportStatus(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/agency/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  existAgencyName(agencyName: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/agency/names?agency=${agencyName}`
    );
  }

  // ------------------- Campaign Part ----------------------

  addCampaignName(data: any): Observable<any> {
    return this.http.post(`${this.serviceBaseUrl}/campaigns`, data);
  }

  updateCampaignName(data: any): Observable<any> {
    return this.http.put(`${this.serviceBaseUrl}/campaigns`, data);
  }

  deleteCampaign(id: any) {
    const ids = {
      body: {
        ids: id,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/campaigns`, ids);
  }

  exportCampaign(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/campaign/export/email`,
      payload
    );
  }

  getCampaignExportStatus(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/campaign/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  uploadCampaignExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/bulk`, formData);
  }

  getCampaignExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/campaign/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  uploadCampaignMappedColumns(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/campaign/batch`, payload);
  }

  existCampaignName(name: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/camapaign/names?campaign=${name}`
    );
  }

  // ------------------- Channel Partner Part ----------------------

  addChannelPartner(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/channelpartner`, payload);
  }

  getChannelPartnerLocation(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/channelpartner/addresses`);
  }

  updateChannelPartner(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/channelpartner`, payload);
  }

  deleteChannelPartner(id: string) {
    const ids = {
      body: {
        ids: id,
      },
    };
    return this.http.delete(`${this.serviceBaseUrl}/channelpartner`, ids);
  }

  uploadChannelPartnerExcel(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${this.serviceBaseUrl}/bulk`, formData);
  }

  getChannelPartnerExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/channelpartners/bulk/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  uploadChannelPartnerMappedColumns(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/channelpartners/batch`,
      payload
    );
  }

  exportCP(payload: any) {
    return this.http.post(
      `${this.serviceBaseUrl}/channelpartners/export/email`,
      payload
    );
  }

  getCPExportStatus(pageNumber: number, pageSize: number) {
    return this.http.get(
      `${this.serviceBaseUrl}/channelpartners/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`
    );
  }

  existChannelPartnerName(CPName: string) {
    return this.http.get(
      `${this.serviceBaseUrl}/channelpartner/names?channelPartner=${CPName}`
    );
  }

  getCreatedByUsers(): Observable<any> {
    return this.http.get(`${this.serviceBaseUrl}/channelpartner/createdbyusers`);
  }
}
