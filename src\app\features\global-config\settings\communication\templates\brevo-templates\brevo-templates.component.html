<div class="d-flex min-w-0 position-absolute top-20 right-30 ph-position-relative ph-ntop-12 ph-left-12">
    <button class="btn-coal min-w-0" (click)="syncBrevoTemplates()">
        <span class="ic-menu icon ic-sm"></span>
        <span class="ml-8 ip-d-none">Sync</span>
    </button>
    <button class="btn-coal ml-10 min-w-0" *ngIf="canTemplatesCreate" (click)="openAddModal()">
        <span class="ic-add icon ic-sm"></span>
        <span class="ml-8 ip-d-none">Add Template</span>
    </button>

</div>
<div [formGroup]="brevoForm">
    <div class="bg-white w-100 border-gray flex-between mb-3">
        <div autocomplete="off" class="align-center border-end  w-100 py-10 px-12 no-validation">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
            <input placeholder="type template name" name="searchQrForm" class="border-0 outline-0 w-100"
                autocomplete="off" id="inpSearchQrForm" formControlName="searchTerm" (keydown.enter)="filterFunction()"
                (input)="isEmptyInput($event)">
            <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate }})</small>
        </div>
        <div class="show-dropdown-white align-center position-relative ip-br-0">
            <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                    {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
            <ng-select [items]="showEntriesSize" formControlName="pageSize" (change)="assignPageSize()"
                [virtualScroll]="true" [placeholder]="currPageSize" class="w-150 tb-w-120px" [searchable]="false">
            </ng-select>
        </div>
    </div>
</div>
<div *ngIf="!isTemplateLoading; else loader">
    <div class="scrollbar scroll-hide tb-w-100-60 table-scrollbar">
        <table class="table standard-table no-vertical-border" *ngIf="brevoTemplates?.length; else noData">
            <thead>
                <tr class="w-100 text-nowrap">
                    <!-- <th class="w-50px">{{'GLOBAL.s-no' | translate}}</th> -->
                    <th class="w-150">Template Name</th>
                    <th class="w-210">Subject Line</th>
                    <th class="w-210">Associated Account</th>
                    <th class="w-210">Template Status</th>
                    <th class="w-110">Actions</th>
                </tr>
            </thead>
            <ng-container>
                <tbody class="text-secondary fw-semi-bold max-h-100-300">
                    <tr *ngFor="let data of brevoTemplates; index as srNo">
                        <!-- <td class="w-50px">{{srNo+ 1}}</td> -->
                        <td class="w-150">{{ data.name }}</td>
                        <td class="w-210">{{ data.subject }}</td>
                        <td class="w-210">{{ data.additionalProperties?.senderEmailAddress }}</td>
                        <td class="w-210"> <span class="status-label-badge"
                                [ngStyle]="{'background-color': data?.additionalProperties?.brevoTemplateStatus  ? '#63AEFF66' : '#FECCCC66'}">
                                <p class="mr-6"
                                    [ngStyle]="{'color': data?.additionalProperties?.brevoTemplateStatus  ? '#007AFF' : '#ED5454'}">
                                    {{ data?.additionalProperties?.brevoTemplateStatus ? 'Completed' : 'Not Completed'}}
                                </p>
                            </span></td>
                        <td class="w-110">
                            <div class="align-center">
                                <div title="Edit" class="bg-accent-green icon-badge" *ngIf="canTemplatesUpdate"
                                    (click)="openEditModal(data)">
                                    <span class="icon m-auto ic-xxs ic-pen"></span>
                                </div>
                                <div title="Delete" class="bg-light-red icon-badge" *ngIf="canTemplatesDelete"
                                    (click)="openDeleteModal(data.id, delete)">
                                    <span class="icon ic-delete m-auto ic-xxs"></span>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </ng-container>

        </table>


    </div>
    <div class="mt-16  flex-end" *ngIf="brevoTemplates?.length>0">
        <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*currPageSize + 1}}
            {{ 'GLOBAL.to-small' | translate }} {{currOffset*currPageSize + brevoTemplates?.length}}
            {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
        <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,currPageSize)'
            (pageChange)="onPageChange($event)">
        </pagination>
    </div>
</div>

<ng-template #loader>
    <div class="flex-center h-100-270">
        <application-loader></application-loader>
    </div>
</ng-template>
<ng-template #noData>
    <div class="flex-col flex-center h-100-260 min-h-250">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">No Data Found</div>
    </div>
</ng-template>

<ng-template #delete>
    <div class="p-20">
        <h4 class="text-black-100 fw-semi-bold mb-20 text-center word-break line-break">Are you sure you want to delete
            this Template?</h4>
        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: Deleted template cannot be retrieved
            again.
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" id="deleteNo" data-automate-id="deleteNo" (click)="modalRef.hide()">
                {{ 'GLOBAL.no' | translate }}</button>
            <button class="btn-green" id="deleteYes" data-automate-id="deleteYes"
                (click)="modalService.setDismissReason('confirmed');modalRef.hide()">
                {{ 'GLOBAL.yes' | translate }}</button>
        </div>
    </div>
</ng-template>