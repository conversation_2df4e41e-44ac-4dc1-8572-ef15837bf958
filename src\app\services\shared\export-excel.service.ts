import { EventEmitter, Injectable } from '@angular/core';
import * as ExcelJS from 'exceljs';
import { changeCalendar, getStandardTimeName, getSystemTimeOffset, getSystemTimeZoneId, getTenantName, getTimeZoneDate } from 'src/app/core/utils/common.util';
import { FolderNamesS3, OwnerSelectionType, ReportDateType, UserStatus } from 'src/app/app.enum';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { takeUntil } from 'rxjs';
import { getUserBasicDetails, getUsersListForReassignment, getUsersListForReassignmentIsLoading } from 'src/app/reducers/teams/teams.reducer';
import { AppState } from 'src/app/app.reducer';
import { Store } from '@ngrx/store';
import { HeaderTitleService } from './header-title.service';

@Injectable({ providedIn: 'root' })
export class ExportExcelService {
    private stopper: EventEmitter<void> = new EventEmitter<void>();
    allUsers: any[] = [];
    getTimeZoneDate = getTimeZoneDate;
    isAllUsersLoading: boolean = true;
    currentDate: Date = new Date();
    userData: any;

    constructor(private s3UploadService: BlobStorageService,
        private store: Store<AppState>,
        private headerTitle: HeaderTitleService,


    ) {
        this.store
            .select(getUserBasicDetails)
            .pipe(takeUntil(this.stopper))
            .subscribe((data: any) => {
                this.userData = data;
                this.currentDate = changeCalendar(
                    this.userData?.timeZoneInfo?.baseUTcOffset
                );
            });
        this.store
            .select(getUsersListForReassignment)
            .pipe(takeUntil(this.stopper))
            .subscribe((data: any) => {
                this.allUsers = (data || []).map((user: any) => ({
                    ...user,
                    fullName: user.firstName + ' ' + user.lastName,
                }));
                this.allUsers = this.allUsers.sort((a: any, b: any) => a.fullName.localeCompare(b.fullName));
            });
        this.store
            .select(getUsersListForReassignmentIsLoading)
            .pipe(takeUntil(this.stopper))
            .subscribe((isLoading: boolean) => {
                this.isAllUsersLoading = isLoading;
            });
    }

    formatFilterValue(key: string, values: any): string[] {
        if (!key) return [];

        // Only keys actually present in report filter forms/payloads
        const userKeys = [
            'UserIds',
        ];
        const dateTypeKeys = [
            'dateType'
        ];

        const DateKeys = [
            'fromDate', 'toDate', 'toDateForAgency', 'fromDateForAgency', 'callLogFromDate', 'callLogToDate', 'fromDateForProject', 'toDateForProject', 'fromDateForLeadReceived', 'toDateForLeadReceived', 'fromDateForSource', 'toDateForSource', 'fromDateForSubSource', 'toDateForSubSource', 'toDateForMeetingOrVisit', 'fromDateForMeetingOrVisit'
        ];
        const userStatusKey = [
            'userStatus'
        ]

        const ownerTypeKey = [
            'ownerSelection'
        ]
        // OwnerSelectionType
        // User name mapping (exact key match)
        if (userKeys.includes(key)) {
            const arr = Array.isArray(values) ? values : [values];
            return arr.map((id: string) => this.getUserDisplayName(id));
        }

        // DateType enum mapping (exact key match)
        if (dateTypeKeys.includes(key)) {
            const label = ReportDateType[values];
            return [label];
        }

        if (ownerTypeKey.includes(key)) {
            const label = OwnerSelectionType[values];
            return [label];
        }

        if (userStatusKey.includes(key)) {
            const label = UserStatus[values];
            return [label];
        }


        if (DateKeys.includes(key) && values) {
            const arr = Array.isArray(values) ? values : [values];
            return arr.map((v: string) => getTimeZoneDate(new Date(v), this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear'));
        }

        if (Array.isArray(values)) {
            return values.map(v => v && v.toString()).filter(Boolean);
        }

        return [values?.toString()];
    }

    // Main export method, modeled after report-graph
    async exportToExcel(options: {
        reportType: string,
        payload: any,
        gridOptions: any,
        onExportFinished?: (s3BucketKey?: string) => void
    }) {
        const { reportType, payload, gridOptions, onExportFinished } = options;
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Report');
        const rowData = gridOptions?.rowData || [];
        // 1. Header info
        const headerFields = [
            { label: 'Created By:', value: this.userData?.firstName + ' ' + this.userData?.lastName },
            { label: 'Type:', value: this.headerTitle?.pageTitle?.getValue() },
            { label: 'Created On:', value: getTimeZoneDate(this.currentDate, this.userData?.timeZoneInfo?.baseUTcOffset, 'dayMonthYear') },
        ];
        let currentRow = 1;
        headerFields.forEach(field => {
            worksheet.getCell(`A${currentRow}`).value = field.label;
            worksheet.getCell(`A${currentRow}`).font = { bold: true };
            worksheet.getCell(`A${currentRow}`).alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            worksheet.getCell(`A${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
            worksheet.getCell(`A${currentRow}`).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            worksheet.getCell(`B${currentRow}`).value = field.value;
            worksheet.getCell(`B${currentRow}`).font = { bold: false };
            worksheet.getCell(`B${currentRow}`).alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
            worksheet.getCell(`B${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
            worksheet.getCell(`B${currentRow}`).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
            currentRow++;
        });

        if (payload) {
            const skipKeys = [
                'pageNumber', 'pageSize', 'path', 'isNavigatedFromReports', 'ReportPermission', 'ExportPermission',
                'FileName', 'timeZoneId', 'baseUTcOffset', 'reportType', 'totalCount'
            ];
            Object.keys(payload).forEach(key => {
                if (skipKeys.includes(key)) return;
                const values = payload[key];
                const arr = this.formatFilterValue(key, values) || [];
                const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                const filteredArr = arr.filter(v => v !== undefined && v !== null && v !== '');
                worksheet.getCell(`A${currentRow}`).value = `${label}:`;
                worksheet.getCell(`A${currentRow}`).font = { bold: true };
                worksheet.getCell(`A${currentRow}`).alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
                worksheet.getCell(`A${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
                worksheet.getCell(`A${currentRow}`).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                worksheet.getCell(`B${currentRow}`).value = filteredArr.length ? filteredArr.join(', ') : 'N/A';
                worksheet.getCell(`B${currentRow}`).font = { bold: false };
                worksheet.getCell(`B${currentRow}`).alignment = { wrapText: true, horizontal: 'center', vertical: 'middle' };
                worksheet.getCell(`B${currentRow}`).fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFD3D3D3' } };
                worksheet.getCell(`B${currentRow}`).border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                currentRow++;
            });
        }

        // 3. Title row
        worksheet.insertRow(currentRow, []);
        worksheet.mergeCells(`A${currentRow}:C${currentRow}`);
        const titleCell = worksheet.getCell(`A${currentRow}`);
        titleCell.value = this.headerTitle?.pageTitle?.getValue();
        titleCell.alignment = { horizontal: 'center', vertical: 'middle' };
        titleCell.font = { size: 16, bold: true };
        worksheet.getRow(currentRow).height = 28;
        currentRow++;

        // 4. Table (parent/child headers and data, with style and merged parent headers)
        // --- BEGIN: Exact report-graph logic ---
        const buildExcelDataWithTotals = () => {
            const allData = rowData || [];
            let allColumns = (gridOptions?.columnDefs || []);
            let selectedColumns: string[] = [];
            if (gridOptions?.selectedColumns && Array.isArray(gridOptions.selectedColumns) && gridOptions.selectedColumns.length > 0) {
                selectedColumns = gridOptions.selectedColumns;
            } else if (payload?.selectedColumns && Array.isArray(payload.selectedColumns) && payload.selectedColumns.length > 0) {
                selectedColumns = payload.selectedColumns;
            }
            if (selectedColumns.length > 0) {
                allColumns = allColumns.filter((col: any) => selectedColumns.includes(col.field) || selectedColumns.includes(col.colId));
            }
            const flatColumns: any[] = [];
            const parentHeaders: string[] = [];
            const childHeaders: string[] = [];
            let colIdx = 0;
            allColumns.forEach((col: any) => {
                if (col.children && col.children.length > 0) {
                    col.children.forEach((child: any) => {
                        if (child.valueLabels && Array.isArray(child.valueLabels)) {
                            child.valueLabels.forEach((label: string, idx: number) => {
                                parentHeaders.push(col.headerName || '');
                                childHeaders.push(label);
                                flatColumns.push({ ...child, parentName: col.headerName, displayName: label, valueIndex: idx });
                                colIdx++;
                            });
                        } else {
                            parentHeaders.push(col.headerName || '');
                            childHeaders.push(child.headerName || '');
                            flatColumns.push({ ...child, parentName: col.headerName });
                            colIdx++;
                        }
                    });
                } else if (col.valueLabels && Array.isArray(col.valueLabels)) {
                    col.valueLabels.forEach((label: string, idx: number) => {
                        parentHeaders.push('');
                        childHeaders.push(label);
                        flatColumns.push({ ...col, displayName: label, valueIndex: idx });
                        colIdx++;
                    });
                } else {
                    parentHeaders.push(col.headerName || '');
                    childHeaders.push(col.headerName || '');
                    flatColumns.push(col);
                    colIdx++;
                }
            });
            // Build data rows
            const data: any[] = [];
            allData.forEach((row: any) => {
                const rowData: any = {};
                flatColumns.forEach(col => {
                    if (col.field) {
                        let value = col?.valueGetter && typeof col.valueGetter === 'function'
                            ? col.valueGetter({ data: row })
                            : row?.[col?.field];
                            if (row && Object.prototype.hasOwnProperty.call(row, `${col.field}`)) {
                              value = row?.[col?.field];
                            }
                        if (Array.isArray(value)) {
                            const idx = typeof col.valueIndex === 'number' ? col.valueIndex : 0;
                            value = value[idx] ?? 0;
                        }
                        const key = (Array.isArray(col.valueLabels) && col.displayName)
                            ? `${col.field}_${col.displayName}`
                            : col.field;
                        rowData[key] = value ?? 0;
                    }
                });
                data.push(rowData);
            });
            // Calculate totals (simple sum for each column)
            const totalRow: any = {};
            flatColumns.forEach(col => {
                const key = (Array.isArray(col.valueLabels) && col.displayName)
                    ? `${col.field}_${col.displayName}`
                    : col.field;
                totalRow[key] = data.reduce((sum, row) => sum + (Number(row[key]) || 0), 0);
            });
            return { headers: [parentHeaders, childHeaders], data, totalRow, flatColumns };
        };
        const excelData = buildExcelDataWithTotals();
        if (excelData.headers[1].length === 0 || excelData.data.length === 0) return;
        let dataTableStartRow = currentRow;
        const parentHeaders = excelData.headers[0];
        const childHeaders = excelData.headers[1];
        const flatColumns = excelData.flatColumns || [];
        // --- HEADER ROWS ---
        // Ensure header rows are explicitly created with correct column count before cell assignment
        worksheet.getRow(dataTableStartRow).values = Array(flatColumns.length).fill('');
        worksheet.getRow(dataTableStartRow + 1).values = Array(flatColumns.length).fill('');
        let colIndex = 0;
        while (colIndex < parentHeaders.length) {
            const parent = parentHeaders[colIndex];
            const child = childHeaders[colIndex];
            // Find span for this parent
            let span = 1;
            for (let j = colIndex + 1; j < parentHeaders.length; j++) {
                if (parentHeaders[j] === parent) span++;
                else break;
            }
            const colLetterStart = this.getExcelColumnLetter(colIndex);
            const colLetterEnd = this.getExcelColumnLetter(colIndex + span - 1);
            // If parent and child are the same and not a group, leave parent row blank
            if (parent === child && span === 1) {
                worksheet.getRow(dataTableStartRow);
                const parentCell = worksheet.getCell(`${colLetterStart}${dataTableStartRow}`);
                parentCell.value = '';
                parentCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                parentCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
                parentCell.alignment = { horizontal: 'center', vertical: 'middle' };
                // No bottom border for parent when it's empty
                parentCell.border = { top: { style: 'thin', color: { argb: 'FFFFFFFF' } }, left: { style: 'thin', color: { argb: 'FFFFFFFF' } }, right: { style: 'thin', color: { argb: 'FFFFFFFF' } } };
                worksheet.getRow(dataTableStartRow + 1);
                const childCell = worksheet.getCell(`${colLetterStart}${dataTableStartRow + 1}`);
                childCell.value = child;
                childCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                childCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
                childCell.alignment = { horizontal: 'center', vertical: 'middle' };
                // No top border for child when parent is empty
                childCell.border = { left: { style: 'thin', color: { argb: 'FFFFFFFF' } }, bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } }, right: { style: 'thin', color: { argb: 'FFFFFFFF' } } };
                colIndex += span;
                continue;
            }
            if ((parent === '' || parent === undefined) && Array.isArray(flatColumns[colIndex]?.valueLabels)) {
                // Treat as no parent: leave parent row blank, do NOT merge cells
                for (let k = 0; k < span; k++) {
                    const parentColLetter = this.getExcelColumnLetter(colIndex + k);
                    const parentCell = worksheet.getCell(`${parentColLetter}${dataTableStartRow}`);
                    parentCell.value = '';
                    parentCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    parentCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
                    parentCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    parentCell.border = { top: { style: 'thin', color: { argb: 'FFFFFFFF' } }, left: { style: 'thin', color: { argb: 'FFFFFFFF' } }, right: { style: 'thin', color: { argb: 'FFFFFFFF' } } };
                    // Child header
                    worksheet.getRow(dataTableStartRow + 1);
                    const childCell = worksheet.getCell(`${parentColLetter}${dataTableStartRow + 1}`);
                    childCell.value = childHeaders[colIndex + k];
                    childCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                    childCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
                    childCell.alignment = { horizontal: 'center', vertical: 'middle' };
                    childCell.border = { left: { style: 'thin', color: { argb: 'FFFFFFFF' } }, bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } }, right: { style: 'thin', color: { argb: 'FFFFFFFF' } } };
                }
                colIndex += span;
                continue;
            }
            if (span > 1) {
                worksheet.mergeCells(`${colLetterStart}${dataTableStartRow}:${colLetterEnd}${dataTableStartRow}`);
            }
            worksheet.getRow(dataTableStartRow);
            const parentCell = worksheet.getCell(`${colLetterStart}${dataTableStartRow}`);
            parentCell.value = parent;
            parentCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
            parentCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
            parentCell.alignment = { horizontal: 'center', vertical: 'middle' };
            parentCell.border = { top: { style: 'thin', color: { argb: 'FFFFFFFF' } }, left: { style: 'thin', color: { argb: 'FFFFFFFF' } }, bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } }, right: { style: 'thin', color: { argb: 'FFFFFFFF' } } };
            for (let k = 0; k < span; k++) {
                worksheet.getRow(dataTableStartRow + 1);
                const childColLetter = this.getExcelColumnLetter(colIndex + k);
                const childCell = worksheet.getCell(`${childColLetter}${dataTableStartRow + 1}`);
                childCell.value = childHeaders[colIndex + k];
                childCell.font = { bold: true, color: { argb: 'FFFFFFFF' } };
                childCell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF000000' } };
                childCell.alignment = { horizontal: 'center', vertical: 'middle' };
                childCell.border = { top: { style: 'thin', color: { argb: 'FFFFFFFF' } }, left: { style: 'thin', color: { argb: 'FFFFFFFF' } }, bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } }, right: { style: 'thin', color: { argb: 'FFFFFFFF' } } };
            }
            colIndex += span;
        }
        // --- DATA ROWS ---
        if (excelData.data && excelData.data.length > 0 && flatColumns.length > 0) {
            excelData.data.forEach((row: any) => {
                const rowValues = flatColumns.map(col => {
                    const key = (Array.isArray(col.valueLabels) && col.displayName)
                        ? `${col.field}_${col.displayName}`
                        : col.field;
                    return this.getCellValue(row, key, col);
                });
                const isTotalRow = Object.values(row).some(val => val === 'Total');
                const excelRow = worksheet.addRow(rowValues);
                excelRow.eachCell((cell) => {
                    cell.alignment = { horizontal: 'center', vertical: 'middle' };
                    cell.border = { top: { style: 'thin' }, left: { style: 'thin' }, bottom: { style: 'thin' }, right: { style: 'thin' } };
                    if (isTotalRow) {
                        cell.font = { bold: true, size: 12, color: { argb: 'FF333333' } };
                        cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF00' } };
                    }
                });
            });
        }
        // --- COLUMN WIDTH ---
        for (let i = 0; i < flatColumns.length; i++) {
            worksheet.getColumn(i + 1).width = Math.max(
                15,
                parentHeaders[i]?.toString().length || 0,
                childHeaders[i]?.toString().length || 0
            ) + 5;
        }

        // Upload to S3
        const buffer = await workbook.xlsx.writeBuffer();
        const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const baseUTcOffset = this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset();
        const timeZoneId = this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId();
        const tenantName = getTenantName();
        const fileName = `${tenantName}_Export_${reportType}_Report_${getTimeZoneDate(new Date(), baseUTcOffset, 'fullDateTime')}_${getStandardTimeName(timeZoneId)}.xlsx`;
        const file = new File([blob], fileName, { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        const reader = new FileReader();
        reader.onload = () => {
            const base64String = (reader.result as string).split(',')[1];
            const bucketName = this.s3UploadService.bucketName;
            const folderName = FolderNamesS3.Reports;
            this.s3UploadService.uploadExcelBase64Anonymous([base64String], bucketName, folderName, fileName)
                .subscribe({
                    next: (response: any) => {
                        let s3BucketKey: any;
                        if (response.data?.length) {
                            s3BucketKey = (response.data[0]);
                        }
                        if (onExportFinished) onExportFinished(s3BucketKey);
                    },
                    error: (err: any) => {
                        console.error('[ExportExcelService] API upload error:', err);
                    }
                });
        };
        reader.readAsDataURL(file);
    }


    getUserDisplayName(userId: string) {
        let userName = '';
        this.allUsers?.forEach((user: any) => {
            if (userId === user.id) userName = `${user.fullName}`;
        });
        return userName;
    }

    getExcelColumnLetter(index: number): string {
        let result = '';
        while (index >= 0) {
            result = String.fromCharCode(65 + (index % 26)) + result;
            index = Math.floor(index / 26) - 1;
        }
        return result;
    }

    calculateTotalsForSelectedColumns(selectedChildren: any[], rowData: any[], gridOptions: any): any {
        if (!selectedChildren?.length || !rowData?.length) {
            return {};
        }
        const totals: any = {};
        selectedChildren.forEach((child: any) => {
            const fieldToUse = child.originalField || child.field;
            const col = (gridOptions?.columnDefs || []).find((c: any) => c.field === fieldToUse);
            if (!col) return;
            let total = 0;
            rowData.forEach(row => {
                let value = col?.valueGetter && typeof col.valueGetter === 'function'
                    ? col.valueGetter({ data: row })
                    : row?.[col?.field];
                if (Array.isArray(value)) {
                    const idx = typeof child.valueIndex === 'number' ? child.valueIndex : 0;
                    value = value[idx] ?? 0;
                }
                const numValue = Number(value) || 0;
                total += numValue;
            });
            const columnKey = child.displayName || col?.headerName || col?.field;
            totals[columnKey] = total;
        });
        return totals;
    }

    getDataKey(col: any): string {
        return (Array.isArray(col.valueLabels) && col.displayName) ? `${col.field}_${col.displayName}` : col.field;
    }

    getCellValue(row: any, key: string, col: any): any {
        const isManagerColumn = (col.headerName === 'General Manager' || col.headerName === 'Reporting Manager');
        const isTotalRow = (row[key] === 'Total');
        if (isManagerColumn && (row[key] === 0 || row[key] === '0') && !isTotalRow) {
            return '';
        }
        return (row[key] !== undefined && row[key] !== null && row[key] !== '') ? row[key] : '';
    }

}
