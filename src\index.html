<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>CRM</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <script async="" defer="" src="https://apis.google.com/js/api.js"></script>
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCkkbA74XK_YdsvpDn02-jFwhomKKFp2Rc&amp;libraries=visualization"></script>
  <!-- Enhanced Clickjacking Protection -->
  <script>
    // Enhanced iframe busting with multiple protection layers
    (function() {
      'use strict';

      // Method 1: Basic frame busting
      try {
        if (window.top !== window.self) {
          window.top.location.href = window.self.location.href;
        }
      } catch (error) {
        // Cross-origin framing detected - force redirect
        window.location.href = window.location.href;
      }

      // Method 2: Style-based protection
      var style = document.createElement('style');
      style.innerHTML = 'body { display: none !important; }';
      document.head.appendChild(style);

      // Method 3: Delayed verification
      setTimeout(function() {
        try {
          if (window.top === window.self) {
            // Not in frame - show content
            document.body.style.display = 'block';
            style.remove();
          } else {
            // Still in frame - redirect
            window.top.location.href = window.self.location.href;
          }
        } catch (error) {
          // Cross-origin error - redirect
          window.location.href = window.location.href;
        }
      }, 100);

      // Method 4: Continuous monitoring
      setInterval(function() {
        try {
          if (window.top !== window.self) {
            window.top.location.href = window.self.location.href;
          }
        } catch (error) {
          window.location.href = window.location.href;
        }
      }, 1000);
    })();
  </script>

  <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
  <link rel="manifest" href="manifest.webmanifest">
  <link rel="manifest" href="./manifest.json">
  <meta name="theme-color" content="#1976d2">
  <!-- Security Headers -->
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Content-Security-Policy" content="frame-ancestors 'none'; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https:; img-src 'self' data: https:; font-src 'self' data: https:;">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

  <!-- Cache Control -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="expires" content="-1">
</head>

<body>
  <app-root></app-root>
  <!-- <noscript>Please enable JavaScript to continue using this application.</noscript> -->
</body>

</html>