<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>CRM</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <script async="" defer="" src="https://apis.google.com/js/api.js"></script>
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCkkbA74XK_YdsvpDn02-jFwhomKKFp2Rc&amp;libraries=visualization"></script>
  <!-- Enhanced Clickjacking Protection -->
  <script>
    // Aggressive iframe busting with multiple protection layers
    (function() {
      'use strict';

      // Immediate check and redirect
      function bustFrame() {
        try {
          if (window.top !== window.self) {
            // Try to redirect parent window
            window.top.location.replace(window.self.location.href);
            return true;
          }
        } catch (error) {
          // Cross-origin framing detected - force redirect
          window.location.replace(window.location.href);
          return true;
        }
        return false;
      }

      // Method 1: Immediate frame busting
      if (bustFrame()) {
        // If we're in a frame, stop execution
        throw new Error('Clickjacking attempt blocked');
      }

      // Method 2: Style-based protection - hide everything initially
      var style = document.createElement('style');
      style.id = 'anti-clickjack';
      style.innerHTML = 'html, body { display: none !important; visibility: hidden !important; }';
      document.head.appendChild(style);

      // Method 3: Document ready check
      function checkAndShow() {
        try {
          if (window.top === window.self) {
            // We're not in a frame - show content
            var antiClickjackStyle = document.getElementById('anti-clickjack');
            if (antiClickjackStyle) {
              antiClickjackStyle.remove();
            }
            document.documentElement.style.display = 'block';
            document.documentElement.style.visibility = 'visible';
            document.body.style.display = 'block';
            document.body.style.visibility = 'visible';
          } else {
            // Still in frame - force redirect
            window.top.location.replace(window.self.location.href);
          }
        } catch (error) {
          // Cross-origin error - redirect
          window.location.replace(window.location.href);
        }
      }

      // Method 4: Multiple timing checks
      setTimeout(checkAndShow, 0);
      setTimeout(checkAndShow, 50);
      setTimeout(checkAndShow, 100);
      setTimeout(checkAndShow, 200);

      // Method 5: Document ready event
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkAndShow);
      } else {
        checkAndShow();
      }

      // Method 6: Window load event
      window.addEventListener('load', checkAndShow);

      // Method 7: Continuous aggressive monitoring
      var monitoringInterval = setInterval(function() {
        try {
          if (window.top !== window.self) {
            clearInterval(monitoringInterval);
            window.top.location.replace(window.self.location.href);
          }
        } catch (error) {
          clearInterval(monitoringInterval);
          window.location.replace(window.location.href);
        }
      }, 100);

      // Method 8: Backup monitoring with longer interval
      setInterval(function() {
        try {
          if (window.top !== window.self) {
            window.top.location.replace(window.self.location.href);
          }
        } catch (error) {
          window.location.replace(window.location.href);
        }
      }, 1000);

    })();
  </script>

  <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
  <link rel="manifest" href="manifest.webmanifest">
  <link rel="manifest" href="./manifest.json">
  <meta name="theme-color" content="#1976d2">
  <!-- Security Headers -->
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Content-Security-Policy" content="frame-ancestors 'none'; default-src 'self' 'unsafe-inline' 'unsafe-eval' data: https:; img-src 'self' data: https:; font-src 'self' data: https:;">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">
  <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

  <!-- Cache Control -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="expires" content="-1">
</head>

<body>
  <app-root></app-root>
  <!-- <noscript>Please enable JavaScript to continue using this application.</noscript> -->
</body>

</html>