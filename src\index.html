<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>CRM</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <script async="" defer="" src="https://apis.google.com/js/api.js"></script>
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCkkbA74XK_YdsvpDn02-jFwhomKKFp2Rc&amp;libraries=visualization"></script>
  <!-- Enhanced Clickjacking Protection -->
  <style id="antiClickjack">
    body { display: none !important; }
    .security-warning {
      display: block !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 100% !important;
      background: #ffffff !important;
      z-index: 999999 !important;
      text-align: center !important;
      padding: 50px !important;
      font-family: Arial, sans-serif !important;
    }
    .security-warning h1 {
      color: #d32f2f !important;
      font-size: 24px !important;
      margin-bottom: 20px !important;
    }
    .security-warning p {
      font-size: 16px !important;
      color: #333 !important;
      margin: 10px 0 !important;
    }
  </style>
  <script>
    // Enhanced iframe busting with immediate action
    (function() {
      if (self !== top) {
        // We're in an iframe - show security warning immediately
        document.addEventListener('DOMContentLoaded', function() {
          document.body.innerHTML = '<div class="security-warning"><h1>🛡️ Security Warning</h1><p><strong>This page cannot be displayed in a frame for security reasons.</strong></p><p>Please access this application directly by opening it in a new tab.</p><p>Redirecting in 3 seconds...</p></div>';
        });

        // Try to break out of iframe
        try {
          top.location.replace(self.location.href);
        } catch (ex) {
          // If we can't redirect parent, redirect ourselves after showing warning
          setTimeout(function() {
            window.location.replace(window.location.href);
          }, 3000);
        }
      } else {
        // Not in iframe - show the page normally
        document.addEventListener('DOMContentLoaded', function() {
          var antiClickjack = document.getElementById("antiClickjack");
          if (antiClickjack) {
            antiClickjack.parentNode.removeChild(antiClickjack);
          }
        });
      }
    })();
  </script>

  <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
  <link rel="manifest" href="manifest.webmanifest">
  <link rel="manifest" href="./manifest.json">
  <meta name="theme-color" content="#1976d2">
  <!-- Security Headers -->
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Content-Security-Policy" content="frame-ancestors 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">

  <!-- Cache Control -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="expires" content="-1">
</head>

<body>
  <app-root></app-root>
  <!-- <noscript>Please enable JavaScript to continue using this application.</noscript> -->

  <!-- Backup Clickjacking Protection -->
  <script>
    // Immediate check - runs as soon as script loads
    if (window.self !== window.top) {
      console.error('SECURITY: Iframe detected!');

      // Immediately replace page content
      document.open();
      document.write('<!DOCTYPE html><html><head><title>Security Warning</title><style>body{font-family:Arial,sans-serif;text-align:center;padding:50px;background:#f5f5f5;}h1{color:#d32f2f;font-size:28px;margin-bottom:20px;}p{font-size:16px;color:#333;margin:15px 0;}.warning-box{background:#fff;border:2px solid #d32f2f;border-radius:8px;padding:30px;max-width:500px;margin:0 auto;box-shadow:0 4px 8px rgba(0,0,0,0.1);}</style></head><body><div class="warning-box"><h1>🛡️ Security Warning</h1><p><strong>This page cannot be displayed in a frame</strong></p><p>For security reasons, this application cannot be embedded in an iframe.</p><p>Please open this application directly in a new browser tab.</p><p><small>You will be redirected automatically in 3 seconds...</small></p></div><script>setTimeout(function(){try{window.top.location.replace("' + window.location.href + '");}catch(e){window.location.replace("' + window.location.href + '");}},3000);<\/script></body></html>');
      document.close();
    }

    // Additional monitoring after page load
    window.addEventListener('load', function() {
      if (window.self !== window.top) {
        console.error('SECURITY: Iframe still detected after page load!');

        // Replace content again if still in iframe
        document.body.innerHTML = '<div style="position:fixed;top:0;left:0;width:100%;height:100%;background:#fff;z-index:999999;display:flex;align-items:center;justify-content:center;font-family:Arial,sans-serif;"><div style="text-align:center;background:#fff;border:2px solid #d32f2f;border-radius:8px;padding:30px;max-width:500px;box-shadow:0 4px 8px rgba(0,0,0,0.1);"><h1 style="color:#d32f2f;font-size:28px;margin-bottom:20px;">🛡️ Security Warning</h1><p style="font-size:16px;color:#333;margin:15px 0;"><strong>This page cannot be displayed in a frame</strong></p><p style="font-size:16px;color:#333;margin:15px 0;">For security reasons, this application cannot be embedded in an iframe.</p><p style="font-size:16px;color:#333;margin:15px 0;">Please open this application directly in a new browser tab.</p></div></div>';

        // Continuous attempts to break out
        var attempts = 0;
        var breakOut = setInterval(function() {
          try {
            window.top.location.replace(window.self.location.href);
            clearInterval(breakOut);
          } catch (e) {
            attempts++;
            if (attempts > 5) {
              window.location.replace(window.location.href);
              clearInterval(breakOut);
            }
          }
        }, 1000);
      }
    });
  </script>
</body>

</html>