<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>CRM</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <script async="" defer="" src="https://apis.google.com/js/api.js"></script>
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCkkbA74XK_YdsvpDn02-jFwhomKKFp2Rc&amp;libraries=visualization"></script>
  <!-- Simple Clickjacking Protection -->
  <style id="antiClickjack">
    body { display: none !important; }
  </style>
  <script>
    // Simple iframe busting
    if (self !== top) {
      document.addEventListener('DOMContentLoaded', function() {
        document.body.innerHTML = 'This page cannot be displayed in a frame';
        document.body.style.display = 'block';
      });
    } else {
      // Not in iframe - show the page normally
      document.addEventListener('DOMContentLoaded', function() {
        var antiClickjack = document.getElementById("antiClickjack");
        if (antiClickjack) {
          antiClickjack.parentNode.removeChild(antiClickjack);
        }
      });
    }
  </script>

  <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
  <link rel="manifest" href="manifest.webmanifest">
  <link rel="manifest" href="./manifest.json">
  <meta name="theme-color" content="#1976d2">
  <!-- Security Headers -->
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Content-Security-Policy" content="frame-ancestors 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">

  <!-- Cache Control -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="expires" content="-1">
</head>

<body>
  <app-root></app-root>
  <!-- <noscript>Please enable JavaScript to continue using this application.</noscript> -->

  <!-- Backup Clickjacking Protection -->
  <script>
    // Simple immediate check
    if (window.self !== window.top) {
      document.open();
      document.write('This page cannot be displayed in a frame');
      document.close();
    }
  </script>
</body>

</html>