<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>CRM</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
  <script async="" defer="" src="https://apis.google.com/js/api.js"></script>
  <script
    src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCkkbA74XK_YdsvpDn02-jFwhomKKFp2Rc&amp;libraries=visualization"></script>
  <!-- Enhanced Clickjacking Protection -->
  <style id="antiClickjack">
    body { display: none !important; }
  </style>
  <script>
    // Enhanced iframe busting with style protection
    (function() {
      // Hide the page initially
      if (self === top) {
        // Not in iframe - show the page
        var antiClickjack = document.getElementById("antiClickjack");
        if (antiClickjack) {
          antiClickjack.parentNode.removeChild(antiClickjack);
        }
      } else {
        // In iframe - try to break out
        try {
          top.location = self.location;
        } catch (ex) {
          // If we can't redirect parent, redirect ourselves
          document.write('<h1>This page cannot be displayed in a frame</h1>');
          setTimeout(function() {
            window.location = window.location;
          }, 1000);
        }
      }
    })();
  </script>

  <script src="https://sdk.cashfree.com/js/v3/cashfree.js"></script>
  <link rel="manifest" href="manifest.webmanifest">
  <link rel="manifest" href="./manifest.json">
  <meta name="theme-color" content="#1976d2">
  <!-- Security Headers -->
  <meta http-equiv="X-Frame-Options" content="DENY">
  <meta http-equiv="Content-Security-Policy" content="frame-ancestors 'none';">
  <meta http-equiv="X-Content-Type-Options" content="nosniff">

  <!-- Cache Control -->
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="pragma" content="no-cache">
  <meta http-equiv="expires" content="-1">
</head>

<body>
  <app-root></app-root>
  <!-- <noscript>Please enable JavaScript to continue using this application.</noscript> -->

  <!-- Backup Clickjacking Protection -->
  <script>
    // Final check - runs after page load
    (function() {
      if (window.self !== window.top) {
        console.error('SECURITY: Iframe detected after page load!');
        document.body.innerHTML = '<div style="position:fixed;top:0;left:0;width:100%;height:100%;background:#fff;z-index:999999;display:flex;align-items:center;justify-content:center;"><div style="text-align:center;"><h1 style="color:#d32f2f;">Security Warning</h1><p>This application cannot be displayed in a frame for security reasons.</p><p>Please access it directly.</p></div></div>';

        // Continuous attempts to break out
        var attempts = 0;
        var breakOut = setInterval(function() {
          try {
            window.top.location.replace(window.self.location.href);
            clearInterval(breakOut);
          } catch (e) {
            attempts++;
            if (attempts > 10) {
              window.location.replace(window.location.href);
              clearInterval(breakOut);
            }
          }
        }, 500);
      }
    })();
  </script>
</body>

</html>