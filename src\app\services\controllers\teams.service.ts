import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';

@Injectable({
  providedIn: 'root',
})
export class TeamsService extends BaseService<any> {
  baseURL: string = `${env.baseURL}`;
  apiURL: string = `${env.apiURL}`;
  identityURL: string = `${env.identityURL}api`;
  resourecURL: string = 'masterdata';
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }
  getResourceUrl(): string {
    return 'roles';
  }

  getAllPermissions() {
    return this.http.get(`${this.identityURL}/roles/existingpermissions`);
  }

  getAllUsersWithRoles() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/getallusers`);
  }

  getUsersList() {
    return this.http.get(`${env.baseURL}${env.apiURL}user`);
  }

  getRoleDetails(roleId: string) {
    return this.http.get(`${this.serviceBaseUrl}/${roleId}/permissions`);
  }

  getAllRolesList() {
    return this.http.get(`${this.identityURL}/${this.getResourceUrl()}/withpermissions`);
  }

  updateRoleDetails(roleId: string, resource: any) {
    return this.http.put(
      `${this.serviceBaseUrl}/${roleId}/permissions`,
      resource
    );
  }

  addRole(payload: any) {
    return this.http.post(`${this.identityURL}/roles`, payload);
  }

  addUser(payload: any) {
    return this.http.post(`${this.identityURL}/users`, payload);
  }

  updateUser(userId: string, payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/${userId}`, payload);
  }

  updateBulkUsers(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/bulk`, payload);
  }

  updateUserProfile(userId: string, payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}userprofile/${userId}`, payload);
  }

  getRolesPermissionById(roleId: string) {
    return this.http.get(`${this.identityURL}/roles/${roleId}/permissions`);
  }

  getUserById(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/${userId}`);
  }

  getUserProfile(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}userprofile/${userId}`);
  }

  updateRolesPermissionById(roleId: string, payload: any) {
    return this.http.put(
      `${this.identityURL}/roles/${roleId}/permissions`,
      payload
    );
  }

  deleteRole(roleId: string) {
    return this.http.delete(`${this.identityURL}/roles/${roleId}`);
  }

  deleteUser(payload: any) {
    return this.http.post(`${this.identityURL}/users/${payload?.userId}/toggle-status`, payload)
  }

  bulkToggleUserStatus(payload: string) {
    return this.http.post(`${this.identityURL}/users/bulk-toggle-status`, payload);
  }

  getDesignationsList() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/getalldesignations`);
  }

  addDesignation(designation: string) {
    return this.http.post(`${env.baseURL}${env.apiURL}user/designation`, {
      name: designation,
    });
  }

  getDepartmentsList() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/getalldepartments`);
  }

  addDepartment(department: string) {
    return this.http.post(`${env.baseURL}${env.apiURL}user/department`, {
      name: department,
    });
  }

  doesUserNameExists(username: string) {
    return this.http.get(
      `${this.identityURL}/users/tenant-independent-username-exists/${username}`
    );
  }

  doesEmailExists(email: string) {
    return this.http.get(`${this.identityURL}/users/email-exists/${email}`);
  }

  doesPhoneExists(phone: string) {
    return this.http.get(`${this.identityURL}/users/phone-exists/${phone}`);
  }

  UserExcelUpload(selectedFile: File) {
    let formData = new FormData();
    formData.append('file', selectedFile);
    return this.http.post(`${env.baseURL}${env.apiURL}user/excel`, formData);
  }

  uploadUserDocuments(payload: any, userId: string) {
    return this.http.put(
      `${env.baseURL}${env.apiURL}userprofile/document/${userId}`,
      payload
    );
  }

  deleteUserDocuments(id: string) {
    return this.http.delete(
      `${env.baseURL}${env.apiURL}userprofile/document/${id}`
    );
  }

  updateImg(url: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}userprofile/image`, `"${url}"`);
  }

  getUserBasicDetailsById(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/basicdetails/${userId}`);
  }

  getUserAssignmentsById(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/user-assignments/${userId}`);
  }

  toggleAutomation(userIds: Array<String>) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/toggle-automation?UserIds=${userIds[0]}`, null);
  }

  getReportees() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/reportees`);
  }

  getAdminsAndReportees() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/adminsandreportees`);
  }

  getOnlyReportees() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/onlyreportees`);
  }

  getOnlyReporteesWithInactive() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/onlyreportees/withinactiveusers`);
  }

  deleteUserAssignment(payload: any) {
    let headers: any = {
      body: {
        ...payload,
      },
    };
    return this.http.delete(`${env.baseURL}${env.apiURL}user/user-assignments`, headers);
  }

  getIVRSettingUserList() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/settings/call`);
  }

  getIVRSettingUserListById(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/settings/call-through?UserId=${userId}`);
  }

  updateIVRSettings(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/settings/bulk`, payload);
  }

  updateBulkRoles(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/bulk/roles`, payload);
  }
  getOnlyAdmins() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/admin-mfa`);
  }

  bulkToggleMFA(payload: string) {
    return this.http.post(`${this.identityURL}/users/bulk-toggle-mfa`, payload);
  }

  exportUsers(payload: any) {
    return this.http.post(`${env.baseURL}${env.apiURL}user/export/batch`, payload);
  }

  getUsersExportStatus(pageNumber: number, pageSize: number) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/export/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  getWithoutAdmins() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/without/admin`);
  }

  // deleteUserId(id: string) {
  //   return this.http.delete(`${env.baseURL}${env.apiURL}user/${id}`);
  // }

  deleteAssignedUser(payload: any) {
    return this.http.post(`${env.baseURL}${env.apiURL}user/userdelete`, payload);
  }

  getUserAssignedDataById(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/data/${userId}`);
  }

  getDeletedTracker(pageNumber: number, pageSize: number) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/delete/user/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  getAllTeams() {
    return this.http.get(`${env.baseURL}${env.apiURL}team`);
  }

  addTeam(payload: any) {
    return this.http.post(`${env.baseURL}${env.apiURL}team`, payload);
  }

  deleteTeams(payload: string[]) {
    const options = {
      body: payload,
    };
    return this.http.delete(`${env.baseURL}${env.apiURL}team`, options);
  }

  getUnassignedUsers() {
    return this.http.get(`${env.baseURL}${env.apiURL}team/unassignedusers`);
  }

  exportTeams(payload: any) {
    return this.http.post(`${env.baseURL}${env.apiURL}team/export/batch`, payload);
  }

  deleteMember(payload: any) {
    const options = {
      body: payload,
    };
    return this.http.delete(`${env.baseURL}${env.apiURL}team/users/delete`, options);
  }

  updateTeam(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}team`, payload);
  }

  addRetention(payload: any) {
    return this.http.post(`${env.baseURL}${env.apiURL}team/retention`, payload);
  }

  getRetentionList() {
    return this.http.get(`${env.baseURL}${env.apiURL}team/retention`);
  }

  updateRetention(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}team/retention`, payload);
  }

  deleteRetention(id: string) {
    return this.http.delete(`${env.baseURL}${env.apiURL}team/retention/${id}`);
  }

  getTeamsExportStatus(pageNumber: number, pageSize: number) {
    return this.http.get(`${env.baseURL}${env.apiURL}team/export/tracker?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  getGeneralManagerList() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/generalmanagers`);
  }

  getUsersByDesignation() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/getallusers/designation`);
  }

  uploadMappedColumns(params: string) {
    const url = `${env.baseURL}${env.apiURL}user/bulk/user?${params}`;
    return this.http.post(url, params);
  }

  getExcelUploadedList(pageNumber: number, pageSize: number) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/import/trackers?PageNumber=${pageNumber}&PageSize=${pageSize}`);
  }

  deleteRoles(ids: string) {
    let headers: any = {
      body: ids,
    };
    return this.http.delete(`${this.identityURL}/roles/bulk-delete`, headers);
  }

  resetPassword(ids: any) {
    let userIds: any = {
      userId: ids,
    };
    return this.http.post(`${this.identityURL}/users/default-password`, userIds);
  }

  getWhatsappSettingUserList() {
    return this.http.get(`${env.baseURL}${env.apiURL}user/settings/whatsapp`);
  }

  updateWhatsappSettings(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/settings/whatsapp`, payload);
  }

  toggleGeoFencing(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/geofence-active-status`, payload);
  }

  addGeoFencing(payload: any) {
    return this.http.post(`${env.baseURL}${env.apiURL}user/geofence-settings`, payload);
  }

  updateGeoFencing(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user/geofence-settings`, payload);
  }

  getGeoFencingList(userId: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/geofence-details/${userId}`);
  }

  updateUserSearch(payload: any) {
    return this.http.put(`${env.baseURL}${env.apiURL}user`, payload);
  }

  getRecentSearch(moduleType: string) {
    return this.http.get(`${env.baseURL}${env.apiURL}user/searchresults?ModuleType=${moduleType}`);
  }

  getRotationList() {
    return this.http.get(`${env.baseURL}${env.apiURL}team/configuration`);
  }

  getAllAdmin(){
    return this.http.get(`${env.baseURL}${env.apiURL}user/alladminusers`);
  }
}
