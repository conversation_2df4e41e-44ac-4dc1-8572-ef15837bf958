import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, tap } from 'rxjs';
import { getTenantName } from 'src/app/core/utils/common.util';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class LoginService {
  identityURL: string = environment.identityURL;

  constructor(private httpClient: HttpClient, private router: Router) { }

  login(payload: any) {
    return this.httpClient.post(`${this.identityURL}api/tokens`, payload);
  }

  refreshToken(resource: any) {
    return this.httpClient.post(`${this.identityURL}api/tokens/refresh`, resource);
  }

  generateOTP(username: string) {
    return this.httpClient.post(
      `${this.identityURL}api/tokens/otp?userName=${encodeURIComponent(username)}`,
      ''
    );
  }

  tryAnotherWay(payload:any){
    return this.httpClient.post(
      `${this.identityURL}api/users/otp-direct`,payload
    );
  }

  verifyOTP(username: string, sessionId: string, otp: string) {
    return this.httpClient.post(
      `${this.identityURL}api/tokens/verify-otp?userName=${encodeURIComponent(
        username
      )}&sessionId=${sessionId}&otp=${otp}`,
      ''
    );
  }



  doesTenantExist(): () => Observable<any> {
    return () => {
      let subDomain = getTenantName();
      return this.httpClient
        .get(`${this.identityURL}api/tenants/${subDomain}`)
        .pipe(
          tap((resp: any) => {
            if (resp.isActive) {
              this.router.navigate(['/login']);
            } else {
              console.log('tenant not found');
            }
          })
        );
    };
  }

  logout(payload: any) {
    return this.httpClient.post(`${this.identityURL}api/tokens/logout`, payload)
  }
}
