import { Component, ViewChild } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'punch-in',
  templateUrl: './punch-in.component.html',
})
export class PunchInComponent implements ICellRendererAngularComp {
  @ViewChild('locationModal') locationModal: any;
  logs: any[] = [];
  modalRef: BsModalRef;
  params: any;
  constructor(
    private modalService: BsModalService,
  ) { }

  agInit(params: any): void {
    this.params = params;
    this.logs = params.value || [];
  }

  refresh(params: any): boolean {
    this.logs = params.value || [];
    return true;
  }

  
  openPopup(locationModal: any) {
    let initialState = {
      logs: this.logs,
    }
    this.modalRef = this.modalService.show(locationModal, {
      class: 'modal-400 modal-dialog-centered ip-modal-unset',
      initialState,
    });
  }
}
