import {
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subscription, skipWhile, take, takeUntil } from 'rxjs';

import {
  PHONE_PREFERRED_COUNTRIES,
  WATR_MARK_POSITIONS,
} from 'src/app/app.constants';
import { WaterMarkPositions } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, getAssignedToDetails, getAWSImagePath, isEmptyObject } from 'src/app/core/utils/common.util';
import {
  FetchCurrencyList,
  UpdateGlobalSettings
} from 'src/app/reducers/global-settings/global-settings.actions';
import {
  getCurrencyList,
  getCurrencyListIsLoading,
  getGlobalAnonymousIsLoading,
  getGlobalSettingsAnonymous,
} from 'src/app/reducers/global-settings/global-settings.reducer';
import { FetchAreaUnitList } from 'src/app/reducers/master-data/master-data.actions';
import { getAreaUnits } from 'src/app/reducers/master-data/master-data.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchProfile } from 'src/app/reducers/profile/profile.actions';
import {
  getProfile,
  getProfileIsLoading,
} from 'src/app/reducers/profile/profile.reducers';
import { BlobStorageService } from 'src/app/services/controllers/blob-storage.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { getAllAdmin, getAllAdminIsLoading, getUserBasicDetails, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { FetchAllAdmin } from 'src/app/reducers/teams/teams.actions';

@Component({
  selector: 'general-settings',
  templateUrl: './general-settings.component.html',
})
export class GeneralSettingsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  preferredCountries = PHONE_PREFERRED_COUNTRIES;
  leadSettingsForm: FormGroup;
  settingData: any;
  canUpdate: boolean;
  canView: boolean;
  isCurrencyListOpen: boolean = false;
  isChangesMade: boolean = false;
  isCurrencyListLoading: boolean = true;
  isGlobalSettingLoading: boolean = true;
  message: string;
  notes: string;
  countryCodeList: any[] = [];
  currencyList: any[] = [];
  selectedCurrency: any[] = [];
  selectedCode: any[] = [];
  defaultSymbol: string = '';
  defaultCurrency: string = '';
  defaultCountry: string = 'in';
  timezones: string[];
  selectedCurrencies: any[] = [];

  settingProject: any;
  logoOptions: { label: string; key: string; isChecked: any; disabled: any }[];
  getGlobalSettings$: Subscription;
  isProfileLoading: boolean = true;
  currencies: any[];
  selectedCurrenciesCopy: any[];
  defaultSymbolCopy: string;
  IsAssignedCallLogsEnabled: boolean = false;
  getAssignedToDetails = getAssignedToDetails;
  waterMarkSettingsObj: any = {
    watermarkOpacity: null,
    watermarkSize: null,
    currentPosition: 'center',
    selectedFile: '',
    positions: WATR_MARK_POSITIONS,
    profilePic: '',
    defaultLogoType: '',
    ImagePathUrl: '',
    isImage: true,
    selectedPositionIndex: null,
  };
  defaultCurrencyCopy: string;
  areaSizeUnits: any;
  usersList: any[] = [];
  inactiveUsers: any[] = [];
  activeUsers: any[] = [];
  isUsersChanged: boolean = false;
  settingsData: any;
  isAllAdminLoading: boolean;
  adminList: any[] = [];
  canShowSave: boolean = false;
  isAdmin: boolean;
  constructor(
    private router: Router,
    private imgService: BlobStorageService,
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title,
    private _notificationService: NotificationsService
  ) { }

  patchValues() {
    if (this.getGlobalSettings$) {
      this.getGlobalSettings$?.unsubscribe();
    }
    this.getGlobalSettings$ = this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.isAssignedCallLogsEnabled !== undefined) {
          this.IsAssignedCallLogsEnabled = data?.isAssignedCallLogsEnabled;
        }

        (this.selectedCurrencies = data?.countries?.length
          ? data.countries?.[0].currencies
          : null),
          (this.settingData = data);
        const isDefaultCurrency = this.selectedCurrencies?.some(
          (currency) =>
            currency?.currency ===
            this.settingData.countries?.[0]?.defaultCurrency
        );
        const isDefaultSymbol = this.selectedCurrencies?.some(
          (currency) =>
            currency?.symbol === this.settingData.countries?.[0]?.defaultSymbol
        );
        this.defaultCurrency = isDefaultCurrency
          ? this.settingData.countries?.[0].defaultCurrency
          : this.selectedCurrencies?.length > 0
            ? this.selectedCurrencies?.[0]?.currency
            : 'INR';
        this.defaultSymbol = isDefaultSymbol
          ? this.settingData.countries?.[0].defaultSymbol
          : this.selectedCurrencies?.length > 0
            ? this.selectedCurrencies?.[0]?.symbol
            : '₹';
        this.leadSettingsForm.patchValue({
          internationalNo: this.settingData?.hasInternationalSupport,
          mobileCall: this.settingData?.isMobileCallEnabled,
          callDetection: this.settingData?.isCallDetectionActivated,
          allowExportEmailNotificationToAdmin: this.settingData?.exportEmailNotificationSettings?.allowExportEmailNotificationToAdmin,
          selectedAdminUserIds: this.settingData?.exportEmailNotificationSettings?.selectedAdminUserIds,
          currency: this.settingData.countries?.length
            ? this.settingData.countries?.[0].currencies
            : [],
          defaultSymbol: this.settingData.countries?.length
            ? this.settingData.countries?.[0].defaultSymbol
            : '₹',
          defaultCurrency: this.settingData.countries?.length
            ? this.settingData.countries?.[0].defaultCurrency
            : 'INR',
          countryCode: this.settingData.countries?.length
            ? this.settingData.countries?.[0].code
            : 'IN',
          callDetectionMandatoryUserIds: (this.settingData?.callSettings?.callDetectionMandatoryUserIds && Array.isArray(this.settingData.callSettings.callDetectionMandatoryUserIds))
            ? this.settingData.callSettings.callDetectionMandatoryUserIds
            : [],
        });

        this.waterMarkSettingsObj.watermarkOpacity =
          Number(data?.leadProjectSetting?.opacity) / 100;
        this.waterMarkSettingsObj.selectedPositionIndex =
          data?.leadProjectSetting?.waterMarkPosition;
        this.waterMarkSettingsObj.currentPosition = this.getCssClass(
          WaterMarkPositions[this.waterMarkSettingsObj.selectedPositionIndex]
        );
        this.waterMarkSettingsObj.watermarkSize =
          data?.leadProjectSetting?.imageSize || 50;
        this.waterMarkSettingsObj.selectedFile =
          data && data.leadProjectSetting && data.leadProjectSetting.imageName
            ? data.leadProjectSetting.imageName.split('_$_')[0]
            : '';

        if (
          this.waterMarkSettingsObj.profilePic &&
          !data.leadProjectSetting.imageName
        ) {
          this.waterMarkSettingsObj.defaultLogoType = 'UserOrganizationLogo';
          this.waterMarkSettingsObj.isImage = false;
        } else {
          this.waterMarkSettingsObj.defaultLogoType = 'UploadFile';

        }
        this.waterMarkSettingsObj.ImagePathUrl =
          data?.leadProjectSetting?.waterMarkUrl;
        this.settingProject = data;
        this.leadSettingsForm.patchValue({
          isProjectLogo: data?.leadProjectSetting?.isWaterMarksOnImagesEnabled,
          areaSizeUnit: data?.defaultValues?.masterAreaUnit
        });
      });

    this._store
      .select(getGlobalAnonymousIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((loading: boolean) => {
        this.isGlobalSettingLoading = loading;
      });
  }

  ngOnInit(): void {
    this.leadSettingsForm = this.fb.group({
      internationalNo: [null],
      callDetection: [null],
      countryCode: [this.defaultCountry],
      callingCode: [null],
      timeZone: false,
      currency: [null],
      symbol: [null],
      defaultSymbol: [null],
      defaultCurrency: [null],
      isProjectLogo: [false],
      mobileCall: [null],
      areaSizeUnit: [null],
      callDetectionMandatoryUserIds: [[]],
      allowExportEmailNotificationToAdmin: [null],
      selectedAdminUserIds: [null],
    });
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this._store.dispatch(new FetchCurrencyList());
    this._store.dispatch(new FetchAreaUnitList());
    this._store
      .select(getCurrencyList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.countryCodeList = data?.countries?.map((country: any) => ({
          name: country?.name,
          countryCode: country?.code,
          callingCode: country?.callingCode,
        }));
        this.currencyList = data?.countries
          ?.map((country: any) => ({
            currency: country?.currency,
            symbol: country?.symbol,
            name: country?.name || country.currency,
          }))
          .filter(
            (value: any, index: number, self: any[]) =>
              index ===
              self?.findIndex((item) => item?.symbol === value?.symbol)
          );
      });

      this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (!isEmptyObject(data)) {
          this.isAdmin = data?.subscriptionDetails?.isAdmin;
          if(this.isAdmin){
            this._store.dispatch(new FetchAllAdmin());
          }
        }
      });

    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canView = permissionsSet.has('Permissions.GlobalSettings.View');
        this.canUpdate = permissionsSet.has(
          'Permissions.GlobalSettings.Update'
        );
        if (permissionsSet.has('Permissions.OrgProfile.View')) {
          this._store.dispatch(new FetchProfile());
        }
      });

    this._store
      .select(getCurrencyListIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isCurrencyListLoading = isLoading;
      });

    this._store
      .select(getAllAdminIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAllAdminLoading = isLoading;
      });

    this._store
      .select(getAllAdmin)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.adminList = data;
        this.adminList = this.adminList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.selectAllForDropdownItems(this.adminList);
        this.adminList = assignToSort(this.adminList, '');
      });

    this._store
      .select(getAreaUnits)
      .pipe(takeUntil(this.stopper))
      .subscribe((units: any) => {
        this.areaSizeUnits = units || [];
      });

    this.logoOptions = [
      {
        label: 'Use organization Logo',
        key: 'UserOrganizationLogo',
        isChecked: false,
        disabled: false,
      },
      {
        label: 'Upload file',
        key: 'UploadFile',
        isChecked: false,
        disabled: false,
      },
    ];

    this._store
      .select(getProfileIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isProfileLoading = isLoading;
      });

    this._store
      .select(getProfile)
      .pipe(
        skipWhile(() => this.isProfileLoading),
        take(1)
      )
      .subscribe((data: any) => {
        this.waterMarkSettingsObj.profilePic = data?.logoImgUrl
          ? getAWSImagePath(data?.logoImgUrl || '')
          : '';
        if (this.waterMarkSettingsObj.profilePic?.length) {
          this.waterMarkSettingsObj.defaultLogoType = 'UserOrganizationLogo';
        } else {
          this.waterMarkSettingsObj.defaultLogoType = 'UploadFile';
        }
        this.logoOptions = this.logoOptions.map((option) => {
          if (option.key === 'UserOrganizationLogo') {
            return {
              ...option,
              disabled: !this.waterMarkSettingsObj.profilePic ? true : false,
            };
          }
          if (option.key === 'UploadFile') {
            return {
              ...option,
            };
          }

          return option;
        });
        if (data) {
          this.patchValues();
        }
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.usersList = data;
        this.usersList = this.usersList?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.inactiveUsers = this.usersList?.filter((user: any) => !user.isActive);
        this.usersList = assignToSort(this.usersList, '');
      });

    // this.patchValues();
    this.selectPosition('', 0, true);
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'internationalNo':
        if (this.leadSettingsForm.value.internationalNo) {
          this.message =
            'Are you sure you want to disable the “International Number” option?';
          this.notes =
            'This will disable users from adding leads with international numbers.';
        } else {
          this.message =
            'Are you sure you want to enable the “International Number” option?';
          this.notes =
            'This will allow the users to add leads with International numbers';
        }
        break;
      case 'mobileCall':
        if (this.leadSettingsForm.value.mobileCall) {
          this.message =
            'Are you sure you want to enable the "Call Notification" option?';
          this.notes =
            'Enabling this will allow call notifications to be sent to the mobile device when calling a lead/data.';
        } else {
          this.message =
            'Are you sure you want to disable the "Call Notification" option?';
          this.notes =
            'Disabling this will stop call notifications from being sent to the mobile device when calling a lead/data.';
        }
        break;
      case 'callDetection':
        if (this.leadSettingsForm.value.callDetection) {
          this.message =
            'Are you sure you want to disable the “Call Detection”';
          this.notes =
            'This will disable the call detection for your organization.';
        } else {
          this.message = 'Are you sure you want to enable the “Call Detection”';
          this.notes =
            'This will allow users to track calls from leads if the lead is assigned to them';
        }
        break;
      case 'allowExportEmailNotificationToAdmin':
        if (this.leadSettingsForm.value.allowExportEmailNotificationToAdmin) {
          this.message =
            'Are you sure you want to disable the “Allow Export Email Notification To Admin”';
          this.notes = 'This will disable the export email notification to admin.';
        } else {
          this.message =
            'Are you sure you want to enable the “Allow Export Email Notification To Admin”';
          this.notes = 'This will allow the export email notification to admin.';
        }
        break;
      case 'timeZone':
        if (this.leadSettingsForm.value.timeZone) {
          this.message =
            'Are you sure you want to disable the “Automatic Time-Zone”';
          this.notes = 'This not set your time-zone automatically.';
        } else {
          this.message =
            'Are you sure you want to enable the “Automatic Time-Zone”';
          this.notes = 'This will set your time-zone automatically';
        }
        break;
    }
  }

  selectCurrency(symbol: string, currency: string) {
    this.defaultSymbol = symbol;
    this.defaultCurrency = currency;
    this.isChangesMade = true;
  }

  onSave() {
    const settingProjectVal: any = this.leadSettingsForm?.value;
    let settingsData: any = this.leadSettingsForm.value;
    if (!settingsData.callingCode) {
      const selectedCountry = this.countryCodeList?.find(
        (item) => item?.countryCode === settingsData?.countryCode
      );
      this.leadSettingsForm?.patchValue({
        callingCode: selectedCountry?.callingCode || '+91',
      });
    }
    settingsData = this.leadSettingsForm.value;
    const isDefaultCurrency = this.selectedCurrencies?.some(
      (currency: any) => currency?.currency === this.defaultCurrency
    );
    const isDefaultSymbol = this.selectedCurrencies?.some(
      (currency: any) => currency?.symbol === this.defaultSymbol
    );
    const countryObj = {
      currencies: settingsData.currency || [],
      defaultSymbol: isDefaultSymbol
        ? this.defaultSymbol
        : this.selectedCurrencies?.length > 0
          ? this.selectedCurrencies?.[0]?.symbol
          : '₹',
      defaultCurrency: isDefaultCurrency
        ? this.defaultCurrency
        : this.selectedCurrencies?.length > 0
          ? this.selectedCurrencies?.[0]?.currency
          : 'INR',
      code: settingsData?.countryCode ? settingsData?.countryCode : 'IN',
      defaultCallingCode: settingsData?.callingCode,
    };

    let payload: any = {
      ...this.settingData,
      hasInternationalSupport: settingsData.internationalNo,
      isMobileCallEnabled: settingsData.mobileCall,
      isCallDetectionActivated: settingsData.callDetection,
      exportEmailNotificationSettings: {
        allowExportEmailNotificationToAdmin: settingsData.allowExportEmailNotificationToAdmin,
        selectedAdminUserIds: settingsData.selectedAdminUserIds
      },
      defaultValues: {
        masterAreaUnit: settingsData.areaSizeUnit
      },
      countries: [countryObj],
      leadProjectSetting: {
        ...this.settingData.leadProjectSetting,
        isWaterMarksOnImagesEnabled: settingProjectVal?.isProjectLogo,
      },
      callSettings: {
        ...this.settingData.callSettings,
        callDetectionMandatoryUserIds: settingsData.callDetectionMandatoryUserIds || []
      }
    };
    payload.isAssignedCallLogsEnabled = this.IsAssignedCallLogsEnabled;
    this._store.dispatch(new UpdateGlobalSettings(payload));
    this.modalRef.hide();
    this.copyCurrency();
    this.isChangesMade = false;
    this.canShowSave = false;
  }

  copyCurrency() {
    this.currencies = this.leadSettingsForm?.get('currency')?.value;
    this.selectedCurrenciesCopy = [...this.selectedCurrencies];
    this.defaultSymbolCopy = this.defaultSymbol;
    this.defaultCurrencyCopy = this.defaultCurrency;
  }

  onCancel() {
    this.defaultSymbol = this.defaultSymbolCopy;
    this.defaultCurrency = this.defaultCurrencyCopy;
    this.leadSettingsForm?.get('currency')?.setValue(this.currencies);
    this.selectedCurrencies = [...this.selectedCurrenciesCopy];
    this.isChangesMade = false;
  }

  onCountryCodeChange(event: any) {
    const selectedCountry = this.countryCodeList?.find(
      (item) => item?.countryCode === event
    );

    this.leadSettingsForm?.patchValue({
      callingCode: selectedCountry?.callingCode || '+91',
    });

    this.onSave();
  }

  updateSelectedCurrencies(event: any) {
    this.selectedCurrencies = [];
    for (const item of event) {
      this.selectedCurrencies.push(item);
    }
    this.isChangesMade = true;
  }

  closePopup() {
    this.patchValues();
    this.modalRef.hide();
  }

  selectLogoType(logoType: string) {
    this.waterMarkSettingsObj.defaultLogoType = logoType;
  }

  triggerFileInput(): void {
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      const validImageTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
      ];
      if (!validImageTypes.includes(file?.type)) {
        this._notificationService.warn(
          `Unsupported file type. Only image formats are allowed.`
        );
        return;
      }
      let reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (eventOnload: any) => {
        if (reader.result) {
          this.imgService
            .uploadPropertyGallery([eventOnload.target.result])
            .pipe(takeUntil(this.stopper))
            .subscribe((res: any) => {
              if (res?.data) {
                let pathArr = res?.data;
                pathArr?.map((path: string) => {
                  this.waterMarkSettingsObj.ImagePathUrl =
                    getAWSImagePath(path);
                });
              }
            });
        }
      };

      this.waterMarkSettingsObj.selectedFile =
        file.name + '_$_' + this.waterMarkSettingsObj.defaultLogoType;
      this.waterMarkSettingsObj.isImage = true;
    }
  }

  selectPosition(
    item: any,
    index: number,
    defaultselected: boolean = false
  ): void {
    this.waterMarkSettingsObj.selectedPositionIndex = index;
    this.waterMarkSettingsObj.currentPosition = this.getCssClass(
      defaultselected ? 'Center' : item.position
    );
  }

  getCssClass(position: string): string {
    switch (position) {
      case 'Left Top':
        return 'left-top';
      case 'Left Center':
        return 'left-center';
      case 'Left Bottom':
        return 'left-bottom';
      case 'Top Center':
        return 'top-center';
      case 'Center':
        return 'center';
      case 'Bottom Center':
        return 'bottom-center';
      case 'Right Top':
        return 'right-top';
      case 'Right Center':
        return 'right-center';
      case 'Right Bottom':
        return 'right-bottom';
      default:
        return 'center';
    }
  }

  changeOpacity(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    this.waterMarkSettingsObj.watermarkOpacity =
      Number(inputElement.value) / 100;
  }

  saveWaterMark() {
    if (
      this.waterMarkSettingsObj.defaultLogoType === 'UploadFile' &&
      !this.waterMarkSettingsObj.selectedFile
    ) {
      this._notificationService.warn(`Please upload file!`);
      return;
    }
    let payload: any = {
      ...this.settingProject,
      leadProjectSetting: {
        ...this.settingProject?.leadProjectSetting,
        waterMarkUrl:
          this.waterMarkSettingsObj.defaultLogoType === 'UserOrganizationLogo'
            ? this.waterMarkSettingsObj.profilePic
            : this.waterMarkSettingsObj.ImagePathUrl,
        opacity: (
          Number(this.waterMarkSettingsObj.watermarkOpacity) * 100
        ).toString(),
        waterMarkPosition: parseInt(
          this.waterMarkSettingsObj.selectedPositionIndex
        ),
        imageSize: this.waterMarkSettingsObj.watermarkSize?.toString(),
        background: true,
        imageName:
          this.waterMarkSettingsObj.defaultLogoType === 'UserOrganizationLogo'
            ? ''
            : this.waterMarkSettingsObj.selectedFile +
            '_$_' +
            this.waterMarkSettingsObj.defaultLogoType,
      },
    };
    this._store.dispatch(new UpdateGlobalSettings(payload));
  }

  changeWatermarkSize(size: string): void {
    this.waterMarkSettingsObj.watermarkSize = Number(size);
  }

  gotoGlobalConfig() {
    this.router.navigate(['/global-config']);
  }

  onSaveWatermark() {
    this.leadSettingsForm
      .get('isProjectLogo')
      .setValue(!this.leadSettingsForm.get('isProjectLogo').value);
    this.onSave();
  }

  formatFileName(selectedFileName: any) {
    if (selectedFileName === undefined || selectedFileName === null) {
      return;
    }
    if (typeof selectedFileName !== 'string') {
      throw new TypeError('The input should be a string.');
    }
    const maxLength = 40;
    const lastDotIndex = selectedFileName.lastIndexOf('.');
    let extension = '';
    if (
      lastDotIndex !== -1 &&
      lastDotIndex !== 0 &&
      lastDotIndex !== selectedFileName.length - 1
    ) {
      extension = selectedFileName.substring(lastDotIndex + 1);
      selectedFileName = selectedFileName.substring(0, lastDotIndex);
    }
    if (selectedFileName.length > maxLength) {
      selectedFileName = selectedFileName.slice(0, maxLength) + '...';
    }
    let fileName = extension
      ? `${selectedFileName}.${extension}`
      : selectedFileName
        ? selectedFileName
        : '';
    return fileName.split('_$_')[0];
  }

  onSelectionChange(event: any) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event.map((item: any) => item?.id);
    this.leadSettingsForm?.get('callDetectionMandatoryUserIds')?.setValue(newlySelectedItems);
    this.isUsersChanged = true;
  }

  selectAllForDropdownItems(items: any[]) {
    let allSelect = (items: any) => {
      items?.forEach((element: any) => {
        element['selectedAllGroup'] = 'selectedAllGroup';
      });
    };

    allSelect(items);
  }

  removeUser(userId: string) {
    this.canShowSave = true;
    this.leadSettingsForm?.get('selectedAdminUserIds')?.setValue(
      this.leadSettingsForm?.get('selectedAdminUserIds')?.value?.filter(
        (id: string) => id !== userId
      )
    );
  }
}
