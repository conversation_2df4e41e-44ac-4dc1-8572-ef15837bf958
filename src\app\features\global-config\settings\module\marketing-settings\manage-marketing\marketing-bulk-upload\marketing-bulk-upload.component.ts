import { Component, ElementRef, EventEmitter, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AGENCY_EXCEL_TEMPLATE, AgencyDisplayColumns, CAMPAIGN_EXCEL_TEMPLATE, CHANNELPARTNER_EXCEL_TEMPLATE, ChannelPartnerDisplayColumns, MarketingDataColumns } from 'src/app/app.constants';
import { MarketingType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { getAppName, getSystemTimeOffset, getSystemTimeZoneId, validateAllFormFields } from 'src/app/core/utils/common.util';
import { ExcelUploadedStatusComponent } from 'src/app/features/leads/excel-uploaded-status/excel-uploaded-status.component';
import { FetchAgencyExcelUploadedList, FetchCampaignExcelUploadedList, FetchChannelPartnerExcelUploadedList, MarketingExcelUpload, UploadAgencyMappedColumns, UploadCampaignMappedColumns, UploadChannelPartnerMappedColumns } from 'src/app/reducers/manage-marketing/marketing.action';
import { getMarketingColumnHeadings } from 'src/app/reducers/manage-marketing/marketing.reducer';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'marketing-bulk-upload',
  templateUrl: './marketing-bulk-upload.component.html',
})
export class MarketingBulkUploadComponent implements OnInit {
  getAppName = getAppName;
  @ViewChild('validModal') validModal: any;
  @ViewChild('fileInput') fileInput: ElementRef<HTMLInputElement>;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  queryParameterData: any;
  excelAgencyTemplatePath: string;
  agencyData = AgencyDisplayColumns;
  channelData = ChannelPartnerDisplayColumns;
  agencyMappingForm: FormGroup;
  campaignForm: FormGroup;
  channelPartnerForm: FormGroup;
  isFileTypeSupported: boolean = true;
  currentStep: number = 1;
  isValidModal: boolean = false;
  selectedFile: File;
  formKeys: Array<string> = [];
  s3BucketKey: string;
  excelSheets: any = {};
  sheetNames: Array<string> = [];
  selectedSheet: string;
  selectedTitle: string;
  userData: any;

  constructor(
    public modalRef: BsModalRef,
    public modalService: BsModalService,
    private _store: Store<AppState>,
    private router: Router,
    private fb: FormBuilder,
    private headerTitle: HeaderTitleService,
    private route: ActivatedRoute
  ) { }

  ngOnInit() {
    this.agencyMappingForm = this.fb.group({
      agencyName: [null, Validators.required],
      countryCode: [null],
      phoneNumber: [null],
      email: [null],
      location: [null],
      state: [null],
      city: [null],
    });

    this.campaignForm = this.fb.group({
      campaignName: [null, Validators.required],
    });

    this.channelPartnerForm = this.fb.group({
      channelPartnerName: [null, Validators.required],
      countryCode: [null],
      phoneNumber: [null],
      email: [null],
      location: [null],
      state: [null],
      city: [null],
      reraNumber: [null],
      companyName: [null],
    });

    this.route.queryParams.subscribe(params => {
      this.queryParameterData = params;
      if (this.queryParameterData?.query === 'Agency Name') {
        this.excelAgencyTemplatePath = AGENCY_EXCEL_TEMPLATE;
      } else if (this.queryParameterData?.query === 'Campaign name') {
        this.excelAgencyTemplatePath = CAMPAIGN_EXCEL_TEMPLATE;
      }
      else {
        this.excelAgencyTemplatePath = CHANNELPARTNER_EXCEL_TEMPLATE;
      }
    });
    this._store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

  }

  onFileSelection(file: File) {
    this.selectedFile = file;
    this.currentStep = this.currentStep < 2 ? this.currentStep + 1 : this.currentStep;
  }

  uploadFile() {
    this._store.dispatch(new MarketingExcelUpload(this.selectedFile));
    this._store
      .select(getMarketingColumnHeadings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.excelSheets = data?.multiSheetColumnNames;
        if (this.excelSheets) this.sheetNames = Object.keys(this.excelSheets);
        this.selectedSheet = this.sheetNames[0];
        this.formKeys = data?.columnNames;
        this.s3BucketKey = data?.s3BucketKey;
        if (this.formKeys?.length) {
          this.currentStep = 3;
        }
        this.resetForms();
        this.onAutoMapChange();
      });
  }

  replaceFile() {
    this.fileInput.nativeElement.click();
  }

  isValidForm() {
    let isFormValid = false;

    if (this.queryParameterData?.query === 'Agency Name') {
      isFormValid = this.agencyMappingForm.valid;
    } else if (this.queryParameterData?.query === 'Campaign name') {
      isFormValid = this.campaignForm?.valid;
      if (!isFormValid) {
        validateAllFormFields(this.campaignForm);
      }
    } else {
      isFormValid = this.channelPartnerForm?.valid;
      if (!isFormValid) {
        validateAllFormFields(this.channelPartnerForm);
      }
    }

    if (!this.selectedSheet || !isFormValid) {
      if (this.queryParameterData?.query === 'Agency Name') {
        validateAllFormFields(this.agencyMappingForm);
      } else if (this.queryParameterData?.query === 'Campaign name') {
        validateAllFormFields(this.campaignForm);
      } else {
        validateAllFormFields(this.channelPartnerForm);
      }
      return;
    } else {
      this.isValidModal = true;
      this.modalRef = this.modalService.show(this.validModal, {
        class: 'modal-350 modal-dialog-centered ip-modal-unset',
        keyboard: false,
      });
    }
  }

  confirmSheet(trackerInfoModal: TemplateRef<any>) {
    this.modalRef.hide();
    this.sendMappingDetails(trackerInfoModal);
  }

  sendMappingDetails(trackerInfoModal: TemplateRef<any>) {
    if (!this.selectedSheet) {
      return;
    }

    const payload: any = {
      s3BucketKey: this.s3BucketKey,
      fileName: this.selectedFile?.name,
      mappedColumnData: {},
      sheetName: this.selectedSheet || this.sheetNames[0],
      timeZoneId: this.userData?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userData?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset()
    };

    let bulkUploadType: number;
    let formGroup: any;

    if (this.queryParameterData?.query === 'Agency Name') {
      formGroup = this.agencyMappingForm;
      bulkUploadType = MarketingType.AgencyName;
    } else if (this.queryParameterData?.query === 'Campaign name') {
      formGroup = this.campaignForm;
      bulkUploadType = MarketingType.CampaignName;
    } else {
      formGroup = this.channelPartnerForm;
      bulkUploadType = MarketingType.ChannelPartner;
    }

    const filteredColumns = MarketingDataColumns.filter(item => {
      if (this.queryParameterData?.query === 'Agency Name') {
        return item.type === 'Agency' || item.type === 'All';
      } else if (this.queryParameterData?.query === 'Campaign name') {
        return item.type === 'Campaign';
      } else {
        return item.type === 'ChannelPartner' || item.type === 'All';
      }
    });

    filteredColumns.map((item: any) => {
      if (formGroup.value[item.value]) {
        payload.mappedColumnData[item.displayName] = formGroup.value[item.value];
        if (bulkUploadType) {
          payload.bulkUploadType = bulkUploadType;
        }
      }
    });

    if (this.queryParameterData?.query === 'Agency Name') {
      this._store.dispatch(new UploadAgencyMappedColumns(payload));
      this._store.dispatch(new FetchAgencyExcelUploadedList(1, 10));
    } else if (this.queryParameterData?.query === 'Campaign name') {
      this._store.dispatch(new UploadCampaignMappedColumns(payload));
      this._store.dispatch(new FetchCampaignExcelUploadedList(1, 10));
    } else {
      this._store.dispatch(new UploadChannelPartnerMappedColumns(payload));
      this._store.dispatch(new FetchChannelPartnerExcelUploadedList(1, 10));
    }

    this.modalRef = this.modalService.show(
      trackerInfoModal,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          ignoreBackdropClick: true,
          keyboard: false,
        }
      )
    );
  }

  onAutoMapChange() {
    const mappingColumns = this.queryParameterData?.query === 'Agency Name' ? AgencyDisplayColumns : ChannelPartnerDisplayColumns;
    const form = this.queryParameterData?.query === 'Agency Name' ? this.agencyMappingForm : this.channelPartnerForm;
    const nameControl = this.queryParameterData?.query === 'Agency Name' ? 'agencyName' : 'channelPartnerName';
    const nameKey = this.queryParameterData?.query === 'Agency Name' ? 'Agency Name' : 'Channel Partner Name';
    const mappingNameForName = this.formKeys?.find(key => key === nameKey);

    mappingColumns.forEach(column => {
      if (column?.displayName && form.controls[column?.value] && this.formKeys?.includes(column?.displayName)) {
        form.patchValue({
          [column?.value]: column?.displayName,
        });
      }
    });

    if (mappingNameForName) {
      form.patchValue({
        [nameControl]: mappingNameForName,
      });
    }
  }

  openBulkUploadedStatusModal() {
    this.navigateToMarketing();
    if (this.modalRef) {
      this.modalRef.hide();
    }

    let initialState: any = {
      fieldType: this.queryParameterData?.query,
    };

    this.modalRef = this.modalService.show(ExcelUploadedStatusComponent, {
      class: 'modal-1100 modal-dialog-centered h-100 tb-modal-unset',
      initialState
    });
  }


  onSheetSelection() {
    this.formKeys = this.excelSheets?.[this.selectedSheet];
  }

  navigateToMarketing() {
    this.router.navigate(['global-config/marketing']);
  }

  resetForms() {
    this.agencyMappingForm.reset();
    this.campaignForm.reset();
    this.channelPartnerForm.reset();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
