import { Component, EventEmitter, OnInit, Output, TemplateRef } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalService } from 'ngx-bootstrap/modal';
import { take, takeUntil } from 'rxjs/operators';
import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, getAssignedToDetails, getPages } from 'src/app/core/utils/common.util';
import { AssignBrevoAccount, DeleteBrevoAccount, FetchBrevoAccountList, UpdateBrevoFilterPayload } from 'src/app/reducers/email/email-settings.action';
import { getBrevoAccountList, getBrevoAccountListIsLoading, getBrevoTotalCount } from 'src/app/reducers/email/email-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchAdminsAndReportees, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'app-brevo-email-settings',
  templateUrl: './brevo-email-settings.component.html',
})
export class BrevoEmailSettingsComponent implements OnInit {
  showEntriesSize: number[] = SHOW_ENTRIES;
  brevoList: any[] = [];
  currOffset: number = 0;
  currPageSize: number = PAGE_SIZE;
  totalCount: number = 0;
  getPages = getPages;
  brevoForm: FormGroup;
  filterPayload: any;
  brevoListIsLoading: boolean;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  modalRef: any;
  @Output() editBrevoAccount = new EventEmitter<any>();
  selectedBrevoAccount: any;
  allAssignUserList: any;
  allUserList: any;
  userList: any;
  assignedUser = new FormControl([]);
  getAssignedToDetails = getAssignedToDetails;
  canAssignToAny: boolean;
  allAdminsAndReportees: any;

  get assignedUserDetails() {
    return this.assignedUser.value;
  }

  constructor(
    private fb: FormBuilder,
    private store: Store<AppState>,
    private modalService: BsModalService,
  ) { }

  ngOnInit(): void {
    this.brevoForm = this.fb.group({
      searchTerm: ['', [Validators.required]],
      pageNumber: [1],
      pageSize: [PAGE_SIZE],
    });
    this.filterFunction();
    this.initializeDispatch()
    this.initializeSubscriptions()
  }

  initializeDispatch() {
    this.store.dispatch(new FetchUsersListForReassignment());
    this.store.dispatch(new FetchAdminsAndReportees());
  }

  initializeSubscriptions() {
    this.store.select(getBrevoAccountList).pipe(takeUntil(this.stopper)).subscribe((resp) => {
      this.brevoList = resp;
    });
    this.store.select(getBrevoAccountListIsLoading).pipe(takeUntil(this.stopper)).subscribe((resp) => {
      this.brevoListIsLoading = resp;
    });
    this.store.select(getBrevoTotalCount).pipe(takeUntil(this.stopper)).subscribe((resp) => {
      this.totalCount = resp;
    });


    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAssignUserList = data;
        this.allUserList = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allUserList = assignToSort(this.allUserList, '');
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAdminsAndReportees = data;
        this.userList = data;
        this.userList = assignToSort(this.userList, '');
      });


    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
        }
      });
  }

  assignPageSize() {
    this.currPageSize = this.brevoForm.value.pageSize;
    this.brevoForm.patchValue({
      pageNumber: 1,
    });
    this.filterFunction();
  }

  onPageChange(event: any) {
    this.currOffset = event;
    this.brevoForm.patchValue({
      pageNumber: event + 1,
    });
    this.filterFunction();
  }

  isEmptyInput(event: any) {
    if (event.target.value === '') {
      this.filterFunction();
    }
  }

  filterFunction() {
    const values = this.brevoForm.value;
    this.filterPayload = {
      ...values,
    };
    this.store.dispatch(new UpdateBrevoFilterPayload(this.filterPayload));
    this.store.dispatch(new FetchBrevoAccountList());
  }

  openDeleteModal(id: string, deleteModal: TemplateRef<any>) {
    this.modalRef = this.modalService.show(deleteModal, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteBrevoAccount(id));
        }
      });
    }
  }

  openEditModal(form: any) {
    this.editBrevoAccount.emit(form);

  }

  onUserSelect(event: any) {
    const lastUser = event[event.length - 1];
    if (lastUser && !lastUser.isActive) {
      event.pop();
    }
    const newlySelectedItems = event.map((item: any) => item?.id);
    this.assignedUser?.setValue(newlySelectedItems);
  }

  openAssignUserModal(form: any, assignModal: TemplateRef<any>) {
    this.selectedBrevoAccount = form;
    this.assignedUser.setValue(form?.emailApiIntegrationData?.userIds ?? []);
    this.modalRef = this.modalService.show(assignModal, {
      class: 'modal-400 top-modal ph-modal-unset',
    });
    this.modalRef.onHide.pipe(take(1)).subscribe(() => {
      this.selectedBrevoAccount = null;
    });
  }

  deleteAssignUser(user: any) {
    const filteredUsers = this.assignedUserDetails.filter((id: string) => id !== user);
    this.assignedUser.setValue(filteredUsers);
  }


  assignUser() {
    let payload = {
      id: this.selectedBrevoAccount.emailApiIntegrationData.id,
      userIds: this.assignedUserDetails
    }
    this.store.dispatch(new AssignBrevoAccount(payload));
    this.modalRef.hide();
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
