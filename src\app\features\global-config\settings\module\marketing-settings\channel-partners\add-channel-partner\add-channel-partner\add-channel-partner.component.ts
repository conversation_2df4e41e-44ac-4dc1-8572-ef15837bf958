import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { BehaviorSubject, debounceTime, distinctUntilChanged, filter, Subject, takeUntil } from 'rxjs';
import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getLocationDetailsByObj } from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { AddChannelPartnerName, ExistChannelPartner, UpdateChannelPartner } from 'src/app/reducers/manage-marketing/marketing.action';
import { getChannelPartnerExist, getIsCPAddLoading, getIsCPUpdateLoading } from 'src/app/reducers/manage-marketing/marketing.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-channel-partner',
  templateUrl: './add-channel-partner.component.html',
})
export class AddChannelPartnerComponent implements OnInit, OnDestroy {
  @ViewChild('phoneNumber') phoneNumber: any;
  searchPlaceTerm$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private unsubscribe$ = new Subject<void>();

  preferredCountries = ['in'];
  hasInternationalSupport = false;
  addChannelPartnerForm: FormGroup;
  selectedChannelPartner: any;
  isShowManualCustomerLocation = false;
  placesList: any[] = [];
  isLoadingPlaces = false;
  doesExistChannelPartner: boolean;
  isAddEditCP: boolean = false;

  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.fetchingGS();
    this.fetchLocationUpdates();

    if (this.selectedChannelPartner) {
      this.patchCPForm(this.selectedChannelPartner);
    }

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchTerm: string) => searchTerm && searchTerm.trim() !== ''),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((searchTerm: string) => {
        if (searchTerm) {
          this.isLoadingPlaces = true;
          this.store.dispatch(new FetchLocationsWithGoogle(searchTerm));
        }
      });

  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private initForm(): void {
    this.addChannelPartnerForm = this.fb.group({
      channelPartnerName: [null, Validators.required],
      phoneNumber: [null, this.contactNumberValidator()],
      email: [null, ValidationUtil.emailValidatorMinLength],
      location: [null],
      customerLocality: null,
      customerCity: null,
      customerState: null,
      reraNumber: [null],
      companyName: [null],
    });
  }

  private fetchingGS(): void {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.unsubscribe$.asObservable()))
      .subscribe((data: any) => {
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = this.hasInternationalSupport
          ? data.countries.length
            ? [data.countries[0].code.toLowerCase()]
            : ['in']
          : ['in'];
      });
  }

  private fetchLocationUpdates(): void {
    this.store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.unsubscribe$.asObservable()))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
        this.isLoadingPlaces = false;
      });
  }

  contactNumberValidator(): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const input = document.querySelector(
        '.phoneNumber > div > input'
      ) as HTMLInputElement;
      if (!input?.value?.length) {
        return null;
      }
      defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      try {
        const validNumber = isPossiblePhoneNumber(
          this.phoneNumber?.value || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }
  getSelectedCountryCodeContactNo(): any {
    return this.phoneNumber?.selectedCountry;
  }

  onSubmit(): void {
    if (this.addChannelPartnerForm.valid) {
      this.isAddEditCP = true;
      let payload;
      const placeId = this.addChannelPartnerForm.value.location?.placeId;
      const locationId = this.addChannelPartnerForm.value.location?.locationId;
      if (this.isShowManualCustomerLocation) {
        payload = {
          ...(this.selectedChannelPartner ? { id: this.selectedChannelPartner.id } : {}),
          ...(this.selectedChannelPartner ? { firmName: this.addChannelPartnerForm.value.channelPartnerName } : { firmName: this.addChannelPartnerForm.value.channelPartnerName }),
          contactNo: this.addChannelPartnerForm.value.phoneNumber,
          email: this.addChannelPartnerForm.value.email,
          address: {
            placeId: placeId && placeId !== "" ? placeId : null,
            locationId: (!placeId || placeId === "") && locationId ? locationId : null,
            subLocality: this.addChannelPartnerForm.value.customerLocality,
            city: this.addChannelPartnerForm.value.customerCity,
            state: this.addChannelPartnerForm.value.customerState,
          },
          reraNumber: this.addChannelPartnerForm.value.reraNumber,
          companyName: this.addChannelPartnerForm.value.companyName,
        };
      } else {
        payload = {
          ...(this.selectedChannelPartner ? { id: this.selectedChannelPartner.id } : {}),
          firmName: this.addChannelPartnerForm.value.channelPartnerName,
          contactNo: this.addChannelPartnerForm.value.phoneNumber,
          email: this.addChannelPartnerForm.value.email,
          address: {
            placeId: placeId && placeId !== "" ? placeId : null,
            locationId: (!placeId || placeId === "") && locationId ? locationId : null,
            subLocality: this.addChannelPartnerForm.value.customerLocality,
            city: this.addChannelPartnerForm.value.customerCity,
            state: this.addChannelPartnerForm.value.customerState,
          },
          reraNumber: this.addChannelPartnerForm.value.reraNumber,
          companyName: this.addChannelPartnerForm.value.companyName,
        };
      }

      if (this.selectedChannelPartner) {
        this.store.dispatch(new UpdateChannelPartner(payload));
        this.store.select(getIsCPUpdateLoading).subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isAddEditCP = false;
            this.modalRef.hide();
          }
        });
      } else {
        this.store.dispatch(new AddChannelPartnerName(payload));
        this.store.select(getIsCPAddLoading).subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.modalRef.hide();
            this.isAddEditCP = false;
          }
        });
      }
    } else {
      this.addChannelPartnerForm.markAllAsTouched();
    }
  }


  patchCPForm(data: any): void {
    const isLocationAvailable = data?.address?.subLocality || data?.address?.city || data?.address?.state;
    this.addChannelPartnerForm.patchValue({
      channelPartnerName: data?.firmName || null,
      phoneNumber: data?.contactNo || null,
      email: data?.email || null,
      location: isLocationAvailable
        ? { id: EMPTY_GUID, location: getLocationDetailsByObj(data?.address), placeId: data?.address?.placeId }
        : null,
      customerLocality: data?.address?.locality ? data?.address?.locality : data?.address?.subLocality || null,
      customerCity: data?.address?.city || null,
      customerState: data?.address?.state || null,
      reraNumber: data?.reraNumber || null,
      companyName: data?.companyName || null,
    });
  }

  doesChannelPartnerExist(cp: string) {
    if (cp) {
      this.store.dispatch(new ExistChannelPartner(cp));
      this.store.select(getChannelPartnerExist).subscribe((doesExistChannelPartner: boolean) => {
        this.doesExistChannelPartner = doesExistChannelPartner;
        this.addChannelPartnerForm.get('channelPartnerName').setErrors(
          doesExistChannelPartner ? { alreadyExist: true } : null
        );
      });
    }
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Backspace' || event.keyCode === 8) {
      this.searchPlaceTerm$.next('');
    }
  }
}
