<div class="py-10 d-flex align-center">
    <img alt="img" ng-reflect-type="leadrat" ng-reflect-app-image="assets/images/integration/Brevo"
        src="assets/images/integration/brevo.svg">
    <h2 class="fw-500 text-black-10 ml-10">Brevo</h2>
</div>
<div class="pt-10" [formGroup]="brevoForm">
    <div class="bg-white w-100 border-gray flex-between">
        <div class="align-center border-end  w-100 py-10 px-12 no-validation">
            <span class="search icon ic-search ic-sm ic-slate-90 mr-12 ph-mr-4"> </span>
            <input placeholder="type to search" name="searchQrForm" class="border-0 outline-0 w-100" autocomplete="off"
                id="inpSearchQrForm" formControlName="searchTerm" (keydown.enter)="filterFunction()"
                (input)="isEmptyInput($event)">
            <small class="text-muted text-nowrap ph-d-none pr-8">({{ 'LEADS.lead-search-prompt' | translate
                }})</small>
        </div>
        <div class="show-dropdown-white align-center position-relative ip-br-0">
            <span class="fw-600 position-absolute left-5 z-index-2"><span class="tb-d-none">
                    {{ 'GLOBAL.show' | translate}}</span> {{ 'GLOBAL.entries' | translate }}</span>
            <ng-select [items]="showEntriesSize" formControlName="pageSize" (change)="assignPageSize()"
                [virtualScroll]="true" class="w-150 tb-w-120px" [searchable]="false">
            </ng-select>
        </div>
    </div>
</div>
<div class="scrollbar scroll-hide tb-w-100-40 table-scrollbar" *ngIf="!brevoListIsLoading; else loader">
    <table class="table standard-table no-vertical-border" *ngIf="brevoList?.length; else noData">
        <thead [ngClass]="brevoList?.length ?'':'ph-d-none'">
            <tr class="w-100 text-nowrap">
                <th class="w-150 ">Reference Account Name</th>
                <th class="w-150">Service Provider Name</th>
                <th class="w-150">No. of Templates</th>
                <th class="w-150">Integration Status</th>
                <th class="w-150">Actions</th>
            </tr>
        </thead>
        <ng-container *ngIf="brevoList?.length">
            <tbody class="text-secondary fw-semi-bold max-h-100-270">

                <tr *ngFor="let form of brevoList; let i = index">
                    <td class="w-150 text-truncate">{{form.senderEmailAddress}}</td>
                    <td class="w-150 text-truncate">Brevo</td>
                    <td class="w-150 text-truncate">{{form.noOfTemplates}}</td>
                    <td class="w-150 text-truncate">
                        <span class="status-label-badge"
                            [ngStyle]="{'background-color': form.status ? '#63AEFF66' : '#FECCCC66'}">
                            <p class="mr-6" [ngStyle]="{'color': form.status ? '#007AFF' : '#ED5454'}">{{form.status ?
                                'Completed' : 'Not Completed'}}</p>
                        </span>
                    </td>
                    <td class="w-150">
                        <div class="align-center">
                            <div title="Edit" class="bg-accent-green icon-badge" (click)="openEditModal(form)">
                                <span class="icon m-auto ic-xxs ic-pen"></span>
                            </div>
                            <div title="Assign Users" class="bg-blue-800 icon-badge"
                                (click)="openAssignUserModal(form,assign)">
                                <span class="icon ic-assign-to m-auto ic-xxs"></span>
                            </div>
                            <div title="Delete" class="bg-light-red icon-badge"
                                (click)="openDeleteModal(form.id,delete)">
                                <span class="icon ic-delete m-auto ic-xxs"></span>
                            </div>
                        </div>
                    </td>
                </tr>

            </tbody>
        </ng-container>
    </table>
</div>
<div class="mt-16  flex-end" *ngIf="brevoList?.length>0">
    <div class="mr-10">{{ 'GLOBAL.showing' | translate }} {{currOffset*currPageSize + 1}}
        {{ 'GLOBAL.to-small' | translate }} {{currOffset*currPageSize + brevoList?.length}}
        {{ 'GLOBAL.of-small' | translate }} {{totalCount}} {{ 'GLOBAL.entries-small' | translate }}</div>
    <pagination [offset]="currOffset" [limit]="1" [range]="1" [size]='getPages(totalCount,currPageSize)'
        (pageChange)="onPageChange($event)">
    </pagination>
</div>

<ng-template #loader>
    <div class="flex-center h-330">
        <application-loader></application-loader>
    </div>
</ng-template>
<ng-template #noData>
    <div class="flex-col flex-center h-100-260 min-h-250">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">No Data Found</div>
    </div>
</ng-template>

<ng-template #delete>
    <div class="p-20">
        <h4 class="text-black-100 fw-semi-bold mb-20 text-center word-break line-break">Are you sure you want to delete
            this Brevo Account?</h4>
        <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: Deleted Brevo Account cannot be retrieved
            again.
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" id="deleteNo" data-automate-id="deleteNo" (click)="modalRef.hide()">
                {{ 'GLOBAL.no' | translate }}</button>
            <button class="btn-green" id="deleteYes" data-automate-id="deleteYes"
                (click)="modalService.setDismissReason('confirmed');modalRef.hide()">
                {{ 'GLOBAL.yes' | translate }}</button>
        </div>
    </div>
</ng-template>

<ng-template #assign>
    <h5 class="text-white fw-600 bg-black px-20 py-12">Assign Users</h5>
    <form autocomplete="off" class="pb-20 px-30">
        <div class="field-label">Assign To</div>
        <div class="ng-select-sm-gray">
            <ng-select [virtualScroll]="true" placeholder="ex. Mounika Pampana"
                [items]="canAssignToAny ? allUserList : userList" [multiple]="true" [closeOnSelect]="false"
                bindLabel="fullName" bindValue="id" name="assignedUser" [formControl]="assignedUser" class="bg-white"
                (change)="onUserSelect($event)">
                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                    <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                </ng-template>
                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                    <div class="checkbox-container">
                        <input type="checkbox" id="item-{{index}}" data-automate-id="item-{{index}}"
                            [checked]="item$.selected" [disabled]="!item.isActive">
                        <span class="checkmark"></span>
                        <span class="text-truncate-1 break-all">{{item.firstName}} {{item.lastName}}</span>
                        <span class="error-message-custom top-13" *ngIf="!item.isActive">( Disabled )</span>
                    </div>
                </ng-template>
            </ng-select>
        </div>
        <div class="flex-end mt-30">
            <button class="btn-gray mr-20" id="brevoAccountCancel" data-automate-id="brevoAccountCancel"
                (click)="modalRef.hide()">{{
                'BUTTONS.cancel' | translate }}</button>
            <button class="btn-coal" id="brevoAccount" data-automate-id="brevoAccount" (click)="assignUser()">
                {{ 'Assign' }}
            </button>
        </div>
    </form>

</ng-template>