<div class="h-100vh text-coal">
    <div class="bg-coal w-100 px-16 py-12 text-white flex-between">
        <h3 class="fw-semi-bold">{{selectedTemplate ? 'Update Brevo Template' : 'Add Brevo Template'}}</h3>
        <div class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></div>
    </div>
    <div class="h-100-110 scrollbar ip-min-w-350 ip-max-w-350">
        <form [formGroup]="addBrevoTemplateForm" autocomplete="off" class="pb-20 px-20">
            <div class="field-label-req">Reference Account Name</div>
            <form-errors-wrapper [control]="addBrevoTemplateForm.controls['emailApiIntegrationId']"
                label="reference account name">
                <ng-select formControlName="emailApiIntegrationId" [ngClass]="{'pe-none disabled': selectedTemplate}"
                    [items]="brevoAccountListByName" bindLabel="senderEmailAddress"
                    bindValue="emailApiIntegrationDataId" placeholder="Select Account Name" [virtualScroll]="true"
                    class="w-100 bg-white" [searchable]="true">
                </ng-select>
            </form-errors-wrapper>
            <div class="field-label-req">Template Name</div>
            <form-errors-wrapper [control]="addBrevoTemplateForm.controls['templateName']" label="template name">
                <input type="text" formControlName="templateName" placeholder="Enter Template Name">
            </form-errors-wrapper>
            <div class="field-label-req">Subject</div>
            <form-errors-wrapper [control]="addBrevoTemplateForm.controls['subject']" label="subject">
                <input type="text" formControlName="subject" placeholder="Enter Subject">
            </form-errors-wrapper>
            <div class="field-label-req">Body</div>
            <form-errors-wrapper [control]="addBrevoTemplateForm.controls['body']" label="body">
                <textarea formControlName="body" minlength="10" rows="8" placeholder="Enter Body"
                    id="txtLeadMsg"></textarea>
            </form-errors-wrapper>

        </form>
    </div>
    <div class="position-fixed p-12 bottom-0 border-top flex-between w-100 bg-white">
        <div class="align-center ph-flex-col">
            <div class="position-relative">
                <div class="my-4">
                    <button class="btn-coal" (click)="isShowVariablePopup = !isShowVariablePopup">
                        <span> # Select {{ 'GLOBAL.variable' | translate }}</span>
                    </button>
                </div>
                <div class="position-absolute bg-light-pearl w-460 ip-max-w-340 br-10 z-index-2 left-0 bottom-40"
                    *ngIf="isShowVariablePopup">
                    <div class="br-4 p-12 bg-slate border">
                        <div class="flex-between">
                            <div class="align-center">
                                <div class="icon ic-xxs mr-4 ic-coal"
                                    [ngClass]="isData ? 'ic-address-card-solid' : 'ic-secondary-filter-solid'"></div>
                                <h5 class="fw-600 text-coal">{{ 'Variables' }}</h5>
                            </div>
                            <div class="icon ic-close ic-sm ic-coal cursor-pointer"
                                (click)="isShowVariablePopup = false"></div>
                        </div>
                        <div class="pt-12 d-flex flex-wrap w-100 max-h-100-300 scrollbar mt-10">
                            <div class="py-4 px-8 border br-10 mr-8 mb-8 cursor-pointer"
                                *ngFor="let variable of variables;let i=index"
                                (click)="addVariable(variable.value, $event)">
                                {{(i + 1) + '. ' + variable.displayName}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex-end">
            <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">{{'BUTTONS.cancel' |
                translate }}</u>
            <button class="btn-coal" *ngIf="!isTemplateCreating ; else buttonDots" (click)="onSave()">{{selectedTemplate
                ?
                'Update' : 'Save'}}</button>
        </div>
    </div>
</div>

<ng-template #buttonDots>
    <button class="btn-coal">
        <div class="container flex-center py-8">
            <ng-container *ngFor="let dot of [1,2,3]">
                <div class="dot-falling dot-white"></div>
            </ng-container>
        </div>
    </button>
</ng-template>