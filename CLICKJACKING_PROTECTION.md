# Clickjacking Protection Implementation

This document describes the comprehensive clickjacking protection framework implemented in the Angular application.

## Overview

Clickjacking is a malicious technique where an attacker tricks users into clicking on something different from what they perceive, potentially compromising their security. This implementation provides multiple layers of protection against clickjacking attacks.

## Implementation Components

### 1. Enhanced Iframe Busting (index.html)

**Location**: `src/index.html`

The HTML file includes enhanced iframe busting code that:
- Detects if the page is being framed
- Uses multiple techniques to break out of frames
- Implements style-based protection
- Provides continuous monitoring

**Features**:
- Basic frame detection and redirection
- Style-based protection (hides content until verified)
- Delayed verification with fallback
- Continuous monitoring every 1000ms

### 2. Clickjacking Prevention Utilities

**Location**: `src/app/core/utils/common.util.ts`

Utility functions for clickjacking protection:

```typescript
// Enhanced iframe busting
preventClickjacking(): void

// Advanced framing detection
detectFraming(): boolean

// Multi-technique frame busting
frameBusting(): void

// Initialize complete protection
initializeClickjackingProtection(): void
```

### 3. Clickjacking Prevention Service

**Location**: `src/app/core/services/clickjacking-prevention.service.ts`

Main service that manages clickjacking protection:

**Key Methods**:
- `enableProtection()` - Enable protection
- `disableProtection()` - Disable protection (use with caution)
- `isFramed()` - Check if currently framed
- `bustFrame()` - Manually trigger frame busting
- `setAllowedOrigins()` - Configure allowed origins
- `getSecurityHeaders()` - Get security headers for HTTP requests

### 4. Security Configuration Service

**Location**: `src/app/core/services/security-config.service.ts`

Manages security configuration with persistent storage:

**Configuration Options**:
```typescript
interface SecurityConfig {
  clickjackingProtection: {
    enabled: boolean;
    frameOptions: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
    allowedOrigins: string[];
    continuousMonitoring: boolean;
    monitoringInterval: number;
  };
  contentSecurityPolicy: {
    enabled: boolean;
    frameAncestors: string;
    customDirectives: { [key: string]: string };
  };
  additionalHeaders: {
    xContentTypeOptions: boolean;
    xXSSProtection: boolean;
    referrerPolicy: string;
  };
}
```

### 5. Security Headers Interceptor

**Location**: `src/app/core/interceptors/security-headers.interceptor.ts`

HTTP interceptor that adds security headers to requests:

**Headers Added**:
- `X-Frame-Options`: Controls iframe embedding
- `Content-Security-Policy`: CSP with frame-ancestors directive
- `X-Content-Type-Options`: Prevents MIME sniffing
- `X-XSS-Protection`: Enables XSS filtering
- `Referrer-Policy`: Controls referrer information

### 6. Admin Configuration Component

**Location**: `src/app/features/global-config/settings/module/security-settings/clickjacking-settings.component.ts`

Admin interface for configuring clickjacking protection:

**Features**:
- Enable/disable protection
- Configure X-Frame-Options policy
- Manage allowed origins
- Set monitoring intervals
- Configure additional security headers
- Test protection functionality

## Security Headers

### X-Frame-Options

Controls whether the application can be embedded in frames:

- `DENY`: Prevents all framing
- `SAMEORIGIN`: Allows framing only from the same origin
- `ALLOW-FROM uri`: Allows framing from specific origins (deprecated)

### Content Security Policy (CSP)

The `frame-ancestors` directive controls which sources can embed the page:

- `frame-ancestors 'none'`: Prevents all framing
- `frame-ancestors 'self'`: Allows same-origin framing
- `frame-ancestors 'self' https://example.com`: Allows specific origins

## Configuration

### Default Configuration

```typescript
{
  clickjackingProtection: {
    enabled: true,
    frameOptions: 'DENY',
    allowedOrigins: [],
    continuousMonitoring: true,
    monitoringInterval: 2000
  },
  contentSecurityPolicy: {
    enabled: true,
    frameAncestors: "'none'",
    customDirectives: {
      'default-src': "'self' 'unsafe-inline' 'unsafe-eval' data: https:",
      'img-src': "'self' data: https:",
      'font-src': "'self' data: https:"
    }
  },
  additionalHeaders: {
    xContentTypeOptions: true,
    xXSSProtection: true,
    referrerPolicy: 'strict-origin-when-cross-origin'
  }
}
```

### Customization

Administrators can customize the configuration through the admin panel:

1. Navigate to Global Config → Settings → Security Settings
2. Click "Configure" next to "Clickjacking Protection"
3. Adjust settings as needed
4. Save changes

## Usage

### Automatic Initialization

The protection is automatically initialized when the application starts:

```typescript
// In app.component.ts
ngOnInit(): void {
  // Initialize clickjacking protection
  this.clickjackingService.enableProtection();
  // ... other initialization code
}
```

### Manual Control

```typescript
// Enable protection
this.clickjackingService.enableProtection();

// Disable protection (use with caution)
this.clickjackingService.disableProtection();

// Check if framed
if (this.clickjackingService.isFramed()) {
  console.warn('Page is being framed!');
}

// Set allowed origins for SAMEORIGIN policy
this.clickjackingService.setAllowedOrigins([
  'https://trusted-domain.com',
  'https://partner-site.com'
]);
```

## Testing

### Manual Testing

1. Open the admin panel and navigate to Security Settings
2. Click "Test Protection" in the Clickjacking Settings modal
3. The system will check if the page is currently framed

### Automated Testing

Create test cases to verify:
- Frame detection works correctly
- Headers are properly set
- Configuration changes are applied
- Protection can be enabled/disabled

## Best Practices

1. **Keep Protection Enabled**: Only disable in development if absolutely necessary
2. **Minimal Allowed Origins**: Only add trusted domains to allowed origins
3. **Regular Monitoring**: Check logs for clickjacking attempts
4. **Server-Side Headers**: Ensure server also sets security headers
5. **CSP Compliance**: Ensure CSP directives don't conflict with application needs

## Server-Side Configuration

While this implementation provides client-side protection, server-side headers are also recommended:

### Apache
```apache
Header always set X-Frame-Options "DENY"
Header always set Content-Security-Policy "frame-ancestors 'none'"
```

### Nginx
```nginx
add_header X-Frame-Options "DENY" always;
add_header Content-Security-Policy "frame-ancestors 'none'" always;
```

### Express.js
```javascript
app.use((req, res, next) => {
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Content-Security-Policy', "frame-ancestors 'none'");
  next();
});
```

## Troubleshooting

### Common Issues

1. **Legitimate Embedding Blocked**: Add the domain to allowed origins
2. **CSP Violations**: Adjust CSP directives in configuration
3. **Protection Not Working**: Check if disabled in configuration
4. **Performance Impact**: Adjust monitoring interval if needed

### Debug Mode

Enable debug logging in development:

```typescript
// In environment.ts
export const environment = {
  production: false,
  debugSecurity: true
};
```

## Security Considerations

1. **Defense in Depth**: This is one layer of protection; use with other security measures
2. **Browser Support**: Some older browsers may not support all features
3. **Bypass Attempts**: Attackers may try to disable JavaScript; server-side headers are crucial
4. **Regular Updates**: Keep the protection mechanisms updated

## Compliance

This implementation helps meet security requirements for:
- OWASP Top 10
- PCI DSS
- SOC 2
- ISO 27001

## Support

For issues or questions regarding clickjacking protection:
1. Check the configuration in admin panel
2. Review browser console for errors
3. Verify server-side headers are set
4. Contact the development team for assistance
