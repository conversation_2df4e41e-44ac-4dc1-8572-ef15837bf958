<ng-container>
    <div routerLink='/global-config'
        class="icon ic-circle-chevron-left ml-10 ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
    </div>
    <div class="p-24 position-relative">
        <div class="flex-between align-center mb-20">
            <div class="align-center">
                <div routerLink="/global-config"
                    class="icon ic-triangle-up rotate-270 ic-x-xs ic-black cursor-pointer mr-10" tabindex="0"></div>
                <span class="icon ic-setting-connections ic-sm ic-blue-1150 mr-8"></span>
                <h5 class="fw-600 header-3">Automation Settings</h5>
            </div>
        </div>

        <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
            <div>
                <h5 class="fw-600">Automated Reports</h5>
                <h6 class="text-dark-gray pt-4">Configure automation to get your reports automatically</h6>
            </div>
            <div class="align-center mr-40 ph-mr-20" (click)="navigateToReportAutomation()">
                <h6 class="cursor-pointer text-black-200 fw-400">Click to manage<span
                        class="ml-20 rotate-90 icon ic-black ic-triangle-up ic-xxxs"></span></h6>
            </div>
        </div>
        <div *ngIf="isAutoDialerEnabled" class="bg-white pl-20 py-16 mt-20 br-6">
            <div class="flex-between w-100">
                <div>
                    <h5 class="fw-600">Auto Dialer</h5>
                    <h6 class="text-dark-gray pt-4">Configure your automation to get your IVR integrations</h6>
                </div>
                <div class="align-center mr-40 ph-mr-20" (click)="isAutoDialerOpen = !isAutoDialerOpen">
                    <h6 class="cursor-pointer text-black-200 fw-400">Click to manage<span
                            class="ml-20  icon ic-black ic-xxxs"
                            [ngClass]="isAutoDialerOpen ? 'ic-triangle-down' : 'ic-triangle-up rotate-90'"></span></h6>
                </div>
            </div>
            <ng-container *ngIf="isAutoDialerOpen">
                <form [formGroup]="autoDialerForm" class="mt-20 pr-20">
                    <div class="px-20 py-16 br-4 border">
                        <div class="d-flex flex-wrap">
                            <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                                <div class="form-group mr-20 mb-4">
                                    <div class="label">Select Users</div>
                                    <ng-select [virtualScroll]="true"
                                        [items]="canAssignToAny ? allActiveUsers : activeUsers" [multiple]="true"
                                        ResizableDropdown [closeOnSelect]="false" bindLabel="fullName" bindValue="id"
                                        name="userIds" formControlName="userIds" placeholder="Select Users"
                                        class="bg-white">
                                        <ng-template ng-label-tmp let-item="item">
                                            {{item.firstName}} {{item.lastName}}
                                        </ng-template>
                                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                            <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                    data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                    class="checkmark"></span>{{item.firstName}}
                                                {{item.lastName}}</div>
                                        </ng-template>
                                    </ng-select>
                                </div>
                            </div>
                            <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                                <div class="mr-20">
                                    <div class="label">Call Interval</div>
                                    <form-errors-wrapper label="Rotation time" autocomplete="off">
                                        <div class="align-center position-relative">
                                            <input type="number" min="1" placeholder="ex. enter..."
                                                class="brbr-0 brtr-0" formControlName="maxCallInterval">
                                            <div class="position-absolute right-10">
                                                <span class="mr-4 px-4 py-2 border-left"></span>
                                                <span class="text-xs text-light-slate">Minutes</span>
                                            </div>
                                        </div>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-16pr mqd-w-25 tb-w-50 ip-w-100">
                                <div class="mr-20">
                                    <div class="label">Priority</div>
                                    <div class="align-center py-12 px-8 bg-slate py-4">
                                        <label class="checkbox-container">
                                            <input type="checkbox" [checked]="autoDialerForm.get('priority').value"
                                                formControlName="priority">
                                            <span class="checkmark"></span>
                                            <h5>Priority Integration Leads</h5>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex-end mt-20 mr-20">
                            <button class="btn-gray mr-10" (click)="cancelAutoDialer()">
                                Cancel
                            </button>
                            <button class="btn-coal" (click)="saveAutoDialerSettings()">
                                Save and Apply
                            </button>
                        </div>
                    </div>
                </form>
            </ng-container>
        </div>
    </div>
</ng-container>