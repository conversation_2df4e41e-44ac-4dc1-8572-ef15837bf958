<div routerLink="/leads/manage-leads" [ngClass]="showLeftNav ? 'left-150' : 'left-55'"
    class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
</div>
<div class="flex-between p-4 bg-white border-gray-y">
    <div class="align-center ml-20 top-nav-bar text-nowrap scrollbar ip-w-100-225 w-100-423 scroll-hide">
        <ng-container *ngFor="let filter of filterTabs">
            <div [title]="filter?.displayName" class="cursor-pointer" (click)="onFilterChange(filter.id)">
                <div class="align-center ph-mb-4">
                    <span class="text-large fw-semi-bold mx-8 d-flex" [class.active]="selectedFilter === filter.id">{{
                        filter?.displayName
                        }}
                        <ng-container *ngIf="!isCountsLoading; else dotLoader">
                            ({{ filter.count }})
                        </ng-container>
                    </span>
                </div>
            </div>
        </ng-container>
    </div>
    <div class="align-center mr-30">
        <div class="align-center">
            <span class="mr-10 text-sm text-muted">unavailable</span>
            <input type="checkbox" class="toggle-switch toggle-availability" [(ngModel)]="isAvailable"
                (change)="onAvailabilityToggle()" id="availabilityToggle" />
            <label for="availabilityToggle" class="switch-label"></label>
            <span class="ml-10 text-sm text-muted">available</span>
        </div>
    </div>
</div>
<div class="pt-16 px-30">
    <div class="align-center bg-white w-100 border-gray">
        <div class="align-center flex-grow-1 no-validation border-end">
            <div class="position-relative flex-grow-1">
                <div class="align-center w-100 px-10 py-12">
                    <span class="search icon ic-search ic-sm ic-slate-90 mr-12"></span>
                    <input type="text" placeholder="type to search" [(ngModel)]="SearchByNameAndNumber"
                        (input)="isEmptyInput()" (keydown)="onSearch($event)" [ngModelOptions]="{standalone: true}"
                        class="border-0 outline-0 w-100" />
                </div>
            </div>
            <div class="flex-end border-left">
                <div class="px-10 align-center cursor-pointer border-end tb-flex-grow-1"
                    (click)="openAdvFiltersModal(AdvancedFilters)">
                    <div class="icon ic-filter-solid ic-xxs ic-black mr-10"></div>
                    <span class="fw-600 ph-d-none">{{'PROPERTY.advanced-filters' | translate}}</span>
                </div>
                <div class="bg-dark text-white px-20 py-12 align-center cursor-pointer" (click)="refreshData()">
                    <span class="ic-update icon mr-8 ic-xxs"></span>
                    Refresh
                </div>
            </div>
            <div class="show-dropdown-white align-center position-relative">
                <ng-select [virtualScroll]="true" [placeholder]="pageSize" class="w-75px" [searchable]="false"
                    ResizableDropdown (change)="onPageSizeChange($event)" [(ngModel)]="selectedPageSize">
                    <ng-option name="showEntriesSize" *ngFor="let pageSize of showEntriesSize" [value]="pageSize">
                        {{pageSize}}</ng-option>
                </ng-select>
            </div>
        </div>
    </div>
    <div class="bg-white px-4 py-8 tb-w-100-34 w-100-190">
        <div class="bg-secondary flex-between" *ngIf="showFilters">
            <drag-scroll class="br-4 overflow-auto d-flex scroll-hide w-100">
                <div class="d-flex" *ngFor="let filter of filtersPayload | keyvalue">
                    <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap"
                        *ngFor="let value of getArrayOfFilters(filter.key, filter.value)">
                        {{autoDialerKeylabel[filter.key] || filter.key}}: {{
                        filter.key === 'CallStatus'? getCallStatus(value) :
                        filter.key === 'UserIds' ? getUserName(value) :
                        value }}
                        <span class="icon ic-cancel ic-dark ic-x-xs cursor-pointer text-light-slate ml-4"
                            (click)="onRemoveFilter(filter.key, value)"></span>
                    </div>
                </div>
            </drag-scroll>
            <div class="px-8 py-4 bg-slate-120 m-4 br-4 text-mud text-center fw-semi-bold text-nowrap cursor-pointer"
                (click)="clearAll()">
                {{'BUTTONS.clear' | translate}} {{'GLOBAL.all' | translate}}</div>
        </div>
    </div>
</div>
<div class="px-30">
    <ng-container *ngIf="!isDataLoading;else dataLoader">
        <div class="manage-leads pinned-grid" *ngIf="rowData?.length; else noData">
            <ag-grid-angular class="ag-theme-alpine" #agGrid [gridOptions]="gridOptions"
                (gridReady)="onGridReady($event)" [rowData]="rowData" [suppressPaginationPanel]="true"
                [alwaysShowHorizontalScroll]="true" [alwaysShowVerticalScroll]="true">
            </ag-grid-angular>
        </div>
        <div class="justify-center">
            <div class="position-absolute bg-white bottom-12 br-12 flex-between box-shadow-10 p-16 z-index-2"
                [ngClass]="{'d-none': !gridApi?.getSelectedNodes()?.length}">
                <div class="align-center tb-mb-10">
                    <div class="fw-600 text-coal mr-20 text-xl">{{gridApi?.getSelectedNodes()?.length}}
                        {{gridApi?.getSelectedNodes()?.length > 1 ? 'Items' : 'Item'}} {{ 'LEADS.selected' | translate}}
                    </div>
                </div>
                <div class="flex-center flex-wrap">
                    <button class="btn-bulk-red" (click)="openBulkDeleteModal(BulkDeleteModal)"
                        [disabled]="gridApi?.getSelectedNodes()?.length < 1" id="btnBulkDelete"
                        data-automate-id="btnBulkDelete">
                        {{ 'LEADS.bulk' | translate }} {{ ('BUTTONS.delete') | translate }}</button>
                </div>
            </div>
        </div>
        <div class="flex-end my-20">
            <div class="mr-10">Showing
                {{ (currentPage - 1) * pageSize + 1 }}
                to
                {{ (currentPage - 1) * pageSize + rowData?.length }}
                of {{ totalCount }} entries
            </div>
            <pagination [offset]="currentPage - 1" [limit]="1" [range]="1" [size]="getPages(totalCount, pageSize)"
                (pageChange)="onPageChange($event)">
            </pagination>
        </div>
    </ng-container>
</div>
<!-- Floating Dialer Widget -->
<div *ngIf="currentLead && (currentLead?.assignTo === userDetails || currentLead?.secondaryUserId === userDetails)"
    class="bottom-28 z-index-1000" [ngClass]="isLeadPreviewOpen ? 'right-580' : 'right-28'"
    [ngStyle]="{'top.px': positionY, 'right.px': 28, 'position': 'fixed', 'width.px': 300}"
    (mousedown)="startVerticalDrag($event)">
    <div class="bg-dark text-white border shadow-lg border-gray brtl-8 brbl-8 p-16 min-w-300 cursor-pointer"
        (click)="openLeadPreview(currentLead?.id)" title="Click to view lead details">
        <div class="d-flex align-center mb-12">
            <div class="bg-success brtl-8 brbl-8 p-8 mr-12 d-flex align-center justify-content-center">
                <span class="ic-phone  icon ic-sm text-white"></span>
            </div>
            <div class="flex-grow-1">
                <div class="fw-600 text-white mb-4">{{ currentLead?.name || 'Lead Name01' }}</div>
                <div class="d-flex">
                    <div class="text-white dot dot-sm text-sm fw-semi-bold mr-10 border-silver-gradient">P</div>
                    <h6 class="fw-400 text-light-gray text-truncate-1 break-all">
                        {{getAssignedToDetails(currentLead?.assignTo,allUsers, true)}}
                    </h6>
                    <ng-container
                        *ngIf="currentLead?.secondaryUserId !== emptyGuid && globalSettingsData?.isDualOwnershipEnabled">
                        <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mx-10 border-gold-gradient">
                            S
                        </div>
                        <h6 class="fw-400 text-light-gray text-truncate-1 break-all">
                            {{getAssignedToDetails(currentLead?.secondaryUserId,allUsers, true)}}
                        </h6>
                    </ng-container>
                </div>
            </div>
        </div>
        <div class="flex-between">
            <div class="bg-pearl-170 text-white border-white br-20 px-12 py-6">
                <span class="text-xs fw-600">{{ duration }}</span>
            </div>
            <div class="align-center ml-auto">
                <button class="btn btn-sm btn-danger br-50 p-8" title="End Call">
                    <span class="ic-phone icon ic-xs"></span>
                </button>
            </div>
        </div>
    </div>
</div>
<ng-template #AdvancedFilters>
    <form [formGroup]="filterForm">
        <div class="lead-adv-filter p-30 bg-white brbl-15 brbr-15">
            <div class="adv-filter">
                <div class="d-flex w-100 flex-wrap ng-select-sm">
                    <div class="flex-column w-50 ph-w-100">
                        <div class="field-label">Users</div>
                        <div class="mr-20 ph-mr-0">
                            <ng-select [virtualScroll]="true" [items]="canViewAllUsers ? allUsersList : onlyReportees"
                                [multiple]="true" [closeOnSelect]="false" name="UserIds" formControlName="UserIds"
                                placeholder="Select Users" ResizableDropdown bindLabel="fullName" bindValue="id"
                                [ngClass]="{'pe-none blinking': canViewAllUsers ? allUserListIsLoading : isReporteesWithInactiveLoading}">
                                <ng-template ng-label-tmp let-item="item" let-clear="clear">
                                    <span class="ic-cancel ic-dark icon ic-x-xs mr-4" (click)="clear(item)"></span>
                                    <span class="ng-value-label"> {{item.firstName + ' ' + item.lastName}}</span>
                                </ng-template>
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="flex-between">
                                        <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                                data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                                class="checkmark"></span><span
                                                class="text-truncate-1 break-all">{{item.firstName}}
                                                {{item.lastName}}</span>
                                        </div>
                                        <span class="text-disabled" *ngIf="!item.isActive">( Disabled )</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="flex-column w-50 ph-w-100">
                        <div class="field-label">Call Status</div>
                        <div class="mr-20">
                            <ng-select [virtualScroll]="true" [items]="callStatusList" bindLabel="displayName"
                                bindValue="value" [multiple]="true" [closeOnSelect]="false" ResizableDropdown
                                placeholder="{{'GLOBAL.select' | translate}}" formControlName="CallStatus">
                                <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                                    <div class="checkbox-container"><input type="checkbox" id="item-{{index}}"
                                            data-automate-id="item-{{index}}" [checked]="item$.selected"><span
                                            class="checkmark"></span><span class="text-truncate-1 break-all">
                                            {{item?.displayName}}</span>
                                    </div>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                    <div class="w-100 modal-footer bg-white justify-end position-sticky bottom-0 z-index-1021 mt-20">
                        <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalService.hide()">Cancel</u>
                        <div class="mr-10 btn-gray" (click)="onClearAllFilters()">Reset</div>
                        <button class="btn-coal" (click)="applyAdvancedFilter()">Apply Filter</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</ng-template>
<ng-template #noData>
    <div class="flex-center-col h-300">
        <img src="assets/images/layered-cards.svg" alt="No Data Found" width="160" height="140">
        <div class="fw-semi-bold text-xl text-mud">{{'PROFILE.no-lead-found' | translate}}</div>
    </div>
</ng-template>
<ng-template #dataLoader>
    <div class="flex-center h-300">
        <application-loader></application-loader>
    </div>
</ng-template>
<ng-template #BulkDeleteModal>
    <div class="bg-light-pearl h-100vh bg-triangle-pattern">
        <div class="flex-between bg-coal w-100 px-20 py-12 text-white">
            <h3>Bulk Delete</h3>
            <div class="icon ic-close  ic-sm cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="px-12">
            <div class="field-label mb-10">Selected Member(s) -
                {{gridApi?.getSelectedNodes()?.length}}
            </div>
            <div class="flex-column scrollbar max-h-100-176">
                <ng-container *ngFor="let team of selectedLeads">
                    <div class="flex-between p-12 fw-600 text-sm border-bottom text-secondary bg-white">
                        <span class="text-truncate-1 break-all mr-8">{{ team?.lead.name }}</span>
                        <div (click)="openConfirmDeleteModal(team?.lead?.name, team?.lead.id)"
                            class="bg-light-red icon-badge" id="clkBulkDelete" data-automate-id="clkBulkDelete">
                            <span class="icon ic-trash m-auto ic-xxxs"></span>
                        </div>
                    </div>
                </ng-container>
            </div>
            <div class="flex-center" *ngIf="!bulkDeleteIsLoading else ratLoader">
                <button class="btn-coal mt-20" (click)="bulkDelete()">{{ 'BUTTONS.delete' | translate }}</button>
            </div>
        </div>
    </div>
</ng-template>
<ng-template #dotLoader>
    (<div class="container px-4 d-inline">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling"></div>
        </ng-container>
    </div>)
</ng-template>