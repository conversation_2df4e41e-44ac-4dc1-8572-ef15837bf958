import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Store } from '@ngrx/store';
import { takeUntil } from 'rxjs';

import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, getSystemTimeOffset, getSystemTimeZoneId, getTimeZoneDate, isEmptyObject, onPickerOpened, patchTimeZoneDate, setTimeZoneDate } from 'src/app/core/utils/common.util';
import { AttendanceVisualComponent } from 'src/app/features/teams/manage-user/user-info/user-attendance/attendance-visual/attendance-visual.component';
import { ClockInOutComponent } from 'src/app/features/teams/manage-user/user-info/user-attendance/clock-in-out/clock-in-out.component';
import {
  ClockIn,
  ClockOut,
  FetchAttendanceListById,
  FetchAttendanceListNoAuth,
  FetchAttendanceSetting,
  UpdateFilterPayloadNoAuth,
} from 'src/app/reducers/attendance/attendance.actions';
import { getAttendanceListById, getAttendanceListNoAuth, getAttendanceSettings, getFiltersPayloadNoAuth } from 'src/app/reducers/attendance/attendance.reducer';
import { FetchGeoFencingList } from 'src/app/reducers/teams/teams.actions';
import { getGeoFencingList, getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';
import { DeviceInfoService } from 'src/app/services/shared/device-info.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { TrackingService } from 'src/app/services/shared/tracking.service';
import { BaseGridComponent } from 'src/app/shared/components/base-grid/base-grid.component';
import { WebcamComponent } from 'src/app/shared/components/web-cam/web-cam.component';
import { PunchInComponent } from './punch-in/punch-in.component';

@Component({
  selector: 'user-attendance',
  templateUrl: './user-attendance.component.html',
})
export class UserAttendanceComponent
  extends BaseGridComponent
  implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public greeting: string;
  @Input() loggedInUser: boolean;
  gridOptions: any;
  columnDropDown: { field: string; hide: boolean }[] = [];
  rowData: any = [];
  gridApi: any;
  gridColumnApi: any;
  appliedFilter: any;
  isDateFilter: string = 'presentMonth';
  newDate = new Date();
  selectedUserId: any;
  maxDate: Date;
  dateFilterOptions = [
    { type: 'today', translationKey: 'GLOBAL.today' },
    { type: 'yesterday', translationKey: 'REPORTS.yesterday' },
    { type: 'sevenDays', translationKey: 'REPORTS.last-7-days' },
    { type: 'presentMonth', translationKey: 'DASHBOARD.current-month' },
    { type: 'custom', translationKey: 'DASHBOARD.custom' }
  ];
  userData: any;
  isClockIn: boolean;
  hours: number = 0;
  minutes: number = 0;
  seconds: number = 0;
  isLocationDisabled: boolean;
  isMandatorySelfieClockin: boolean = false;
  isMandatorySelfieClockout: boolean = false;
  private timer: any;
  private startTime: Date;
  params: any;
  stream: MediaStream;
  isClockInMode: boolean;
  clockInImg: any;
  clockOutImg: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened
  filtersPayload: any;
  userBasicDetails: any;
  geoFencingData: any = [];
  isInGeoFence: boolean = false;
  isCheckingGeoFence: boolean = false;
  geoFenceCheckInterval: any;
  showGeoFenceBanner: boolean = true;
  geoFenceBannerTimeout: any;
  isGeoFenceEnabled: boolean = false;
  isAccuracyLow: boolean = false;
  previousAccuracy: number = 0;
  isAccuracyChecked: boolean = false;
  showAccuracyBanner: boolean = false;
  accuracyBannerTimeout: any;

  constructor(
    private notificationService: NotificationsService,
    public modalService: BsModalService,
    public modalRef: BsModalRef,
    private store: Store<AppState>,
    private activatedRoute: ActivatedRoute,
    private gridOptionsService: GridOptionsService,
    public trackingService: TrackingService,
    private deviceInfoService: DeviceInfoService
  ) {
    super();

    this.activatedRoute.params.subscribe((params: any) => {
      if (params && params.id) {
        this.selectedUserId = params.id;
      }
    });

    const userId = JSON.parse(localStorage.getItem('userDetails'))?.sub;
    if (userId) {
      this.store.dispatch(new FetchGeoFencingList(userId));
      this.store
        .select(getGeoFencingList)
        .pipe(takeUntil(this.stopper))
        .subscribe((data: any) => {
          this.geoFencingData = data;
          this.checkGeoFenceLocation();
          this.triggerAccuracyCheckIfReady();
        });
    }

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userBasicDetails = data;
        if (!isEmptyObject(data)) {
          const wasGeoFenceActive = this.userBasicDetails?.isGeoFenceActive;
          this.userBasicDetails = data;
          if (wasGeoFenceActive && !this.userBasicDetails?.isGeoFenceActive) {
            this.resetAccuracyState();
          }
          this.triggerAccuracyCheckIfReady();
          this.currentDate = changeCalendar(this.userBasicDetails?.timeZoneInfo?.baseUTcOffset);
          this.filtersPayload = {
            path: 'attendance/no-auth',
            fromDate: setTimeZoneDate(new Date(new Date(this.currentDate).getFullYear(), new Date(this.currentDate).getMonth(), 1), this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
            toDate: setTimeZoneDate(new Date(this.currentDate), this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
            userIds: [this.selectedUserId],
            timeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
            baseUTcOffset: this.userBasicDetails?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
          }
          const currentHour = new Date(this.currentDate).getHours();
          if (currentHour >= 1 && currentHour < 12) {
            this.greeting = 'Good Morning';
          } else if (currentHour >= 12 && currentHour < 17) {
            this.greeting = 'Good Afternoon';
          } else if (currentHour >= 17 && currentHour < 24) {
            this.greeting = 'Good Evening';
          }

          this.store
            .select(getFiltersPayloadNoAuth)
            .pipe(takeUntil(this.stopper))
            .subscribe((data: any) => {
              this.appliedFilter = {
                ...this.appliedFilter,
                pageNumber: null,
                pageSize: null,
                userIds: [this.selectedUserId],
                date: [patchTimeZoneDate(this.filtersPayload?.fromDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset), patchTimeZoneDate(this.filtersPayload?.toDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset)],
                timeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
                baseUTcOffset: this.userBasicDetails?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
              };
            });
          this.filterFunction();
          this.getAttendanceList();
        }
      });
  }

  ngOnInit() {
    this.initializeGridSettings();
    // this.maxDate = new Date();
    // this.maxDate.setDate(this.maxDate.getDate());

    this.store
      .select(getAttendanceListNoAuth)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data.items;
        if (data.items) {
          const rowData = [...(data.items?.[0]?.logByDayDtos || [])];
          rowData.reverse();
          this.rowData = rowData;
        }
      });

    const storedClockInTime = localStorage.getItem('clockInTime');
    if (storedClockInTime) {
      this.startTime = new Date(storedClockInTime);
      this.startTimer();
    }

    this.store.dispatch(new FetchAttendanceSetting());

    this.store
      .select(getAttendanceSettings)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isMandatorySelfieClockin = data?.isSelfieMandatoryForClockIn,
          this.isMandatorySelfieClockout = data?.isSelfieMandatoryForClockOut;
        const wasGeoFenceEnabled = this.isGeoFenceEnabled;
        this.isGeoFenceEnabled = data?.isGeoFenceEnabled;
        if (wasGeoFenceEnabled && !this.isGeoFenceEnabled) {
          this.resetAccuracyState();
        }
        this.triggerAccuracyCheckIfReady();
      });

    this.geoFenceCheckInterval = setInterval(() => {
      if (this.geoFencingData) {
        this.checkGeoFenceLocation();
      }
    }, 120000);

    setTimeout(() => {
      if (this.geoFencingData && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive && !this.isAccuracyChecked) {
        this.checkGeoFenceLocation();
        this.isAccuracyChecked = true;
      }
    }, 2000);
  }

  startTimer() {
    this.timer = setInterval(() => {
      this.updateTime();
    }, 1000);
  }

  updateTime() {
    const currentTime = new Date();
    const timeDifference = Math.floor((currentTime.getTime() - this.startTime.getTime()) / 1000);

    this.hours = Math.floor(timeDifference / 3600);
    this.minutes = Math.floor((timeDifference % 3600) / 60);
    this.seconds = timeDifference % 60;
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.paginationPageSize = this.rowData?.length;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Date',
        field: 'Date',
        maxWidth: 80,
        resizable: false,
        valueGetter: (params: any) => [getTimeZoneDate(params.data?.day, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset, 'dayMonth')],
        cellRenderer: (params: any) => {
          return `<p>${params.value}</p>`;
        },
      },
      {
        headerName: 'Clock In',
        field: 'Clock In',
        maxWidth: 120,
        resizable: false,
        valueGetter: (params: any) => [{
          data: params.data?.logDtos,
          clockInOut: 'clockIn'
        }],
        cellRenderer: ClockInOutComponent,
      },
      {
        headerName: 'Clock Out',
        field: 'Clock Out',
        maxWidth: 120,
        resizable: false,
        valueGetter: (params: any) => [{
          data: params.data?.logDtos,
          clockInOut: 'clockOut'
        }],
        cellRenderer: ClockInOutComponent,
      },
      {
        headerName: 'Working Hours',
        field: 'Working Hours',
        maxWidth: 130,
        resizable: false,
        valueGetter: (params: any) => [params.data.workingHours == '00:00:00' ? '--' : params.data.workingHours.slice(0, 5) + ' Hrs'],
        cellRenderer: (params: any) => {
          return `<p>${params.value} </p>`;
        },
      },
      {
        headerName: 'Attendance Visuals',
        filter: false,
        resizable: false,
        valueGetter: (params: any) => [params.data.status],
        cellRenderer: AttendanceVisualComponent,
      },
      // {
      //   headerName: 'Punch-in locations',
      //   field: 'Punch-in locationss',
      //   maxWidth: 260,
      //   resizable: false,
      //   valueGetter: (params: any) => {
      //     return params.data?.logDtos?.filter((log: any) => log.isPunchInLocation) || [];
      //   },
      //   cellRenderer: PunchInComponent
      // }

    ];
    this.gridOptions.columnDefs.forEach((item: any, index: number) => {
      if (index != 0 && index != this.gridOptions.columnDefs.length - 1) {
        this.columnDropDown.push({ field: item.field, hide: item.hide });
      }
    });
    this.gridOptions.rowData = this.rowData;
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  filterByDate(type?: string) {
    let newDate = new Date(this.currentDate);
    let newDate1 = new Date(this.currentDate);
    switch (type) {
      case 'today':
        this.isDateFilter = 'today';
        this.appliedFilter.date[0] = new Date(this.currentDate);
        this.appliedFilter.date[1] = new Date(this.currentDate);
        break;
      case 'yesterday':
        this.isDateFilter = 'yesterday';
        this.appliedFilter.date[0] = newDate.setDate(newDate.getDate() - 1);
        this.appliedFilter.date[1] = newDate1.setDate(newDate1.getDate() - 1);
        break;
      case 'sevenDays':
        this.isDateFilter = 'sevenDays';
        this.appliedFilter.date[0] = newDate.setDate(
          newDate.getDate() - 6
        );
        this.appliedFilter.date[1] = new Date(this.currentDate);
        break;
      case 'presentMonth':
        this.isDateFilter = 'presentMonth';
        let currentDate = new Date(this.currentDate);
        let firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        this.appliedFilter.date[0] = firstDayOfMonth;
        this.appliedFilter.date[1] = new Date(this.currentDate);
        break;
      case 'custom':
        this.isDateFilter = 'custom';
        this.appliedFilter.date[0] = null;
        this.appliedFilter.date[1] = null;
        break;
    }
    if (type !== 'custom') {
      this.filterFunction();
    }
    let track: any = {
      today: 'Today',
      yesterday: 'Yesterday',
      sevenDays: 'Last7Days',
      presentMonth: 'CurrentMonth',
      custom: 'Custom'
    }
    this.trackingService.trackFeature(`Web.UserDetails.Attendance.Filter.${track[type]}.Click`)
  }

  filterFunction() {
    this.filtersPayload = {
      ...this.filtersPayload,
      UserIds: [this.selectedUserId],
      fromDate: setTimeZoneDate(this.appliedFilter?.date?.[0], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      toDate: setTimeZoneDate(this.appliedFilter.date?.[1], this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      timeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId || getSystemTimeZoneId(),
      baseUTcOffset: this.userBasicDetails?.timeZoneInfo?.baseUTcOffset || getSystemTimeOffset(),
    };
    this.store.dispatch(new UpdateFilterPayloadNoAuth(this.filtersPayload));
    this.store.dispatch(new FetchAttendanceListNoAuth());
  }

  resetDate() {
    let currentDate = new Date(this.currentDate);
    let firstDayOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    let toDate: any = new Date(this.currentDate);
    let fromDate: any = firstDayOfMonth;
    this.filtersPayload = {
      ...this.filtersPayload,
      toDate: setTimeZoneDate(toDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
      fromDate: setTimeZoneDate(fromDate, this.userBasicDetails?.timeZoneInfo?.baseUTcOffset),
    };
    this.isDateFilter = 'presentMonth';
    this.store.dispatch(new UpdateFilterPayloadNoAuth(this.filtersPayload));
    this.store.dispatch(new FetchAttendanceListNoAuth());
  }

  clockIn() {
    if (this.geoFencingData && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive && !this.isInGeoFence) {
      this.checkGeoFenceLocation().then(isWithinFence => {
        if (!isWithinFence) {
          return;
        } else if (this.isAccuracyLow) {
          return;
        } else {
          this.proceedWithClockIn();
        }
      });
    } else if (this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive && this.isAccuracyLow) {
      return;
    } else {
      this.proceedWithClockIn();
    }
  }

  proceedWithClockIn() {
    this.isClockInMode = true;
    navigator?.mediaDevices
      ?.getUserMedia({
        video: true,
      })
      .then((res: any) => {
        this.stream = res;
        this.modalRef = this.modalService?.show(WebcamComponent, {
          class: 'modal-640 modal-dialog-centered h-100 tb-modal-unset',
          initialState: {
            isClockInMode: this.isClockInMode,
            isMandatorySelfieClockin: this.isMandatorySelfieClockin,
          }
        });
        this.modalRef?.content?.photoCaptured?.subscribe((res: any) => {
          this.clockInImg = res?.imageAsDataUrl;
          if ((this.isMandatorySelfieClockin && res?.imageAsDataUrl) || (!this.isMandatorySelfieClockin && res?.imageAsDataUrl)) {
            this.processClockIn(res?.imageAsDataUrl);
          }
          this.stopStream();
          if (!this.isMandatorySelfieClockin && !res?.imageAsDataUrl)
            this.clockInImg = null;
        });

        this.modalRef?.onHidden?.subscribe(() => {
          if (!this.isMandatorySelfieClockin && !this.clockInImg) {
            this.processClockIn(null);
          }
          this.stopStream();
        });
      })
      .catch((err: any) => {
        this.handleCameraError(err);
      });
  }

  async processClockIn(imageDataUrl: string | null) {
    if (navigator.geolocation) {
      const geolocationOptions = this.deviceInfoService.getGeolocationOptions();
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const accuracy = position.coords.accuracy;
          if (accuracy > 200 && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive) {
            return;
          }
          let clockPayload = {
            latitude: String(position.coords.latitude),
            longitude: String(position.coords.longitude),
            clockInImageUrl: imageDataUrl,
            timeZone: this.userBasicDetails?.timeZoneInfo,
          };
          if (this.isMandatorySelfieClockin && imageDataUrl) {
            clockPayload.clockInImageUrl = imageDataUrl;
          }
          this.store.dispatch(new ClockIn(clockPayload));
          this.startTime = new Date();
          localStorage.setItem('clockInTime', this.startTime.toISOString());
          this.startTimer();
        },
        () => {
          this.openDisabledLocMsg();
        },
        geolocationOptions
      );
    } else {
      this.openDisabledLocMsg();
    }
  }

  handleCameraError(err: any) {
    if (err?.message === 'Permission denied') {
      this.notificationService.warn("Give camera permission");
    } else {
      this.notificationService.warn("Camera cannot be accessed, please login via a device with a camera to clock in");
    }
  }

  stopStream() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
    }
  }

  clockOut() {
    if (this.geoFencingData && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive && !this.isInGeoFence) {
      this.checkGeoFenceLocation().then(isWithinFence => {
        if (!isWithinFence) {
          return;
        } else if (this.isAccuracyLow) {
          return;
        } else {
          this.proceedWithClockOut();
        }
      });
    } else if (this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive && this.isAccuracyLow) {
      return;
    } else {
      this.proceedWithClockOut();
    }
  }

  proceedWithClockOut() {
    this.isClockInMode = false;
    navigator?.mediaDevices
      ?.getUserMedia({
        video: true,
      })
      .then((res: any) => {
        this.stream = res;
        this.modalRef = this.modalService?.show(WebcamComponent, {
          class: 'modal-640 modal-dialog-centered h-100 tb-modal-unset',
          initialState: {
            isClockInMode: this.isClockInMode,
            isMandatorySelfieClockout: this.isMandatorySelfieClockout
          }
        });
        this.modalRef?.content?.photoCaptured?.subscribe((res: any) => {
          this.clockOutImg = res?.imageAsDataUrl;
          if ((this.isMandatorySelfieClockout && res?.imageAsDataUrl) || (!this.isMandatorySelfieClockout && res?.imageAsDataUrl)) {
            this.processClockOut(res?.imageAsDataUrl);
          }
          this.stopStream();
          if (!this.isMandatorySelfieClockout && !res?.imageAsDataUrl)
            this.clockOutImg = null;
        });

        this.modalRef?.onHidden?.subscribe(() => {
          if (!this.isMandatorySelfieClockout && !this.clockOutImg) {
            this.processClockOut(null);
          }
          this.stopStream();
        });
      })
      .catch((err: any) => {
        this.handleCameraError(err);
      });
  }

  async processClockOut(imageDataUrl: string | null) {
    if (navigator.geolocation) {
      const geolocationOptions = this.deviceInfoService.getGeolocationOptions();
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const accuracy = position.coords.accuracy;
          if (accuracy > 200 && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive) {
            return;
          }
          let clockPayload = {
            latitude: String(position.coords.latitude),
            longitude: String(position.coords.longitude),
            clockOutImageUrl: imageDataUrl,
            timeZone: this.userBasicDetails?.timeZoneInfo,
          };
          if (this.isMandatorySelfieClockout && imageDataUrl) {
            clockPayload.clockOutImageUrl = imageDataUrl;
          }
          this.store.dispatch(new ClockOut(clockPayload));
        },
        () => {
          this.openDisabledLocMsg();
        },
        geolocationOptions
      );
    } else {
      this.openDisabledLocMsg();
    }
  }

  getAttendanceList() {
    this.store.dispatch(new FetchAttendanceListById(this.selectedUserId, this.userBasicDetails?.timeZoneInfo));
    this.store
      .select(getAttendanceListById)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        let attendanceList = data;
        this.isClockIn = !attendanceList?.length ? false : !attendanceList?.[0]?.isClosed;
        const lastClockInDateTime = new Date(attendanceList?.[0]?.clockInTime);
        const presentDateTime = new Date();
        const timeDifference = presentDateTime.getTime() - lastClockInDateTime.getTime();
        const secondsDifference = Math.floor(timeDifference / 1000);
        this.hours = Math.floor(secondsDifference / 3600);
        this.minutes = Math.floor((secondsDifference % 3600) / 60);
        this.seconds = secondsDifference % 60;
      });
    this.store.dispatch(new FetchAttendanceListNoAuth());
  }

  openDisabledLocMsg() {
    this.isLocationDisabled = true;
    setTimeout(() => {
      this.isLocationDisabled = false;
    }, 5000);
  }

  triggerAccuracyCheckIfReady() {
    if (this.geoFencingData && this.userBasicDetails && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive && !this.isAccuracyChecked) {
      setTimeout(() => {
        this.checkGeoFenceLocation();
        this.isAccuracyChecked = true;
      }, 1000);
    }
  }

  resetAccuracyState() {
    this.isAccuracyLow = false;
    this.showAccuracyBanner = false;
    this.previousAccuracy = 0;
    if (this.accuracyBannerTimeout) {
      clearTimeout(this.accuracyBannerTimeout);
      this.accuracyBannerTimeout = null;
    }
  }

  checkGeoFenceLocation(): Promise<boolean> {
    if (!this.geoFencingData) {
      return Promise.resolve(true);
    }

    if (this.isCheckingGeoFence) {
      return Promise.resolve(this.isInGeoFence);
    }

    this.isCheckingGeoFence = true;

    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        this.isCheckingGeoFence = false;
        if (this.isGeoFenceEnabled) {
          this.openDisabledLocMsg();
        }
        this.showGeoFenceBanner = false;
        resolve(false);
        return;
      }

      const geolocationOptions = this.deviceInfoService.getGeolocationOptions();

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const userLat = position.coords.latitude;
          const userLng = position.coords.longitude;
          const accuracy = position.coords.accuracy;
          this.previousAccuracy = accuracy;
          const wasAccuracyPoor = this.isAccuracyLow;
          this.isAccuracyLow = accuracy > 200 && this.isGeoFenceEnabled && this.userBasicDetails?.isGeoFenceActive;
          if (this.isAccuracyLow) {
            this.showAccuracyBanner = true;
            if (this.accuracyBannerTimeout) {
              clearTimeout(this.accuracyBannerTimeout);
            }
            this.accuracyBannerTimeout = setTimeout(() => {
              this.showAccuracyBanner = false;
            }, 5000);
          } else if (wasAccuracyPoor && !this.isAccuracyLow) {
            this.showAccuracyBanner = false;
            if (this.accuracyBannerTimeout) {
              clearTimeout(this.accuracyBannerTimeout);
              this.accuracyBannerTimeout = null;
            }
          }

          let isWithinFence = true;
          let hasValidLocations = false;

          if (this.geoFencingData.properties && this.geoFencingData.properties.length > 0) {
            for (const property of this.geoFencingData.properties) {
              if (property.latitude && property.longitude) {
                hasValidLocations = true;
                const distance = this.calculateDistance(
                  userLat,
                  userLng,
                  property.latitude,
                  property.longitude
                );
                const radiusInMeters = this.geoFencingData.radiusUnit === 1
                  ? this.geoFencingData.geoFenceRadius
                  : this.geoFencingData.geoFenceRadius * 1000;
                if (distance > radiusInMeters) {
                  isWithinFence = false;
                  break;
                }
              }
            }
          }
          if (isWithinFence && this.geoFencingData.projects && this.geoFencingData.projects.length > 0) {
            for (const project of this.geoFencingData.projects) {
              if (project.latitude && project.longitude) {
                hasValidLocations = true;
                const distance = this.calculateDistance(
                  userLat,
                  userLng,
                  project.latitude,
                  project.longitude
                );
                const radiusInMeters = this.geoFencingData.radiusUnit === 1
                  ? this.geoFencingData.geoFenceRadius
                  : this.geoFencingData.geoFenceRadius * 1000;
                if (distance > radiusInMeters) {
                  isWithinFence = false;
                  break;
                }
              }
            }
          }
          if (!hasValidLocations) {
            isWithinFence = true;
          }

          if (this.isInGeoFence !== isWithinFence) {
            if (!isWithinFence) {
              this.showGeoFenceBanner = true;
              if (this.geoFenceBannerTimeout) {
                clearTimeout(this.geoFenceBannerTimeout);
              }
              this.geoFenceBannerTimeout = setTimeout(() => {
                this.showGeoFenceBanner = false;
              }, 5000);
            }
          }
          if (!this.isInGeoFence && !isWithinFence) {
            this.showGeoFenceBanner = true;
            if (this.geoFenceBannerTimeout) {
              clearTimeout(this.geoFenceBannerTimeout);
            }
            this.geoFenceBannerTimeout = setTimeout(() => {
              this.showGeoFenceBanner = false;
            }, 5000);
          }

          this.isInGeoFence = isWithinFence;
          this.isCheckingGeoFence = false;
          resolve(isWithinFence);
        },
        (error) => {
          console.error('Error getting location:', error);
          this.isCheckingGeoFence = false;
          if (this.isGeoFenceEnabled) {
            this.openDisabledLocMsg();
          }

          this.showGeoFenceBanner = false;
          this.showAccuracyBanner = false;
          if (this.accuracyBannerTimeout) {
            clearTimeout(this.accuracyBannerTimeout);
            this.accuracyBannerTimeout = null;
          }
          resolve(false);
        },
        geolocationOptions
      );
    });
  }

  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371000;
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    return distance;
  }

  deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
    clearInterval(this.timer);
    if (this.geoFenceCheckInterval) {
      clearInterval(this.geoFenceCheckInterval);
    }
    if (this.geoFenceBannerTimeout) {
      clearTimeout(this.geoFenceBannerTimeout);
    }
    if (this.accuracyBannerTimeout) {
      clearTimeout(this.accuracyBannerTimeout);
    }
  }
}
