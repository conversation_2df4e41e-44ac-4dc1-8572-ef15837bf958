import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { debounceTime, filter, Subject, switchMap, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { AddCampaignName, ExistCampaign, UpdateCampaignName } from 'src/app/reducers/manage-marketing/marketing.action';
import { getCampaignExist } from 'src/app/reducers/manage-marketing/marketing.reducer';

@Component({
  selector: 'add-campaign',
  templateUrl: './add-campaign.component.html',
})
export class AddCampaignComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  campaignForm: FormGroup;
  selectedCampaign: any;
  private campaignNameSubject = new Subject<string>();
  doesExistCampaign: boolean;

  constructor(
    public modalRef: BsModalRef,
    private fb: FormBuilder,
    private store: Store<AppState>,
  ) { }

  ngOnInit(): void {
    this.campaignForm = this.fb.group({
      campaignName: ['', Validators.required],
    });

    if (this.selectedCampaign) {
      this.patchCampaignForm(this.selectedCampaign);
    }

    this.campaignNameSubject
      .pipe(
        debounceTime(300),
        takeUntil(this.stopper),
        filter(campaignName => !!campaignName),
        switchMap(campaignName => {
          this.store.dispatch(new ExistCampaign(campaignName));
          return this.store.select(getCampaignExist);
        }),
        takeUntil(this.stopper)
      )
      .subscribe((doesExistCampaign: boolean) => {
        this.doesExistCampaign = doesExistCampaign;
        this.campaignForm.get('campaignName').setErrors(
          doesExistCampaign ? { alreadyExist: true } : null
        );
      });
  }

  onCancel(): void {
    this.modalRef.hide();
  }

  onSubmit(): void {
    if (!this.campaignForm.valid) {
      validateAllFormFields(this.campaignForm);
      return;
    }
    if (this.campaignForm.valid) {
      let payload;
      if (this.selectedCampaign) {
        payload = {
          id: this.selectedCampaign.id,
          name: this.campaignForm.value.campaignName,
        };
        this.store.dispatch(new UpdateCampaignName(payload));
      } else {
        payload = {
          name: this.campaignForm.value.campaignName,
        };
        this.store.dispatch(new AddCampaignName(payload));
      }
    }
    this.modalRef.hide();
  }

  doesCampaignExist(name: string) {
    this.campaignNameSubject.next(name);
  }
  patchCampaignForm(data: any): void {
    this.campaignForm.patchValue({
      campaignName: data?.name || '',
    });
  }
}
