import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { buildHttpParams } from 'src/app/core/utils/common.util';
import { environment as env } from 'src/environments/environment';
import { BaseService } from '../shared/base.service';
@Injectable({
  providedIn: 'root'
})
export class AutodialerService extends BaseService<any> {
  public page: number;
  public count: number;
  serviceBaseUrl: string = '';
  constructor(private http: HttpClient) {
    super(http);
    this.serviceBaseUrl = `${env.baseURL}${env.apiURL}${this.getResourceUrl()}`;
  }

  getResourceUrl(): string {
    return 'autodialer';
  }

  getStatusCount(payload: any) {
    const params = buildHttpParams(payload);
    return this.http.get(`${this.serviceBaseUrl}/statusescount?${params.toString()}`);
  }

  updateDialerConfiguration(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/config`, payload);
  }

  geteDialerConfiguration() {
    return this.http.get(`${this.serviceBaseUrl}/config`);
  }

  updateUserStatus(payload: any) {
    return this.http.put(`${this.serviceBaseUrl}/userstatus`, payload);
  }

  getUserStatus() {
    return this.http.get(`${this.serviceBaseUrl}/userstatus`);
  }

  addlead(payload: any) {
    return this.http.post(`${this.serviceBaseUrl}/addleads`, payload);
  }

  bulkDeleteLeads(payload: any) {
    const options = {
      body: payload,
    };
    return this.http.delete(`${this.serviceBaseUrl}/clearbucket`, options);
  }
}
