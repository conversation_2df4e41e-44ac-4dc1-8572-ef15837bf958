import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRe<PERSON>,
  <PERSON>ttpH<PERSON><PERSON>,
  HttpEvent,
  HttpInterceptor,
  HttpResponse
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ClickjackingPreventionService } from '../services/clickjacking-prevention.service';
import { environment } from 'src/environments/environment';

@Injectable()
export class SecurityHeadersInterceptor implements HttpInterceptor {

  constructor(private clickjackingService: ClickjackingPreventionService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    // Get security headers from the service
    const securityHeaders = this.clickjackingService.getSecurityHeaders();

    // Add security headers to outgoing requests
    const secureRequest = request.clone({
      setHeaders: securityHeaders
    });

    return next.handle(secureRequest).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          // Log security headers for debugging (only in development)
          if (!environment.production) {
            console.log('Security headers applied:', securityHeaders);
          }
        }
      })
    );
  }
}

// Note: This interceptor primarily adds headers to requests.
// For a complete solution, server-side configuration is also needed
// to ensure these headers are properly set in HTTP responses.
