import { Component, EventEmitter, HostListener, On<PERSON><PERSON>roy, OnInit, TemplateRef } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { interval, Subscription } from 'rxjs';
import { skipWhile, takeUntil } from 'rxjs/operators';
import { AUTO_DIALER_FILTERS_KEY_LABEL, EMPTY_GUID, IVR_CALL_STATUS, SHOW_ENTRIES } from 'src/app/app.constants';
import { IVRCallStatus, LeadSource } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import { assignToSort, getAssignedToDetails, getPages, getTenantName, hexToRgba, validate<PERSON>ll<PERSON>ormFields } from 'src/app/core/utils/common.util';
import { BulkDeleteLeads, FetchStatusCount, GetUserStatus, UpdateFilterPayload, UpdateUserStatus } from 'src/app/reducers/auto-dialer/auto-dialer.actions';
import { getAutoDialer, getAutoDialerIsLoading, getFiltersPayload, getStatusCount, getStatusCountIsLoading, getTotalCount, getUserStatus } from 'src/app/reducers/auto-dialer/auto-dialer.reducers';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { getOnlyReporteesWithInactive, getOnlyReporteesWithInactiveIsLoading, getUsersListForReassignment, getUsersListForReassignmentIsLoading } from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { SignalRService } from 'src/app/services/shared/web-socket.service';
import { LeadPreviewComponent } from 'src/app/shared/components/lead-preview/lead-preview.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'auto-dialer',
  templateUrl: './auto-dialer.component.html',
})
export class AutoDialerComponent implements OnInit, OnDestroy {
  userDetails = JSON.parse(localStorage.getItem('userDetails') || '{}')?.sub
  positionY = 450;
  isDragging = false;
  offsetY = 0;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  private timerSub!: Subscription;
  filterTabs: any[]
  filterForm: FormGroup;
  selectedFilter: string = null;
  isCountsLoading: boolean = false;
  isAvailable: boolean = false;
  SearchByNameAndNumber: string = '';
  isDataLoading: boolean = false;
  showEntriesSize: Array<number> = SHOW_ENTRIES;
  autoDialerKeylabel = AUTO_DIALER_FILTERS_KEY_LABEL
  rowData: any[] = [];
  totalCount: number = 0;
  currentPage: number = 1;
  pageSize: number = 10;
  currOffset: number = 0;
  filtersPayload: any = {
    pageNumber: 1,
    pageSize: this.pageSize,
    FilterStatusId: this.selectedFilter,
  };
  formFields: any = {
    CallStatus: [null],
    UserIds: [null]
  }
  tenant: string = getTenantName();
  selectedPageSize: number = 10;
  isUsersListForReassignmentLoading: boolean = true;
  gridOptions: GridOptions;
  gridApi: GridApi;
  currentLead: any;
  isLeadPreviewOpen: boolean = false;
  getPages = getPages;
  hexToRgba: Function = hexToRgba;
  globalSettingsData: any;
  allUsers: any;
  getAssignedToDetails = getAssignedToDetails
  emptyGuid = EMPTY_GUID;
  duration = '00:00:00';
  selectedLeads: any[];
  showLeftNav: boolean;
  callStatusList = IVR_CALL_STATUS
  canViewAllUsers: boolean;
  canViewReportees: boolean;
  reportees: any;
  onlyReportees: any;
  isReporteesWithInactiveLoading: boolean;
  allUserListIsLoading: boolean;
  allUsersList: Object[];
  lastOpenedLeadId?: any;
  get showFilters(): boolean {
    const filterKeys = [
      'CallStatus', 'UserIds'
    ];
    return filterKeys.some(key => {
      const value = this.filtersPayload?.[key];
      return Array.isArray(value) ? value.length > 0 : typeof value === 'string' ? value.trim().length > 0 : Number(value) > 0;
    });
  }
  constructor(
    public modalRef: BsModalRef,
    private gridOptionsService: GridOptionsService,
    private _store: Store<AppState>,
    private modalService: BsModalService,
    private signalService: SignalRService,
    private headerTitle: HeaderTitleService,
    private metaTitle: Title,
    private shareDataService: ShareDataService,
    private fb: FormBuilder,
  ) { }

  ngOnInit(): void {
    this.headerTitle.setLangTitle('Auto Dialer');
    this.metaTitle.setTitle('CRM | Dialer');
    this.signalService.startDialerConnection()
    this.signalService.getDialerMessageListener().subscribe((message: any) => {
      if (!message) {
        this.currentLead = null;
        return;
      }
      if (message?.name && (message?.secondaryUserId === this.userDetails || message?.assignTo === this.userDetails)) {
        this.currentLead = message;
        if (this.currentLead?.callStartTime) {
          this.startTimer();
        }
        const leadToPreview = this.rowData?.find(item => item?.lead?.id === message.id);
        if (leadToPreview && this.lastOpenedLeadId !== message.id) {
          this.lastOpenedLeadId = message.id;
          this.openLeadPreview(leadToPreview, 'Overview');
        }
      } else if (message?.callStatus === 0 && message?.id) {
        const leadToPreview = this.rowData?.find(item => item?.lead?.id === message.id);
        if (leadToPreview && this.lastOpenedLeadId !== message.id) {
          this.lastOpenedLeadId = message.id;
          this.openLeadPreview(leadToPreview, 'Overview');
        }
      }
    });

    this.shareDataService.showLeftNav$.subscribe(show => this.showLeftNav = show);
    this.filterForm = this.fb.group(this.formFields);
    this.selectedPageSize = 10;
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canViewAllUsers = permissionsSet.has(
          'Permissions.Attendance.ViewAllUsers'
        );
        this.canViewReportees = permissionsSet.has(
          'Permissions.Attendance.ViewReportees'
        );
      })

    this._store.dispatch(new FetchStatusCount());
    this._store.dispatch(new GetUserStatus());
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));

    this._store
      .select(getStatusCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const tabs = data?.filter((item: any) => item?.count) || [];
        const totalCount = tabs.reduce((sum: number, item: any) => sum + item.count, 0);

        this.filterTabs = [
          { id: null, displayName: 'All', count: totalCount },
          ...tabs.map((item: any) => ({
            id: item.id,
            displayName: item.displayName,
            count: item.count
          }))
        ];
      });


    this._store
      .select(getStatusCountIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isCountsLoading = data;
      });

    this._store
      .select(getUsersListForReassignment)
      .pipe(
        skipWhile((data: any) => !data || data?.length === 0),
        takeUntil(this.stopper)
      )
      .subscribe((data: any) => {
        const sortedUsers = data.map((user: any) => ({
          ...user,
          fullName: `${user.firstName} ${user.lastName}`,
        }));
        this.allUsers = sortedUsers.sort(
          (a: any, b: any) =>
            (b.isActive === true ? 1 : 0) - (a.isActive === true ? 1 : 0)
        );
        this.allUsersList = assignToSort(this.allUsers, '');
        if (this.gridApi) {
          this.gridApi.refreshCells({ force: true });
        }
      });

    this._store
      .select(getOnlyReporteesWithInactive)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.reportees = data;
        this.onlyReportees = data?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.onlyReportees = assignToSort(this.onlyReportees, '');
      });

    this._store
      .select(getOnlyReporteesWithInactiveIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe(
        (data: boolean) => (this.isReporteesWithInactiveLoading = data)
      );

    this._store
      .select(getUsersListForReassignmentIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => (this.allUserListIsLoading = data));

    this._store
      .select(getUserStatus)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (data?.hasOwnProperty('succeeded') && data.hasOwnProperty('data')) {
          this.isAvailable = !!data.data;
        }
      });

    this._store
      .select(getAutoDialer)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.rowData = data || [];
      });

    this._store
      .select(getTotalCount)
      .pipe(takeUntil(this.stopper))
      .subscribe((count: number) => {
        this.totalCount = count;
      });

    this._store
      .select(getFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((payload: any) => {
        this.filtersPayload = payload;
        this.currentPage = payload?.pageNumber || 1;
        this.pageSize = payload?.pageSize || 10;
        this.SearchByNameAndNumber = payload?.SearchByNameAndNumber || '';
        this.selectedFilter = payload?.FilterStatusId;
      });

    this._store
      .select(getAutoDialerIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isDataLoading = data;
      });

    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        if (Object.keys(data || {}).length) {
          this.globalSettingsData = data;
        }
      });

    this.initializeGridSettings();
  }

  openAdvFiltersModal(advFilters: TemplateRef<any>) {
    let initialState: any = {
      class: 'modal-600 modal-dialog tb-modal-unset',
    };
    this.modalService.show(advFilters, initialState);
  }

  onFilterChange(filterKey: string): void {
    this.selectedFilter = filterKey;
    const updatedPayload = {
      ...this.filtersPayload,
      FilterStatusId: filterKey,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateFilterPayload(updatedPayload));
    this.currOffset = 0;
  }

  onAvailabilityToggle(): void {
    let payload: any = {
      shouldToggleUser: true,
    }
    this._store.dispatch(new UpdateUserStatus(payload));
    setTimeout(() => {
      this.signalService.sendDialerMessageToGroup(this.tenant, this.userDetails);
    }, 5000);
  }

  startTimer() {
    this.timerSub = interval(1000).subscribe(() => {
      const start = new Date(this.currentLead?.callStartTime).getTime();
      const now = Date.now();
      const diffInSeconds = Math.floor((now - start) / 1000);

      const hours = Math.floor(diffInSeconds / 3600);
      const minutes = Math.floor((diffInSeconds % 3600) / 60);
      const seconds = diffInSeconds % 60;

      this.duration = [
        hours.toString().padStart(2, '0'),
        minutes.toString().padStart(2, '0'),
        seconds.toString().padStart(2, '0')
      ].join(':');
    });
  }

  onSearch($event: any): void {
    if ($event.key === 'Enter') {
      if (this.SearchByNameAndNumber === '' || this.SearchByNameAndNumber === null) {
        return;
      }
      const updatedPayload = {
        ...this.filtersPayload,
        SearchByNameAndNumber: this.SearchByNameAndNumber,
        pageNumber: 1,
      };
      this._store.dispatch(new UpdateFilterPayload(updatedPayload));
      this.currOffset = 0;
    }
  }

  isEmptyInput(): void {
    if (this.SearchByNameAndNumber === '' || this.SearchByNameAndNumber === null) {
      const updatedPayload = {
        ...this.filtersPayload,
        SearchByNameAndNumber: '',
        pageNumber: 1,
      };
      this._store.dispatch(new UpdateFilterPayload(updatedPayload));
      this.currOffset = 0;
    }
  }

  getCallStatus(value: any) {
    return IVRCallStatus[value as keyof typeof IVRCallStatus];
  }

  getUserName(id: any) {
    let assignedTo = '';
    this.allUsers?.forEach((assign: any) => {
      if (assign.id === id) assignedTo = assign.fullName;
    });
    return assignedTo;
  }

  refreshData(): void {
    this._store.dispatch(new FetchStatusCount());
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
  }

  initializeGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 50;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Lead Name',
        field: 'name',
        valueGetter: (params: any) => [params.data?.lead.name],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1 break-all">${params.value}</p>`;
        },
      },
      {
        headerName: 'Assigned To',
        field: 'assignedTo',
        minWidth: 180,

        valueGetter: (params: any) => [
          getAssignedToDetails(params.data?.lead?.assignTo, this.allUsers, true) ||
          '--',
          getAssignedToDetails(
            params.data.lead.secondaryUserId,
            this.allUsers,
            true
          ) || '--',
        ],
        colId: 'AssignTo',
        cellRenderer: (params: any) => {
          if (this.globalSettingsData?.isDualOwnershipEnabled) {
            return `<div class="d-flex">
                     <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">P</div>
                     <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : '--'
              }</p>
                   </div>
                   <div class="d-flex mt-6">
                     <div class="bg-slate-250 text-white dot dot-sm text-sm fw-semi-bold mr-10">S</div>
                     <p class="text-truncate-1 break-all">${params.value[1] ? params.value[1] : '--'
              }</p>
                   </div>`;
          } else {
            return `<div class="d-flex">
                     <p class="text-truncate-1 break-all">${params.value[0] ? params.value[0] : ''
              }</p>
                   </div>`;
          }
        },
      },
      {
        headerName: 'Status',
        field: 'status',
        valueGetter: (params: any) => {
          return [
            params?.data?.lead?.status?.displayName,
            params?.data?.lead?.status?.actionName,
            params?.data?.lead?.status?.childType?.displayName,
            params?.data?.lead?.status?.childType?.actionName,
          ];
        },
        cellRenderer: (params: any) => {
          const status = params.data.lead.status?.displayName || '--';
          const childStatus = params.data.lead.status?.childType?.displayName || '--';
          let isNegStatus: boolean;
          isNegStatus = [
            'Overdue',
            'Not Interested',
            'Dropped',
            'Booking Cancel',
            'Pending',
          ].includes(status);
          const statusClass = this.globalSettingsData?.isCustomStatusEnabled
            ? 'text-black'
            : isNegStatus
              ? 'text-danger'
              : 'text-accent-green';

          return `
      <p class="text-truncate fw-600 ${statusClass}">${status === 'Site Visit Scheduled' && this.globalSettingsData?.shouldRenameSiteVisitColumn ? 'Referral Scheduled' : status}</p>
      <p class="text-truncate mt-4">${childStatus}</p>
    `;
        },
        minWidth: 120,
      },
      {
        headerName: 'Call Status',
        field: 'callStatus',
        valueGetter: (params: any) => params.data?.callStatus !== 0 ? params.data?.callStatus : '--',
        cellRenderer: (params: any) => {
          if (params.value === '--') {
            return `<span>--</span>`;
          }
          const color = this.getStatusColor(params.value);
          return `
            <span class="status-label-badge" style="background-color: ${this.hexToRgba(
            color,
            0.08
          )};">
              <p class="mr-6" style="color: ${color};">${this.getStatusLabel(params.value)}</p>
            </span>`;
        },
        cellClass: 'cursor-pointer',
        minWidth: 140,
      },
      {
        headerName: 'Source',
        field: 'source',
        valueGetter: (params: any) => [
          LeadSource[params.data.lead?.enquiry?.leadSource],
          params.data?.lead?.enquiry?.subSource,
        ],
        cellRenderer: (params: any) => {
          return `<p class="text-truncate fw-600">
                     ${params.value[0] ? params.value[0] : ''}</p>
                     <p class="text-truncate mt-4">
                     ${params.value[1] ? params.value[1] : ''}</p>`;
        },
        minWidth: 150,
      },
    ];
    //  if (this.canDelete) {
    this.gridOptions.columnDefs.unshift({
      cellRenderer: 'agGroupCellRenderer',
      headerCheckboxSelection: true,
      headerCheckboxSelectionFilteredOnly: true,
      checkboxSelection: true,
      filter: false,
      resizable: false,
      maxWidth: 50,
    });
    this.gridOptions.context = {
      componentParent: this,
    };
    this.gridOptions.onCellClicked = this.onCellClicked.bind(this);
  }

  getStatusLabel(status: IVRCallStatus): string {
    return IVRCallStatus[status] || '';
  }

  openBulkDeleteModal(BulkDeleteModal: TemplateRef<any>) {
    this.selectedLeads = this.gridApi
      ?.getSelectedNodes()
      .map((member: any) => {
        return member.data;
      });
    let initialState: any = {
      data: this.selectedLeads,
      class: 'right-modal modal-300',
    };
    this.modalRef = this.modalService.show(BulkDeleteModal, initialState);
  }

  openConfirmDeleteModal(teamName: string, teamId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: teamName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeMember(teamId);
        }
      });
    }
  }

  removeMember(id: string): void {
    const node = this.gridApi
      ?.getSelectedNodes()
      ?.filter((team: any) => team?.data?.lead?.id === id);
    this.gridApi?.deselectNode(node?.[0]);

    this.selectedLeads = this.selectedLeads?.filter(
      (team: any) => team?.lead?.id !== id
    );
    if (this.selectedLeads?.length <= 0) {
      this.modalRef.hide();
    }
  }

  bulkDelete(): void {
    if (this.modalRef) this.modalRef.hide();
    let ids: any = [];
    this.selectedLeads?.map((member: any) => ids.push(member.id));
    const payload = {
      leadIds: this.selectedLeads?.map((item: any) => item?.lead?.id),
    };
    this._store.dispatch(new BulkDeleteLeads(payload));
    // this._store
    //   .select(getBulkDeleteMemberIsLoading)
    //   .pipe(
    //     skipWhile((isLoading: boolean) => isLoading),
    //     take(1)
    //   )
    //   .subscribe((isLoading: boolean) => {
    //     this.bulkDeleteIsLoading = isLoading;
    //     this.modalRef.hide();
    //   });
  }

  getStatusColor(status: IVRCallStatus): string {
    const statusColorMap: Record<any, string> = {
      [IVRCallStatus.None]: '',
      [IVRCallStatus.InQueue]: '#6850BF',
      [IVRCallStatus.Initiated]: '#3A6DAF',
      [IVRCallStatus.Ringing]: '#FF0000',
      [IVRCallStatus.Answered]: '#ED5454',
      [IVRCallStatus.InProgress]: '#50BFA8',
    };
    return statusColorMap[status] || '#000000';
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
  }

  onCellClicked(event: any): void {
    if (event.data && event.colDef.field) {
      const selectedSection = event.colDef.field === 'status' ? 'Status' : 'Overview';
      this.openLeadPreview(event.data, selectedSection);
    }
  }

  openLeadPreview(leadData: any, selectedSection: string = 'Overview'): void {
    let leadDataId: any
    if (typeof leadData === 'string') {
      leadDataId = this.rowData?.find((item: any) => item.lead?.id === leadData);
    }
    this.isLeadPreviewOpen = true;
    const initialState = {
      data: typeof leadData === 'string' ? leadDataId?.lead : leadData?.lead,
      selectedSection: typeof leadData === 'string' ? 'Status' : selectedSection,
      cardData: this.rowData?.map((item: any) => item.lead),
      isFromAutoDialer: true,
      currentDialerLead: this.currentLead?.lead,
      closeLeadPreviewModal: () => {
        this.isLeadPreviewOpen = false;
        modalRef.hide();
      }
    };
    const modalRef = this.modalService.show(LeadPreviewComponent, {
      class: 'right-modal modal-550 ip-modal-unset',
      initialState: initialState
    });
    modalRef.onHide?.subscribe(() => {
      this.isLeadPreviewOpen = false;
    });
  }

  onPageChange(event: any): void {
    this.currOffset = event;
    const updatedPayload = {
      ...this.filtersPayload,
      pageNumber: event + 1,
      pageSize: this.pageSize,
    };
    this._store.dispatch(new UpdateFilterPayload(updatedPayload));
  }

  onPageSizeChange(event: any): void {
    const updatedPayload = {
      ...this.filtersPayload,
      pageSize: event,
      pageNumber: 1,
    };
    this._store.dispatch(new UpdateFilterPayload(updatedPayload));
    this.currOffset = 0;
  }

  applyAdvancedFilter() {
    if (!this.filterForm.valid) {
      validateAllFormFields(this.filterForm)
      return
    }

    const formValues = this.filterForm.value;
    this.filtersPayload = {
      ...this.filtersPayload,
      ...formValues,
    };

    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this.modalService.hide();
  }

  onClearAllFilters() {
    let resetObj: any = {};
    Object.keys(this.formFields)?.forEach((key: string) => {
      resetObj[key] = this.formFields[key][0];
    })
    this._store.dispatch(new UpdateFilterPayload({
      pageNumber: 1,
      pageSize: 10,
      FilterStatusId: this.selectedFilter,
      SearchByNameAndNumber: this.SearchByNameAndNumber,
    }))
    this.filterForm.patchValue(resetObj);
  }

  getArrayOfFilters(key: string, values: string) {
    const allowedKeys = ['CallStatus', 'UserIds'];
    if (
      ['pageSize', 'pageNumber', 'PageNumber', , 'PageSize', 'SearchByNameAndNumber', 'FilterStatusId']
        .includes(key) ||
      values?.length === 0
    )
      return [];
    else if (allowedKeys.includes(key)) return values;
    return values?.toString()?.split(',');
  }

  onRemoveFilter(key: string, value: string) {
    const resetFilter = (key: string): Record<string, any> => ({
      [key]: null,
    });

    const resetArrayFilter = (key: string, value: string): void => {
      const filteredValue = this.filtersPayload[key]?.filter((item: any) => item !== value);
      if (filteredValue) {
        this.filtersPayload = { ...this.filtersPayload, [key]: filteredValue };
      }
    };
    if (Array.isArray(this.filtersPayload[key]) && value) {
      resetArrayFilter(key, value);
    } else {
      this.filtersPayload = { ...this.filtersPayload, ...resetFilter(key) };
    }
    this._store.dispatch(new UpdateFilterPayload(this.filtersPayload));
    this.filterForm.patchValue(this.filtersPayload);
  }

  clearAll() {
    let resetObj: any = {};
    Object.keys(this.formFields)?.forEach((key: string) => {
      resetObj[key] = this.formFields[key][0];
    })
    this._store.dispatch(new UpdateFilterPayload({
      pageNumber: 1,
      pageSize: 10,
      FilterStatusId: this.selectedFilter,
      SearchByNameAndNumber: this.SearchByNameAndNumber,
    },))
    this.filterForm.patchValue(resetObj);
  }

  closeModal(): void {
    this.modalRef.hide();
  }

  startVerticalDrag(event: MouseEvent) {
    this.isDragging = true;
    this.offsetY = event.clientY - this.positionY;
    event.preventDefault();
  }

  @HostListener('document:mousemove', ['$event'])
  onDrag(event: MouseEvent) {
    if (!this.isDragging) return;
    this.positionY = event.clientY - this.offsetY;
    const minY = 0;
    const maxY = window.innerHeight - 120;
    this.positionY = Math.max(minY, Math.min(this.positionY, maxY));
  }

  @HostListener('document:mouseup')
  stopDrag() {
    this.isDragging = false;
  }

  ngOnDestroy(): void {
    this.stopper.emit();
    this.stopper.complete();
    if (this.timerSub) {
      this.timerSub.unsubscribe();
    }
  }
}
