<h5 class="text-white fw-600 bg-black px-20 py-12">{{selectedCampaign ? 'Edit Campaign Name' : 'Add Campaign Name'}}
</h5>
<form [formGroup]="campaignForm" autocomplete="off" class="pb-20 px-30">
    <div class="field-label-req">{{selectedCampaign ? 'Edit Name' : 'Add name'}}</div>
    <form-errors-wrapper [control]="campaignForm.controls['campaignName']" label="campaign name">
        <input type="text" (input)="doesCampaignExist($event?.target?.value)" required formControlName="campaignName"
            placeholder="enter campaign name" (keyup.enter)="onSubmit()" tabindex="1">
    </form-errors-wrapper>
    <div class="flex-end mt-30">
        <button class="btn-gray mr-20" id="addCampaignCancel" data-automate-id="addCampaignCancel" (click)="onCancel()">{{
            'BUTTONS.cancel' | translate }}</button>
        <button #focusable class="btn-coal" id="addcampaign" data-automate-id="addcampaign" (click)="onSubmit()">
            {{ 'Save' }}
        </button>
    </div>
</form>