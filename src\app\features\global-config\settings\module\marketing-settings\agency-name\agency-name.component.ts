import { Component, EventEmitter, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { filter, Subject, takeUntil } from 'rxjs';

import { PAGE_SIZE, SHOW_ENTRIES } from 'src/app/app.constants';
import { MarketingType, PropertyDateType } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  assignToSort,
  getAssignedToDetails,
  getPages,
  getTimeZoneDate,
  onFilterChanged,
  patchTimeZoneDate,
  setTimeZoneDate,
} from 'src/app/core/utils/common.util';
import { AgencyActionComponent } from 'src/app/features/global-config/settings/module/marketing-settings/agency-name/agency-action/agency-action/agency-action.component';
import { AgencyAdvanceFilterComponent } from 'src/app/features/global-config/settings/module/marketing-settings/agency-name/agency-advance-filter/agency-advance-filter/agency-advance-filter.component';
import {
  DeleteAgency,
  FetchAgencyName,
  UpdateAgencyFiltersPayload,
} from 'src/app/reducers/manage-marketing/marketing.action';
import {
  getAgencyFiltersPayload,
  getIsBulkAgencyLoading,
  getIsFetchAgencyLoading,
  getMarketingAgencyNameList,
} from 'src/app/reducers/manage-marketing/marketing.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import {
  getUserBasicDetails,
  getUsersListForReassignment,
} from 'src/app/reducers/teams/teams.reducer';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { ExportMailComponent } from 'src/app/shared/components/export-mail/export-mail.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';

@Component({
  selector: 'agency-name',
  templateUrl: './agency-name.component.html',
})
export class AgencyNameComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  public searchTermSubject = new Subject<string>();

  showEntriesSize: Array<number> = SHOW_ENTRIES;
  PageSize: number = PAGE_SIZE;
  selectedPageSize: number;
  currOffset: number = 0;
  searchTerm: string;
  filtersPayload: any = {
    PageNumber: 1,
    PageSize: 10,
  };
  appliedFilter: any = {};
  showFilters: boolean = false;
  showLeftNav: boolean = true;
  isBulkDeleteLoading = false;
  private fetchCalled: boolean = false;

  gridApi: any;
  gridColumnApi: any;
  gridOptions: any;
  defaultColDef: any;
  columns: any[];
  defaultColumns: any[];

  allAgencyData: any;
  allUserList: any[] = [];
  getPages = getPages;
  onFilterChanged = onFilterChanged;
  isAgencyLoading: boolean;
  userList: any;
  userData: any;
  toDate: any = new Date();
  fromDate: any = new Date();
  canBulkDelete: boolean = false;
  constructor(
    private gridOptionsService: GridOptionsService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private shareDataService: ShareDataService,
    private modalService: BsModalService,
    private headerTitle: HeaderTitleService,
    private router: Router
  ) {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.defaultColDef = this.gridOptions.defaultColDef;
  }

  ngOnInit(): void {
    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        const permissionsSet = new Set(permissions);
        this.canBulkDelete = permissionsSet.has('Permissions.GlobalSettings.BulkDelete');
      });
    this.selectedPageSize = 10;
    // this.store.dispatch(new FetchAgencyName(this.filtersPayload));

    this.store
      .select(getUserBasicDetails)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userData = data;
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.userList = data;
        this.allUserList = assignToSort(data, '');
      });

    this.store
      .select(getIsFetchAgencyLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: boolean) => {
        this.isAgencyLoading = data;
      });

    this.store
      .select(getAgencyFiltersPayload)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.filtersPayload = data;
        this.currOffset = this.filtersPayload?.PageNumber - 1;
        this.PageSize = this.filtersPayload?.PageSize;
        this.selectedPageSize = this.filtersPayload?.PageSize;
        this.appliedFilter = {
          ...this.appliedFilter,
          PageNumber: this.filtersPayload?.PageNumber,
          PageSize: this.filtersPayload?.PageSize,
          SearchText: this.filtersPayload?.SearchText,
        };
      });
    this.searchTermSubject.subscribe(() => {
      this.appliedFilter.PageNumber = 1;
      this.agencyFilterFunction();
    });

    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });

    this.appliedFilter = {
      ...this.appliedFilter,
      AgencyNames: this.filtersPayload?.AgencyNames,
      PhoneNumber: this.filtersPayload?.PhoneNumber,
      EmailId: this.filtersPayload?.EmailId,
      Location: this.filtersPayload?.Location,
      AssociatedLeadsCount: this.filtersPayload?.AssociatedLeadsCount,
      AssociatedPospectsCount: this.filtersPayload?.AssociatedPospectsCount,
      AssociateProperty: this.filtersPayload?.AssociateProperty,
      date: [
        patchTimeZoneDate(
          this.filtersPayload?.FromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
        patchTimeZoneDate(
          this.filtersPayload?.ToDate,
          this.userData?.timeZoneInfo?.baseUTcOffset
        ),
      ],
      DateType: PropertyDateType[this.filtersPayload?.DateType],
      ModifiedBy: this.filtersPayload?.ModifiedBy,
    };

    this.store
      .select(getMarketingAgencyNameList)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allAgencyData = data;
        if (data?.itemsCount === 0 && !this.fetchCalled) {
          this.store.dispatch(
            new FetchAgencyName({
              ...this.filtersPayload,
              path: 'v1/marketing',
              PageNumber: 1,
            })
          );
          this.currOffset = 0;
          this.fetchCalled = true;
          setTimeout(() => {
            this.fetchCalled = false;
          }, 1000);
        }
      });
    this.agencyFilterFunction();
    this.cityGridSettings();
  }

  cityGridSettings() {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Agency Name',
        field: 'Agency Name',
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned',
        // minWidth: 310,
        wrapText: true,
        autoHeight: true,
        suppressMovable: true,
        lockPosition: 'left',
        valueGetter: (params: any) => params.data?.name || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p class="mt-20 text-truncate-1 break-all">${value}</p>`;
        },
      },
      {
        headerName: 'Phone Number',
        field: 'Phone Number',
        minWidth: 190,
        valueGetter: (params: any) => params.data?.contactNo || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Email',
        field: 'Email',
        minWidth: 190,
        valueGetter: (params: any) => params.data?.email || '--',
        cellRenderer: (params: any) => {
          const value = params.value || '--';
          return `<p>${value}</p>`;
        },
      },
      {
        headerName: 'Location',
        field: 'Location',
        minWidth: 190,
        valueGetter: (params: any) => [
          params.data?.address?.subLocality
            ? params.data?.address?.subLocality
            : params.data?.address?.locality,
          params.data?.address?.city,
          params.data?.address?.state,
          params.data?.address?.postalCode,
        ],
        cellRenderer: (params: any) => {
          if (
            params.data.address === null ||
            (params.value[0] === null &&
              params.value[1] === null &&
              params.value[2] === null &&
              params.value[3] === null)
          ) {
            return '--';
          }
          const fullAddress = `${params.value[0] ? params.value[0] : ''}${params.value[1] ? ', ' + params.value[1] : ''
            }${params.value[2] ? ', ' + params.value[2] : ''}${params.value[3] ? ', ' + params.value[3] : ''
            }`;
          return `<p class="text-truncate-1 break-all" title="${fullAddress}">
                    ${params.value[0] ? params.value[0] : ''}
                    ${params.value[1] ? ', ' + params.value[1] : ''}
                    ${params.value[2] ? ', ' + params.value[2] : ''}
                    ${params.value[3] ? ', ' + params.value[3] : ''}
                  </p>`;
        },
      },
      {
        headerName: 'Associated Leads',
        field: 'Associated Leads',
        hide: true,
        valueGetter: (params: any) => [params?.data?.leadsCount],
        cellRenderer: (params: any) => {
          const leadsCount = params?.data?.leadsCount;
          const agencyName = params?.data?.name;
          return `<p>
            ${leadsCount
              ? `<a class="cursor-pointer"   
                         data-agency-name="${agencyName}"   
                         data-leads-count="${leadsCount}">  
                         ${leadsCount}  
                     </a>`
              : `<span>${leadsCount || '--'}</span>`
            }  
          </p>`;
        },
        onCellClicked: (event: any) => {
          const clickedElement = event?.event?.target;
          if (clickedElement.tagName !== 'A') {
            return;
          }

          const agencyName = event?.data?.name;
          const leadsCount = event?.data?.leadsCount;
          if (!agencyName || !leadsCount) return;
          const isCtrlClick = event?.event?.ctrlKey;
          const navigateTo = () => {
            this.router.navigate(['leads', 'manage-leads'], {
              queryParams: {
                AgencyNames: JSON.stringify([agencyName]),
                isNavigatedFromMarketing: true,
              },
            });
          };
          if (isCtrlClick) {
            window.open(
              `leads/manage-leads?isNavigatedFromMarketing=true&AgencyNames=${encodeURIComponent(
                JSON.stringify([agencyName])
              )}`,
              '_blank'
            );
          } else {
            event.event.preventDefault();
            navigateTo();
          }
        },
        cellClass: (params: any) => (params?.data?.leadsCount ? '' : ''),
      },
      {
        headerName: 'Associated Data',
        field: 'Associated Data',
        hide: true,
        valueGetter: (params: any) => [params?.data?.prospectCount],
        cellRenderer: (params: any) => {
          const prospectCount = params?.data?.prospectCount;
          const agencyName = params?.data?.name;
          return `<p>
            ${prospectCount
              ? `<a class="cursor-pointer"   
                         data-agency-name="${agencyName}"   
                         data-prospect-count="${prospectCount}">  
                         ${prospectCount}  
                     </a>`
              : `<span>${prospectCount || '--'}</span>`
            }
          </p>`;
        },
        onCellClicked: (event: any) => {
          const clickedElement = event?.event?.target;
          if (clickedElement.tagName !== 'A') {
            return;
          }
          const agencyName = event?.data?.name;
          const prospectCount = event?.data?.prospectCount;
          if (!agencyName || !prospectCount) return;
          const isCtrlClick = event?.event?.ctrlKey;
          const navigateTo = () => {
            this.router.navigate(['data', 'manage-data'], {
              queryParams: {
                AgencyNames: JSON.stringify([agencyName]),
                isNavigatedFromMarketing: true,
              },
            });
          };
          if (isCtrlClick) {
            window.open(
              `data/manage-data?isNavigatedFromMarketing=true&AgencyNames=${encodeURIComponent(
                JSON.stringify([agencyName])
              )}`,
              '_blank'
            );
          } else {
            event.event.preventDefault();
            navigateTo();
          }
        },
        cellClass: (params: any) => (params?.data?.prospectCount ? '' : ''),
      },
      // {
      //   hide: true,
      //   headerName: 'Associate Property',
      //   field: 'Associate Property',
      //   valueGetter: (params: any) => params.data?.propertyCount || '--',
      //   cellRenderer: (params: any) => {
      //     const value = params.value || '--'
      //     return `<p>${value}</p>`;
      //   },
      // },
      {
        hide: true,
        minWidth: 195,
        headerName: 'Created',
        field: 'Created',
        valueGetter: (params: any) => [
          getAssignedToDetails(params.data.createdBy, this.userList, true) ||
          '',
          params.data?.createdOn
            ? getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ) +
            ' at ' +
            getTimeZoneDate(
              params.data?.createdOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
          <p class='fw-400'>${params.value[1]}</p>
                      <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        hide: true,
        minWidth: 195,
        headerName: 'Modified',
        field: 'Modified',
        valueGetter: (params: any) => [
          getAssignedToDetails(
            params.data.lastModifiedBy,
            this.userList,
            true
          ) || '',
          params.data?.lastModifiedOn
            ? getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'dayMonthYear'
            ) +
            ' at ' +
            getTimeZoneDate(
              params.data?.lastModifiedOn,
              this.userData?.timeZoneInfo?.baseUTcOffset,
              'timeWithMeridiem'
            )
            : '',
        ],
        cellRenderer: (params: any) => {
          return `<p class="fw-600 mb-4">${params.value[0]}</p>
                  <p class='fw-400'>${params.value[1]}</p>
                              <p class="text-truncate-1 break-all text-xs text-dark-gray fst-italic">${this.userData?.timeZoneInfo?.timeZoneName &&
              this.userData?.shouldShowTimeZone &&
              params.value[1]
              ? '(' +
              this.userData?.timeZoneInfo?.timeZoneName +
              ')'
              : ''
            }</p>`;
        },
      },
      {
        headerName: 'Actions',
        minWidth: 110,
        maxWidth: 110,
        menuTabs: [],
        filter: false,
        suppressMovable: true,
        lockPosition: 'right',
        cellRenderer: AgencyActionComponent,
      },
    ];
    if (this.canBulkDelete) {
      this.gridOptions.columnDefs.unshift({
        showRowGroup: true,
        cellRenderer: 'agGroupCellRenderer',
        headerCheckboxSelection: true,
        headerCheckboxSelectionFilteredOnly: true,
        checkboxSelection: true,
        filter: false,
        pinned: window.innerWidth > 768 ? 'left' : null,
        lockPinned: true,
        cellClass: 'lock-pinned mt-8',
        maxWidth: 50,
        suppressMovable: true,
        lockPosition: 'left',
      })
    }
    this.gridOptions.context = {
      componentParent: this,
    };
  }

  onGridReady(params: any) {
    this.gridApi = params?.api;
    this.gridColumnApi = params.columnApi;
    this.toggleColumns(params);
  }

  assignCount() {
    this.PageSize = this.selectedPageSize;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageSize: this.PageSize,
      PageNumber: 1,
    };
    this.store.dispatch(new UpdateAgencyFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchAgencyName(this.filtersPayload));
    this.currOffset = 0;
  }

  hiddenKeys: string[] = ['PageSize', 'PageNumber'];
  hasValue(value: any): boolean {
    if (value === null || value === undefined) {
      return false;
    }
    const stringValue = String(value);
    return stringValue.trim().length > 0;
  }
  isKeyVisible(key: string): boolean {
    return !this.hiddenKeys.includes(key);
  }

  getArrayOfFilters(key: string, values: string | string[]) {
    if (
      ['PageSize', 'PageNumber', 'SearchText'].includes(key) ||
      values?.length === 0
    ) {
      return [];
    } else if (key === 'date' && Array.isArray(values) && values.length === 2) {
      if (key === 'date' && values[0] !== null) {
        this.toDate = setTimeZoneDate(
          new Date(values[0]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        this.fromDate = setTimeZoneDate(
          new Date(values[1]),
          this.userData?.timeZoneInfo?.baseUTcOffset
        );
        const formattedToDate = getTimeZoneDate(
          this.toDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const formattedFromDate = getTimeZoneDate(
          this.fromDate,
          this.userData?.timeZoneInfo?.baseUTcOffset,
          'dayMonthYear'
        );
        const dateRangeString = `${formattedToDate} to ${formattedFromDate}`;
        return [dateRangeString];
      } else {
        return [];
      }
    } else if (typeof values === 'string') {
      return values?.toString()?.split(',');
    }
    return values;
  }

  onRemoveFilter(key: string, value: any) {
    if (
      typeof this.appliedFilter[key] === 'string' ||
      typeof this.appliedFilter[key] === 'number' ||
      typeof this.appliedFilter[key] === 'boolean'
    ) {
      this.appliedFilter[key] = null;
    } else if (['DateType', 'date'].includes(key)) {
      this.appliedFilter[key] = null;
    } else if (Array.isArray(this.appliedFilter[key])) {
      const valueToRemove = value.toString().trim().toLowerCase();
      this.appliedFilter[key] = this.appliedFilter[key]?.filter((item: any) => {
        return item.toString().trim().toLowerCase() !== valueToRemove;
      });
    }
    this.agencyFilterFunction();
  }

  agencyFilterFunction() {
    if (
      this.appliedFilter?.AgencyNames ||
      this.appliedFilter?.PhoneNumber ||
      this.appliedFilter?.EmailId ||
      this.appliedFilter?.Location?.length ||
      this.appliedFilter?.AssociatedLeadsCount ||
      this.appliedFilter?.AssociatedPospectsCount ||
      this.appliedFilter?.AssociateProperty ||
      this.appliedFilter?.CreatedBy ||
      this.appliedFilter?.DateType ||
      this.appliedFilter?.FromDate ||
      this.appliedFilter?.date?.[0] ||
      this.appliedFilter?.ModifiedBy
    ) {
      this.showFilters = true;
    } else {
      this.showFilters = false;
    }

    let leadsCounts = this.appliedFilter?.AssociatedLeadsCount?.split('-');
    let prospectsCounts =
      this.appliedFilter?.AssociatedPospectsCount?.split('-');

    let LeadsMinCount =
      this.appliedFilter?.AssociatedLeadsCount === 'Above 100'
        ? 100
        : leadsCounts
          ? leadsCounts[0]
          : undefined;
    let LeadsMaxCount =
      this.appliedFilter?.AssociatedLeadsCount === 'Above 100'
        ? null
        : leadsCounts
          ? leadsCounts[1]
          : undefined;

    let ProspectMinCount =
      this.appliedFilter?.AssociatedPospectsCount === 'Above 100'
        ? 100
        : prospectsCounts
          ? prospectsCounts[0]
          : undefined;
    let ProspectMaxCount =
      this.appliedFilter?.AssociatedPospectsCount === 'Above 100'
        ? null
        : prospectsCounts
          ? prospectsCounts[1]
          : undefined;

    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: this.appliedFilter?.PageNumber,
      PageSize: this.appliedFilter?.PageSize,
      SearchText: this.searchTerm,
      AgencyNames: this.appliedFilter?.AgencyNames,
      PhoneNumber: this.appliedFilter?.PhoneNumber,
      EmailId: this.appliedFilter?.EmailId,
      Location: this.appliedFilter?.Location,
      LeadsMinCount,
      LeadsMaxCount,
      ProspectMinCount,
      ProspectMaxCount,
      AssociatedLeadsCount: this.appliedFilter?.AssociatedLeadsCount,
      AssociatedPospectsCount: this.appliedFilter?.AssociatedPospectsCount,
      AssociateProperty: this.appliedFilter?.AssociateProperty,
      DateType: PropertyDateType[this.appliedFilter?.DateType],
      CreatedBy: this.appliedFilter?.CreatedBy,
      FromDate: setTimeZoneDate(
        this.appliedFilter?.date?.[0],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ToDate: setTimeZoneDate(
        this.appliedFilter.date?.[1],
        this.userData?.timeZoneInfo?.baseUTcOffset
      ),
      ModifiedBy: this.appliedFilter?.ModifiedBy,
    };

    this.store.dispatch(new UpdateAgencyFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchAgencyName(this.filtersPayload));

    this.currOffset = 0;
  }

  exportAgency() {
    let { PageNumber, PageSize, ...filteredPayload } = this.filtersPayload;
    let initialState: any = {
      payload: {
        ...filteredPayload,
        exportType: MarketingType.AgencyName,
        path: 'agency',
      },
      class: 'modal-400 modal-dialog-centered ph-modal-unset',
    };

    this.modalService.show(
      ExportMailComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 modal-dialog-centered ph-modal-unset',
          initialState,
        }
      )
    );
  }

  onPageChange(e: any) {
    this.currOffset = e;
    this.filtersPayload = {
      ...this.filtersPayload,
      PageNumber: e + 1,
    };
    this.store.dispatch(new UpdateAgencyFiltersPayload(this.filtersPayload));
    this.store.dispatch(new FetchAgencyName(this.filtersPayload));
  }

  deselectOptions() {
    let selectedNodes = this.gridApi?.getSelectedNodes();
    selectedNodes.forEach((node: any) => node.setSelected(false));
  }

  deleteAgency() {
    let initialState: any = {
      type: 'manageMarketingDelete',
      data: {
        buttonContent: 'Delete',
        fieldType: 'Delete',
        heading: `Deleting Agencies?`,
        bulkMessage: `Are you sure you want to delete the <b>"${this.gridApi?.getSelectedNodes()?.length
          }"</b> selected agencies?`,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.isClickYesOrNo(true);
        }
      });
    }
  }

  isClickYesOrNo(isClick: boolean) {
    if (isClick) {
      this.isBulkDeleteLoading = true;
      let selectedNodes = this.gridApi?.getSelectedNodes();
      let selectedIds = selectedNodes?.map((node: any) => node.data?.id);
      this.store.dispatch(new DeleteAgency(selectedIds));
      const loadingSubscription = this.store
        .select(getIsBulkAgencyLoading)
        .pipe(filter((isLoading: boolean) => !isLoading))
        .subscribe(() => {
          this.isBulkDeleteLoading = false;
          this.modalService.hide();
          this.store.dispatch(
            new UpdateAgencyFiltersPayload(this.filtersPayload)
          );
          this.store.dispatch(new FetchAgencyName(this.filtersPayload));
        });
    }
    this.modalRef.hide();
  }

  onSearch($event: any) {
    if ($event.key === 'Enter') {
      if (this.searchTerm === '' || this.searchTerm === null) {
        return;
      }
      this.searchTermSubject.next(this.searchTerm);
    }
  }

  isEmptyInput($event: any) {
    if (this.searchTerm === '' || this.searchTerm === null) {
      this.searchTermSubject.next('');
    }
  }

  openAdvFiltersModal() {
    let initialState: any = {
      class: 'modal-600 modal-dialog tb-modal-unset',
      initialState: {
        applyAdvancedFilter: this.applyAdvancedFilter.bind(this),
        onClearAllFilters: this.onClearAllFilters.bind(this),
        appliedFilter: this.appliedFilter,
      },
    };
    const modalRef = this.modalService.show(
      AgencyAdvanceFilterComponent,
      initialState
    );
  }

  toggleColumns(params: any): void {
    this.columns = params?.columnApi?.getColumns()?.map((column: any) => {
      return {
        label: column?.userProvidedColDef?.headerName,
        value: column,
      };
    });
    this.columns = this.columns
      .slice(2, this.columns?.length - 1)
      .sort((a: any, b: any) => a?.label?.localeCompare(b?.label));
    this.defaultColumns = this.columns?.filter(
      (col) => col?.value?.getColDef()?.hide !== true
    );

    let columnState = JSON.parse(localStorage.getItem('myAgencyColumnState'));
    if (columnState) {
      this.gridColumnApi.applyColumnState({
        state: columnState,
        applyOrder: true,
      });
    }

    let columnData = localStorage.getItem('manage-agency-columns')?.split(',');

    if (columnData?.length) {
      let visibleColumns = this.columns?.filter((col: any) =>
        columnData?.includes(col.label)
      );
      this.defaultColumns = visibleColumns;
      this.onColumnsSelected(visibleColumns);
    }
  }

  onColumnsSelected(columns: any[]) {
    let colData = columns?.map((column: any) => column.label);
    localStorage.setItem('manage-agency-columns', colData?.toString());
    if (!columns) {
      columns = this.defaultColumns;
    }
    const cols = columns?.map((col) => col.value);
    this.gridColumnApi?.setColumnsVisible(cols, true);
    const nonSelectedCols = this.columns?.filter((col: any) => {
      return !cols.includes(col.value);
    });
    this.gridColumnApi?.setColumnsVisible(
      nonSelectedCols.map((col) => col.value),
      false
    );
    var columnState: any = this.gridColumnApi.getColumnState();
    localStorage.setItem(
      'myAgencyColumnState',
      JSON.stringify(
        columnState.map((column: any) => ({
          ...column,
          sort: null,
        }))
      )
    );
    this.gridColumnApi.applyColumnState({
      state: columnState,
      applyOrder: true,
    });
  }

  onSetColumnDefault() {
    this.defaultColumns = this.columns.filter(
      (col) => col.value.getColDef().hide !== true
    );
    this.onColumnsSelected(this.defaultColumns);
  }

  onClearAllFilters(data: string) {
    if (this.appliedFilter && typeof this.appliedFilter === 'object') {
      Object.keys(this.appliedFilter).forEach((key) => {
        if (key !== 'PageNumber' && key !== 'PageSize') {
          this.appliedFilter[key] = null;
        }
      });
    }

    this.agencyFilterFunction();
  }

  applyAdvancedFilter() {
    this.agencyFilterFunction();
    this.modalService.hide();
  }

  openBulkDeleteModal(bulkDeleteModal: any) {
    this.modalRef = this.modalService.show(bulkDeleteModal, {
      class: 'right-modal modal-500',
    });
  }

  openConfirmDeleteModal(agencyName: string, agencyId: string): void {
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'remove',
      title: agencyName,
      fieldType: 'from the selection',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.removeLead(agencyId);
        }
      });
    }
  }

  removeLead(id: string): void {
    const selectedNodes = this.gridApi?.getSelectedNodes();
    const selectedNode = selectedNodes?.find(
      (lead: any) => lead?.data?.id === id
    );
    if (selectedNode) {
      this.gridApi?.deselectNode(selectedNode);
    }
    const remainingSelectedNodes = this.gridApi?.getSelectedNodes();
    if (!remainingSelectedNodes || remainingSelectedNodes.length === 0) {
      this.modalService.hide();
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
