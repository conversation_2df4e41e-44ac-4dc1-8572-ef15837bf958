import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { catchError, of, takeUntil } from 'rxjs';
import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { assignToSort } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { FetchAdminsAndReportees, FetchUsersListForReassignment } from 'src/app/reducers/teams/teams.actions';
import { getAdminsAndReportees, getUsersListForReassignment } from 'src/app/reducers/teams/teams.reducer';
import { AutodialerService } from 'src/app/services/controllers/autodialer.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'automation-settings',
  templateUrl: './automation-settings.component.html',
})
export class AutomationSettingsComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();

  isAutoDialerOpen: boolean = false;
  autoDialerForm: FormGroup;
  canAssignToAny: boolean;
  activeUsers: any = [];
  allActiveUsers: any = [];
  isAutoDialerEnabled: any;

  constructor(
    private router: Router,
    public metaTitle: Title,
    private headerTitle: HeaderTitleService,
    private fb: FormBuilder,
    private store: Store<AppState>,
    public dialerService: AutodialerService,
    public notificationService: NotificationsService
  ) {
    this.autoDialerForm = this.fb.group({
      userIds: [null],
      maxCallInterval: [null],
      priority: [false],
    });
  }

  ngOnInit(): void {
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');

    this.store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.Users.AssignToAny')) {
          this.canAssignToAny = true;
          this.store.dispatch(new FetchUsersListForReassignment());
        } else {
          this.store.dispatch(new FetchAdminsAndReportees());
        }
      });

    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.isAutoDialerEnabled = data?.callSettings?.isAutoDialerEnabled;
        if (this.isAutoDialerEnabled) {
          this.dialerService.geteDialerConfiguration().subscribe({
            next: (res: any) => {
              this.autoDialerForm.patchValue({
                userIds: res?.data?.userIds,
                maxCallInterval: res?.data?.maxCallInterval !== '0' ? res?.data?.maxCallInterval : null,
                priority: res?.data?.prioritizeIntegrationLead
              })
            },
            error: () => {
              this.notificationService.error('Failed to load dialer settings.');
            }
          });
        }
      });

    this.store
      .select(getAdminsAndReportees)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.activeUsers = data?.filter((user: any) => user.isActive);
        this.activeUsers = this.activeUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.activeUsers = assignToSort(this.activeUsers, '');
      });

    this.store
      .select(getUsersListForReassignment)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.allActiveUsers = data?.filter((user: any) => user.isActive);
        this.allActiveUsers = this.allActiveUsers?.map((user: any) => {
          user = {
            ...user,
            fullName: user.firstName + ' ' + user.lastName,
          };
          return user;
        });
        this.allActiveUsers = assignToSort(this.allActiveUsers, '');
      });
  }

  navigateToReportAutomation(): void {
    this.router.navigate(['/global-config/report-automation']);
  }

  cancelAutoDialer(): void {
    this.isAutoDialerOpen = false;
  }

  saveAutoDialerSettings(): void {
    if (!this.autoDialerForm.valid) return;
    const formValues = this.autoDialerForm.value;
    const payload = {
      userIds: formValues?.userIds ?? [],
      maxCallInterval: formValues?.maxCallInterval?.toString(),
      PrioritizeIntegrationLead: formValues?.priority
    };
    this.dialerService.updateDialerConfiguration(payload).subscribe({
      next: () => {
        this.notificationService.success('Configuration Updated Successfuly')
        this.isAutoDialerOpen = false;
        this.dialerService.geteDialerConfiguration().subscribe({
          next: () => {
          },
          error: (err) => {
            this.notificationService.error('Failed to fetch updated settings');
          }
        });
      },
      error: (err) => {
        catchError((err) => of(new OnError(err)))
      }
    });
  }

}
