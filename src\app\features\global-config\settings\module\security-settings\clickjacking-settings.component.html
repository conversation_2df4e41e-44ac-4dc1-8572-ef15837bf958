<div class="modal-header">
  <h4 class="modal-title">Clickjacking Protection Settings</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="onCancel()">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">
  <form [formGroup]="clickjackingForm">
    <!-- Enable/Disable Clickjacking Protection -->
    <div class="form-group mb-3">
      <div class="d-flex justify-content-between align-items-center">
        <div>
          <h5 class="fw-600">Enable Clickjacking Protection</h5>
          <p class="text-muted small">Prevents the application from being embedded in iframes to protect against clickjacking attacks.</p>
        </div>
        <div class="form-check form-switch">
          <input class="form-check-input" type="checkbox" formControlName="enabled" id="enableProtection">
          <label class="form-check-label" for="enableProtection">
            {{ clickjackingForm.get('enabled')?.value ? 'Enabled' : 'Disabled' }}
          </label>
        </div>
      </div>
    </div>

    <div *ngIf="clickjackingForm.get('enabled')?.value">
      <!-- Frame Options -->
      <div class="form-group mb-3">
        <label class="form-label fw-600">X-Frame-Options Policy</label>
        <select class="form-select" formControlName="frameOptions" (change)="onFrameOptionsChange()">
          <option *ngFor="let option of frameOptionsValues" [value]="option.value">
            {{ option.label }}
          </option>
        </select>
        <small class="form-text text-muted">
          Controls whether the application can be embedded in frames from other domains.
        </small>
      </div>

      <!-- Allowed Origins (only show when SAMEORIGIN is selected) -->
      <div *ngIf="clickjackingForm.get('frameOptions')?.value === 'SAMEORIGIN'" class="form-group mb-3">
        <label class="form-label fw-600">Allowed Origins</label>
        <div formArrayName="allowedOrigins">
          <div *ngFor="let origin of allowedOriginsArray.controls; let i = index" class="input-group mb-2">
            <input type="url" class="form-control" [formControlName]="i" placeholder="https://example.com">
            <button type="button" class="btn btn-outline-danger" (click)="removeAllowedOrigin(i)">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <button type="button" class="btn btn-outline-primary btn-sm" (click)="addAllowedOrigin()">
          <i class="fas fa-plus"></i> Add Origin
        </button>
        <small class="form-text text-muted">
          Specify domains that are allowed to embed this application in frames.
        </small>
      </div>

      <!-- Continuous Monitoring -->
      <div class="form-group mb-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="fw-600">Continuous Monitoring</h6>
            <p class="text-muted small">Continuously monitor for framing attempts and automatically redirect if detected.</p>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" formControlName="continuousMonitoring" id="continuousMonitoring">
            <label class="form-check-label" for="continuousMonitoring">
              {{ clickjackingForm.get('continuousMonitoring')?.value ? 'On' : 'Off' }}
            </label>
          </div>
        </div>
      </div>

      <!-- Monitoring Interval -->
      <div *ngIf="clickjackingForm.get('continuousMonitoring')?.value" class="form-group mb-3">
        <label class="form-label fw-600">Monitoring Interval (ms)</label>
        <input type="number" class="form-control" formControlName="monitoringInterval" min="1000" max="10000">
        <small class="form-text text-muted">
          How often to check for framing attempts (minimum 1000ms).
        </small>
      </div>

      <!-- Content Security Policy -->
      <div class="form-group mb-3">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="fw-600">Content Security Policy (CSP)</h6>
            <p class="text-muted small">Enable CSP frame-ancestors directive for additional protection.</p>
          </div>
          <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" formControlName="cspEnabled" id="cspEnabled">
            <label class="form-check-label" for="cspEnabled">
              {{ clickjackingForm.get('cspEnabled')?.value ? 'Enabled' : 'Disabled' }}
            </label>
          </div>
        </div>
      </div>

      <!-- Additional Security Headers -->
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Additional Security Headers</h6>
        </div>
        <div class="card-body">
          <!-- X-Content-Type-Options -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="fw-600">X-Content-Type-Options</span>
                <small class="text-muted d-block">Prevents MIME type sniffing</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" formControlName="xContentTypeOptions" id="xContentType">
                <label class="form-check-label" for="xContentType">
                  {{ clickjackingForm.get('xContentTypeOptions')?.value ? 'On' : 'Off' }}
                </label>
              </div>
            </div>
          </div>

          <!-- X-XSS-Protection -->
          <div class="form-group mb-3">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <span class="fw-600">X-XSS-Protection</span>
                <small class="text-muted d-block">Enables XSS filtering in browsers</small>
              </div>
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" formControlName="xXSSProtection" id="xXSSProtection">
                <label class="form-check-label" for="xXSSProtection">
                  {{ clickjackingForm.get('xXSSProtection')?.value ? 'On' : 'Off' }}
                </label>
              </div>
            </div>
          </div>

          <!-- Referrer Policy -->
          <div class="form-group mb-3">
            <label class="form-label fw-600">Referrer Policy</label>
            <select class="form-select" formControlName="referrerPolicy">
              <option value="no-referrer">no-referrer</option>
              <option value="no-referrer-when-downgrade">no-referrer-when-downgrade</option>
              <option value="origin">origin</option>
              <option value="origin-when-cross-origin">origin-when-cross-origin</option>
              <option value="same-origin">same-origin</option>
              <option value="strict-origin">strict-origin</option>
              <option value="strict-origin-when-cross-origin">strict-origin-when-cross-origin</option>
              <option value="unsafe-url">unsafe-url</option>
            </select>
            <small class="form-text text-muted">
              Controls how much referrer information is sent with requests.
            </small>
          </div>
        </div>
      </div>
    </div>
  </form>

  <!-- Test Section -->
  <div class="mt-4 p-3 bg-light rounded">
    <h6 class="fw-600">Test Protection</h6>
    <p class="text-muted small">Click the button below to test if clickjacking protection is working.</p>
    <button type="button" class="btn btn-outline-info btn-sm" (click)="testClickjackingProtection()">
      <i class="fas fa-shield-alt"></i> Test Protection
    </button>
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-outline-secondary" (click)="resetToDefaults()">
    Reset to Defaults
  </button>
  <button type="button" class="btn btn-secondary" (click)="onCancel()">
    Cancel
  </button>
  <button type="button" class="btn btn-primary" (click)="onSave()" [disabled]="!clickjackingForm.valid">
    Save Changes
  </button>
</div>
