import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { AddChannelPartnerComponent } from '../../add-channel-partner/add-channel-partner/add-channel-partner.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { DeleteChannelPartner } from 'src/app/reducers/manage-marketing/marketing.action';

@Component({
  selector: 'channel-partner-action',
  templateUrl: './channel-partner-action.component.html',
})
export class ChannelPartnerActionComponent implements OnInit {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editChannelPartner(partner: any): void {
    const initialState = {
      selectedChannelPartner: partner,
    };
    this.modalRef = this.modalService.show(AddChannelPartnerComponent, {
      class: 'right-modal modal-350 ip-modal-unset',
      initialState,
    });
  }

  deleteDeleteChannelPartner(partner: any) {
    let initialState: any = {
      type: 'manageMarketingDelete',
      data: {
        buttonContent:'Delete Channel Partner',
        fieldType: 'Delete',
        heading: `Deleting Channel Partner Name?`,
        agencyData: partner,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteChannelPartner([partner?.id]));
        }
      });
    }
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
