import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { PROP_DATE_TYPE } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { changeCalendar, onPickerOpened } from 'src/app/core/utils/common.util';
import { getUserBasicDetails } from 'src/app/reducers/teams/teams.reducer';

@Component({
  selector: 'campaign-name-advance-filter',
  templateUrl: './campaign-name-advance-filter.component.html',
})
export class CampaignNameAdvanceFilterComponent implements OnInit {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  applyAdvancedFilter: () => void;
  onClearAllFilters: (data: any)=> void;
  appliedFilter:any;
  dateTypeList: Array<string> = PROP_DATE_TYPE.slice(0, 3);
  locationsIsLoading: boolean = true;
  locations: string[];
  propertyList: Array<string> = ['0 - 50' , '50 - 100' , 'above 100'];
  userData: any;
  currentDate: Date = new Date();
  onPickerOpened = onPickerOpened;

  constructor(
    public modalRef: BsModalRef,
    private modalService: BsModalService,
    private _store: Store<AppState>,
  ) { 
  }

  ngOnInit(): void {
    this._store
    .select(getUserBasicDetails)
    .pipe(takeUntil(this.stopper))
    .subscribe((data: any) => {
      this.userData = data;
      this.currentDate = changeCalendar(this.userData?.timeZoneInfo?.baseUTcOffset)
    });
  }

  onResetDateFilter() {
    this.appliedFilter = {
      ...this.appliedFilter,
      dateType: null,
      date: '',
    };
  }

}
