import { Action } from '@ngrx/store';

export enum CustomFormActionTypes {
  FETCH_CUSTOMFORM = '[CUSTOMFORM] Fetch Custom Form',
  FETCH_CUSTOMFORM_SUCCESS = '[CUSTOMFORM] Fetch Custom Form Success',
  ADD_CUSTOMFORM = '[CUSTOMFORM] Add Custom Form To Lead',
  ADD_CUSTOMFORM_SUCCESS = '[CUSTOMFORM] Add Custom Form Success',
  DELETE_CUSTOMFORM = '[CUSTOMFORM] Delete Custom Form',
  DELETE_CUSTOMFORM_SUCCESS = '[CUSTOMFORM] Delete Custom Form Success',
  FETCH_CUSTOMFORM_FIELD_EXIST = "[CUSTOMFORM] Fetch Custom Form Field Exist Success",
  FETCH_CUSTOMFORM_FIELD_EXIST_SUCCESS = "[CUSTOMFORM] Fetch Custom Form Field Exist",
  FETCH_CUSTOMFORM_FIELD = "[CUSTOMFORM] Add Custom Form Field",
  FETCH_CUSTOMFORM_FIELD_SUCCESS = "[CUSTOMFORM] Add Custom Form Field Success",
}

export class FetchCustomForm implements Action {
  readonly type: string = CustomFormActionTypes.FETCH_CUSTOMFORM;
  constructor() { }
}

export class FetchCustomFormSuccess implements Action {
  readonly type: string = CustomFormActionTypes.FETCH_CUSTOMFORM_SUCCESS;
  constructor(public resp: any = '') { }
}

export class AddCustomForm implements Action {
  readonly type: string = CustomFormActionTypes.ADD_CUSTOMFORM;
  constructor(public payload: any = {}) { }
}

export class AddCustomFormSuccess implements Action {
  readonly type: string = CustomFormActionTypes.ADD_CUSTOMFORM_SUCCESS;
  constructor() { }
}

export class DeleteCustomForm implements Action {
  readonly type: string = CustomFormActionTypes.DELETE_CUSTOMFORM;
  constructor(public id: string) { }
}

export class DeleteCustomFormSuccess implements Action {
  readonly type: string = CustomFormActionTypes.DELETE_CUSTOMFORM_SUCCESS;
  constructor() { }
}

export class FetchCustomFormFieldExist implements Action {
  readonly type: string = CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD_EXIST;
  constructor(public payload: any = {}) { }
}

export class FetchCustomFormFieldExistSuccess implements Action {
  readonly type: string =
    CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD_EXIST_SUCCESS;
  constructor(public resp: boolean) { }
}

export class FetchCustomFormField implements Action {
  readonly type: string = CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD;
  constructor(public id: string) { }
}

export class FetchCustomFormFieldSuccess implements Action {
  readonly type: string = CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD_SUCCESS;
  constructor(public resp: any = '') { }
}