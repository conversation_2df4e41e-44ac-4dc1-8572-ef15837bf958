<div class="h-100vh bg-light-pearl">
    <div class="bg-dark flex-center flex-between p-12 h-40 position-relative">
        <h5 class="text-white fw-400">{{selectedChannelPartner ? 'Edit Channel Partner' : 'Add Channel Partner'}}</h5>
        <span (click)="modalRef.hide()"
            class="icon ic-close ic-sm position-absolute top-7 right-8 cursor-pointer"></span>
    </div>

    <form [formGroup]="addChannelPartnerForm" class="px-10">
        <div class="flex-col px-10 bg-white w-100 mt-10 scrollbar scroll-hide h-100-110">
            <div class="w-100">
                <div class="field-label-req">Channel partner name</div>
                <form-errors-wrapper [label]="'channel partner name'"
                    [control]="addChannelPartnerForm.controls['channelPartnerName']">
                    <input appDebounceInput [debounceTime]="500" (debounceEvent)="doesChannelPartnerExist($event)"
                        type="text" formControlName="channelPartnerName" placeholder="Ex. Zee">
                </form-errors-wrapper>
            </div>
            <div class="w-100">
                <div class="field-label">Phone number</div>
                <form-errors-wrapper label="phone number" [control]="addChannelPartnerForm.controls['phoneNumber']">
                    <ngx-mat-intl-tel-input #phoneNumber *ngIf="hasInternationalSupport"
                        [preferredCountries]="preferredCountries" [enablePlaceholder]="true" [enableSearch]="true"
                        class="phoneNumber no-validation" formControlName="phoneNumber">
                    </ngx-mat-intl-tel-input>
                    <ngx-mat-intl-tel-input #phoneNumber *ngIf="!hasInternationalSupport"
                        [preferredCountries]="preferredCountries" [onlyCountries]="preferredCountries"
                        [enablePlaceholder]="true" [enableSearch]="true" class="phoneNumber no-validation"
                        formControlName="phoneNumber">
                    </ngx-mat-intl-tel-input>
                </form-errors-wrapper>
                <!-- <small class="text-danger text-xxs flex-end mr-8 position-absolute right-20"
                    *ngIf="addChannelPartnerForm.get('phoneNumber')?.errors?.validatePhoneNumber">
                    Please enter a valid phone number.
                </small> -->
            </div>
            <div class="w-100">
                <div class="field-label">Email ID</div>
                <form-errors-wrapper label="email" [control]="addChannelPartnerForm.controls['email']">
                    <input type="email" formControlName="email" placeholder="ex. <EMAIL>">
                </form-errors-wrapper>
                <!-- <small class="text-danger text-xxs flex-end mr-8 position-absolute right-20"
                        *ngIf="addChannelPartnerForm.get('email')?.errors?.email">
                        Please enter a valid email address.
                    </small> -->
            </div>

            <ng-container *ngIf="!isShowManualCustomerLocation; else manualCustomerLocation">
                <div class="field-label">Location</div>
                <div class="field-tag">
                    <ng-select formControlName="location" [items]="placesList" bindLabel="location"
                        placeholder="Search Location" (search)="searchPlaceTerm$.next($event.term)"
                        [searchFn]="customSearch" [editableSearchTerm]="true" [closeOnSelect]="true"
                        [clearSearchOnAdd]="true" [virtualScroll]="true" class="bg-white w-310"
                        (keydown)="onKeydown($event)">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index">
                            <div [title]="item?.location">{{item?.location}}</div>
                        </ng-template>
                    </ng-select>
                    <div class="position-absolute top-5 right-40" *ngIf="isLoadingPlaces">
                        <img src="assets/images/loader-rat.svg" class="rat-loader h-30px w-30px" alt="loader">
                    </div>
                    <div class="search icon ic-search ic-sm ic-coal"></div>
                </div>
            </ng-container>
            <ng-template #manualCustomerLocation>
                <div class="field-label">Locality</div>
                <div class="form-group w-310">
                    <input type="text" formControlName="customerLocality" placeholder="ex. HSR Layout">
                </div>
                <div class="form-group w-100">
                    <div class="field-label">City</div>
                    <input type="text" formControlName="customerCity" placeholder="ex. Bengaluru">
                </div>
                <div class="form-group w-100">
                    <div class="field-label">State</div>
                    <input type="text" formControlName="customerState" placeholder="ex. Karnataka">
                </div>
            </ng-template>
            <div class="d-flex pb-10 mt-2">
                <div class="cursor-pointer align-center fw-semi-bold text-accent-green"
                    (click)="isShowManualCustomerLocation = !isShowManualCustomerLocation">
                    <span class="icon ic-xs ic-accent-green"
                        [ngClass]="isShowManualCustomerLocation ? 'ic-search' : 'ic-add'"></span>
                    <span>{{isShowManualCustomerLocation ? 'Location List' : 'Manually Enter Location'}}</span>
                </div>
            </div>
            <div class="w-100">
                <div class="field-label mt-0">RERA Number</div>
                <div class="form-group">
                    <input type="text" formControlName="reraNumber" placeholder="Enter RERA Number">
                </div>
            </div>
            <div class="w-100">
                <div class="field-label">Company Name</div>
                <div class="form-group">
                    <input type="text" formControlName="companyName" placeholder="Enter Company Name">
                </div>
            </div>
        </div>
        <div class="w-100 flex-end p-10 gap-4 position-absolute bottom-0 bg-white">
            <h5 (click)="modalRef.hide()" class="fw-semi-bold text-black-200 text-decoration-underline cursor-pointer"
                id="btnCancelUpdateStatus" data-automate-id="btnCancelUpdateStatus">
                {{ 'BUTTONS.cancel' | translate }}
            </h5>
            <button (click)="!isAddEditCP ? onSubmit() : ''" class="btn-coal mr-8 px-10 min-w-fit-content"
                id="btnSaveConfiguration" data-automate-id="btnSaveConfiguration">
                <span *ngIf="!isAddEditCP else buttonDots">Save details</span>
            </button>
        </div>
    </form>
</div>

<ng-template #buttonDots>
    <div class="container px-4">
        <ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-falling dot-white"></div>
        </ng-container>
    </div>
</ng-template>