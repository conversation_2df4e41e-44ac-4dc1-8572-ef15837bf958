import { Component, EventEmitter, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { skipWhile, take, takeUntil } from 'rxjs';
import { BREVO_TEMPLATE_VARIABLES } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { validateAllFormFields } from 'src/app/core/utils/common.util';
import { CreateBrevoTemplate, FetchBrevoAccountListByName, UpdateBrevoTemplate } from 'src/app/reducers/email/email-settings.action';
import { getBrevoAccountListByName, getIsTemplateCreating } from 'src/app/reducers/email/email-settings.reducer';

@Component({
  selector: 'add-brevo-template',
  templateUrl: './add-brevo-template.component.html',
})
export class AddBrevoTemplateComponent implements OnInit {
  selectedTemplate: any;
  addBrevoTemplateForm: FormGroup;
  isShowVariablePopup: boolean = false;
  variables: any[] = BREVO_TEMPLATE_VARIABLES;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  brevoAccountListByName: any;
  isTemplateCreating: boolean;

  constructor(
    private modalService: BsModalService,
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder
  ) { }

  ngOnInit(): void {
    this.addBrevoTemplateForm = this.fb.group({
      emailApiIntegrationId: [null, Validators.required],
      templateName: [null, Validators.required],
      subject: [null, Validators.required],
      body: [null, Validators.required],
    })
    this.initializeDispatch();
    this.initializeSubscription();
    if (this.selectedTemplate) {
      this.addBrevoTemplateForm.patchValue({
        emailApiIntegrationId: this.selectedTemplate?.emailApiIntegrationDataId,
        templateName: this.selectedTemplate?.name,
        subject: this.selectedTemplate?.subject,
        body: this.selectedTemplate?.body.replace(/<br>/g, "\n"),
      });
    }
  }

  initializeDispatch() {
    this.store.dispatch(new FetchBrevoAccountListByName());
  }

  initializeSubscription() {
    this.store.select(getBrevoAccountListByName).pipe(takeUntil(this.stopper)).subscribe((resp) => {
      this.brevoAccountListByName = resp;
    });
  }

  addVariable(variable: string) {
    const textToInsert = ' ' + variable;

    const textarea = document.getElementById(
      'txtLeadMsg'
    ) as HTMLTextAreaElement;

    const cursorPosition = textarea.selectionStart;

    let textBeforeCursor = textarea.value.substring(0, cursorPosition);
    let textAfterCursor = textarea.value.substring(cursorPosition);

    if (cursorPosition === undefined || cursorPosition === null) {
      textBeforeCursor = textarea.value;
      textAfterCursor = ' ';
    }

    textarea.value = textBeforeCursor + textToInsert + textAfterCursor;

    textarea.selectionStart = cursorPosition + textToInsert.length;
    textarea.selectionEnd = cursorPosition + textToInsert.length;


    this.isShowVariablePopup = false;
    this.addBrevoTemplateForm.get('body').setValue(textarea.value);
  }

  onSave() {
    if (this.addBrevoTemplateForm.invalid) {
      validateAllFormFields(this.addBrevoTemplateForm);
      return;
    }
    let payload = {
      ...(this.selectedTemplate ? this.selectedTemplate : {}),
      subject: this.addBrevoTemplateForm.value.subject,
      body: this.addBrevoTemplateForm.value.body.replace(/\n/g, "<br>"),
      name: this.addBrevoTemplateForm.value.templateName,
      bodyType: 2,
      emailApiIntegrationDataId: this.addBrevoTemplateForm.value.emailApiIntegrationId,
      emailServiceProviders: 2,
    }
    this.selectedTemplate ? this.store.dispatch(new UpdateBrevoTemplate(payload)) : this.store.dispatch(new CreateBrevoTemplate(payload));
    this.isTemplateCreating = true;
    this.store.select(getIsTemplateCreating).pipe(takeUntil(this.stopper), skipWhile((isLoading: boolean) => isLoading), take(1)).subscribe((resp) => {
      this.isTemplateCreating = resp;
      this.modalRef.hide();
    });
  }


  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
