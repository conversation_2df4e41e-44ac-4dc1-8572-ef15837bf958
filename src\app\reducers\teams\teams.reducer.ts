import { Action, createSelector } from '@ngrx/store';
import { AppState } from 'src/app/app.reducer';
import { UserFilter } from 'src/app/core/interfaces/property.interface';
import {
  DeleteRolesSuccess,
  DoesEmailExistsSuccess,
  DoesPhoneNoExistsSuccess,
  DoesUsrnameExistsSuccess,
  FetchAdminsAndReporteesSuccess,
  FetchAllAdminSuccess,
  FetchAllExistingPermissionSuccess,
  FetchAllTeamsSuccess,
  FetchBulkUserLeadCountSuccess,
  FetchDeletedUserListSuccess,
  FetchDepartmentsListSuccess,
  FetchDesignationsListSuccess,
  FetchGeneralManagerListSuccess,
  FetchGeoFencingListSuccess,
  FetchIVRSettingListByIdSuccess,
  FetchIVRSettingListSuccess,
  FetchOnlyAdminsSuccess,
  FetchOnlyReporteesSuccess,
  FetchOnlyReporteesWithInactiveSuccess,
  FetchRecentSearchSuccess,
  FetchReporteesSuccess,
  FetchReportingManagerDetailsSuccess,
  FetchRetentionListSuccess,
  FetchRolePermissionByIdSuccess,
  FetchRolesListSuccess,
  FetchRotationListSuccess,
  FetchTeamExportTrackerSuccess,
  FetchTeamListSuccess,
  FetchTeamMemberSuccess,
  FetchUnassignedUserSuccess,
  FetchUserAssignedDataByIdSuccess,
  FetchUserAssignmentsByIdSuccess,
  FetchUserBasicDetailsByIdSuccess,
  FetchUserByIdSuccess,
  FetchUserExcelUploadedSuccess,
  FetchUserExportTrackerSuccess,
  FetchUserProfileSuccess,
  FetchUsersByDesignationSuccess,
  FetchUsersListForReassignmentSuccess,
  FetchUsersListSuccess,
  FetchWhatsappSettingListSuccess,
  FetchWithoutAdminsSuccess,
  TeamsActionTypes,
  TeamsFilterPayload,
  TeamsMemberFilterPayload,
  UpdateFilterPayload,
  UserExcelUploadSuccess
} from 'src/app/reducers/teams/teams.actions';

export type TeamsState = {
  isRolesLoading: boolean;
  isBulkDeleteRolesLoading: boolean;
  userAssignments: any;
  userData: any,
  rolesList?: any[];
  permissionsList?: any;
  usersList?: any[];
  selectedRoleToUpdate?: any;
  designationList?: any[];
  departmentsList?: any[];
  selectedUserToUpdate?: any;
  userCount?: any;
  usersListForReassignment?: any[];
  doesUserNameExists: boolean;
  doesEmailExists: boolean;
  doesPhoneNoExists: boolean;
  excelColumnHeading?: any;
  userBasicDetails?: any;
  userProfile?: any;
  isUserProfileLoading?: boolean;
  reportees?: Array<Object>;
  adminsAndReportees?: Array<Object>;
  onlyReportees?: Array<Object>;
  onlyReporteesWithInactive?: Array<Object>;
  userLeadCount?: any;
  filtersPayload: UserFilter;
  usersData: any;
  usersDataIsLoading: boolean;
  isLoading: boolean;
  reporteesIsLoading: boolean;
  usersListForReassignmentIsLoading: boolean;
  adminsAndReporteesIsLoading: boolean;
  bulkUserLeadCount: any;
  IVRSetting: any;
  IVRSettingById: any;
  IVRSettingByIdIsLoading: boolean;
  usersListIsLoading: boolean;
  designationListisLoading: boolean;
  departmentsListIsLoading: boolean;
  updateBulkUsersIsLoading: boolean;
  usersCountIsLoading: boolean;
  onlyReporteesWithInactiveIsLoading: boolean;
  onlyAdmins: any;
  onlyAdminsIsLoading: boolean;
  exportUsersStatus: any;
  isExportStatusLoading: boolean;
  withoutAdmins: any;
  withoutAdminsIsLoading: boolean;
  managerDetails: any[];
  allTeamLeads?: any;
  totalTeamLeads?: number;
  allTeamMembers?: any;
  unAssignedIsLoading: boolean;
  unassignedUser: any;
  allTeams: any[];
  allTeamsIsLoading: boolean
  retentionList: any[];
  retentionListIsLoading: boolean;
  teamsPayload: any;
  teamsMemberPayload: any;
  deleteMemberIsLoading: boolean,
  isTeamsExportLoading: boolean;
  exportTeamsStatus: any;
  teamIsLoading: boolean;
  teamMemberIsLoading: boolean;
  deletedUserList?: any;
  isDeletedUserListLoading: boolean;
  isDeletedUser: boolean;
  generalManagerList?: any[];
  generalManagerListIsLoading: boolean;
  usersByDesignation: any[];
  usersByDesignationIsLoading: boolean;
  excelUploadedList?: any;
  isExcelUploadedListLoading: boolean;
  bulkDeleteRoles: any;
  whatsappSetting: any;
  isAddGeoFencingLoading: boolean;
  isUpdateGeoFencingLoading: boolean;
  isGeoFencingListLoading: boolean;
  geoFencingList: any[];
  isUpdateUserSearchLoading: boolean;
  recentSearch: any;
  isRecentSearchLoading: boolean;
  rotationList: any;
  rotationListIsLoading: boolean;
  allAdmin: any;
  allAdminIsLoading: boolean;
};

const initialState: TeamsState = {
  isRolesLoading: true,
  rolesList: [],
  permissionsList: {},
  usersList: [],
  selectedRoleToUpdate: {},
  designationList: [],
  departmentsList: [],
  selectedUserToUpdate: {},
  userCount: {},
  usersListForReassignment: [],
  doesUserNameExists: false,
  doesEmailExists: false,
  doesPhoneNoExists: false,
  excelColumnHeading: {},
  userBasicDetails: {},
  userProfile: {},
  isUserProfileLoading: true,
  reportees: [],
  adminsAndReportees: [],
  onlyReportees: [],
  onlyReporteesWithInactive: [],
  onlyReporteesWithInactiveIsLoading: true,
  userAssignments: [],
  userData: {},
  bulkUserLeadCount: [],
  IVRSetting: [],
  IVRSettingById: [],
  IVRSettingByIdIsLoading: true,
  isLoading: true,
  filtersPayload: {
    Departments: null,
    Designations: null,
    UserIds: null,
    ReportsToIds: null,
    generalManagerIds: null,
    pageNumber: 1,
    pageSize: 10,
    path: 'user/getallusers',
    userSearch: null,
    userStatus: true,
    ShouldShowReportees: null,
    TimezoneIds: null
  },
  usersData: {},
  usersDataIsLoading: true,
  usersListForReassignmentIsLoading: true,
  adminsAndReporteesIsLoading: true,
  reporteesIsLoading: true,
  usersListIsLoading: true,
  designationListisLoading: true,
  departmentsListIsLoading: true,
  updateBulkUsersIsLoading: false,
  usersCountIsLoading: true,
  onlyAdmins: [],
  onlyAdminsIsLoading: true,
  exportUsersStatus: [],
  isExportStatusLoading: true,
  withoutAdmins: [],
  withoutAdminsIsLoading: true,
  managerDetails: [],
  allTeamLeads: [],
  allTeamMembers: {},
  totalTeamLeads: 0,
  unAssignedIsLoading: true,
  unassignedUser: [],
  allTeams: [],
  allTeamsIsLoading: true,
  retentionList: [],
  retentionListIsLoading: true,
  teamsPayload: [],
  teamsMemberPayload: [],
  deleteMemberIsLoading: true,
  isTeamsExportLoading: true,
  exportTeamsStatus: [],
  teamIsLoading: true,
  teamMemberIsLoading: true,
  deletedUserList: [],
  isDeletedUserListLoading: true,
  isDeletedUser: false,
  generalManagerList: [],
  generalManagerListIsLoading: true,
  usersByDesignation: [],
  usersByDesignationIsLoading: true,
  excelUploadedList: [],
  isExcelUploadedListLoading: true,
  bulkDeleteRoles: {},
  isBulkDeleteRolesLoading: true,
  whatsappSetting: [],
  isAddGeoFencingLoading: true,
  isUpdateGeoFencingLoading: true,
  isGeoFencingListLoading: true,
  geoFencingList: [],
  isUpdateUserSearchLoading: true,
  recentSearch: [],
  isRecentSearchLoading: true,
  rotationList: [],
  rotationListIsLoading: true,
  allAdmin: [],
  allAdminIsLoading: true,
};

export function teamsReducer(
  state: TeamsState = initialState,
  action: Action
): TeamsState {
  switch (action.type) {
    case TeamsActionTypes.FETCH_ALL_EXISTING_PERMISSIONS_SUCCESS:
      return {
        ...state,
        permissionsList: (action as FetchAllExistingPermissionSuccess).response,
      };
    case TeamsActionTypes.USER_EXCEL_UPLOAD_SUCCESS:
      return {
        ...state,
        excelColumnHeading: (action as UserExcelUploadSuccess).resp,
      };
    case TeamsActionTypes.FETCH_ROLES_LIST:
      return {
        ...state,
        isRolesLoading: true,
      };
    case TeamsActionTypes.FETCH_ROLES_LIST_SUCCESS:
      return {
        ...state,
        rolesList: (action as FetchRolesListSuccess).response || [],
        isRolesLoading: false,
      };
    case TeamsActionTypes.FETCH_USERS_LIST:
      return {
        ...state,
        usersListIsLoading: true,
        usersCountIsLoading: true,
      };
    case TeamsActionTypes.FETCH_USERS_LIST_SUCCESS:
      const response = (action as FetchUsersListSuccess).response;
      return {
        ...state,
        usersList: response.items,
        userCount: {
          itemsCount: response.itemsCount,
          totalCount: response.totalCount,
          usersData: response.data,
        },
        usersListIsLoading: false,
        usersCountIsLoading: false,
      };
    case TeamsActionTypes.DELETE_USER:
      return {
        ...state,
        usersListIsLoading: true,
      };
    case TeamsActionTypes.DELETE_USER_SUCCESS:
      return {
        ...state,
        usersListIsLoading: false,
      };
    case TeamsActionTypes.UPDATE_FILTER_PAYLOAD:
      return {
        ...state,
        filtersPayload: (action as UpdateFilterPayload).filter,
      };
    case TeamsActionTypes.FETCH_ROLES_PERMISSION_BY_ID_SUCCESS:
      return {
        ...state,
        selectedRoleToUpdate:
          (action as FetchRolePermissionByIdSuccess).response || {},
      };
    case TeamsActionTypes.FETCH_DESIGNATION_LIST:
      return {
        ...state,
        designationListisLoading: true,
      };
    case TeamsActionTypes.FETCH_DESIGNATION_LIST_SUCCESS:
      return {
        ...state,
        designationList: (action as FetchDesignationsListSuccess).response,
        designationListisLoading: false,
      };
    case TeamsActionTypes.FETCH_DEPARTMENT_LIST:
      return {
        ...state,
        departmentsListIsLoading: true,
      };
    case TeamsActionTypes.FETCH_DEPARTMENT_LIST_SUCCESS:
      return {
        ...state,
        departmentsList: (action as FetchDepartmentsListSuccess).response,
        departmentsListIsLoading: false,
      };
    case TeamsActionTypes.FETCH_USER_BY_ID:
      return {
        ...state,
        selectedUserToUpdate: {},
      };
    case TeamsActionTypes.FETCH_USER_BY_ID_SUCCESS:
      return {
        ...state,
        selectedUserToUpdate: (action as FetchUserByIdSuccess).response,
      };
    case TeamsActionTypes.FETCH_USER_PROFILE:
      return {
        ...state,
        isUserProfileLoading: true
      };
    case TeamsActionTypes.FETCH_USER_PROFILE_SUCCESS:
      return {
        ...state,
        userProfile: (action as FetchUserProfileSuccess).response,
        isUserProfileLoading: false
      };
    case TeamsActionTypes.FETCH_USERS_LIST_FOR_REASSIGNMENT:
      return {
        ...state,
        usersListForReassignmentIsLoading: true,
      };
    case TeamsActionTypes.FETCH_USERS_LIST_FOR_REASSIGNMENT_SUCCESS:
      return {
        ...state,
        usersListForReassignment: (
          action as FetchUsersListForReassignmentSuccess
        ).response,
        usersListForReassignmentIsLoading: false,
      };
    case TeamsActionTypes.DOES_USERNAME_EXISTS_SUCCESS:
      return {
        ...state,
        doesUserNameExists: (action as DoesUsrnameExistsSuccess).response,
      };
    case TeamsActionTypes.DOES_EMAIL_EXISTS_SUCCESS:
      return {
        ...state,
        doesEmailExists: (action as DoesEmailExistsSuccess).response,
      };
    case TeamsActionTypes.DOES_PHONENO_EXISTS_SUCCESS:
      return {
        ...state,
        doesPhoneNoExists: (action as DoesPhoneNoExistsSuccess).response,
      };
    case TeamsActionTypes.FETCH_USER_BASIC_DETAILS_BY_ID_SUCCESS:
      return {
        ...state,
        userBasicDetails: (action as FetchUserBasicDetailsByIdSuccess).response,
      };
    case TeamsActionTypes.FETCH_REPORTEES:
      return {
        ...state,
        reporteesIsLoading: true,
      };
    case TeamsActionTypes.FETCH_REPORTEES_SUCCESS:
      return {
        ...state,
        reporteesIsLoading: false,
        reportees: (action as FetchReporteesSuccess).response,
      };
    case TeamsActionTypes.FETCH_ADMINS_AND_REPORTEES:
      return {
        ...state,
        adminsAndReporteesIsLoading: true,
      };
    case TeamsActionTypes.FETCH_ADMINS_AND_REPORTEES_SUCCESS:
      return {
        ...state,
        adminsAndReportees: (action as FetchAdminsAndReporteesSuccess).response,
        adminsAndReporteesIsLoading: false,
      };
    case TeamsActionTypes.FETCH_ONLY_REPORTEES_SUCCESS:
      return {
        ...state,
        onlyReportees: (action as FetchOnlyReporteesSuccess).response,
      };
    case TeamsActionTypes.FETCH_ONLY_REPORTEES_WITH_INACTIVE:
      return {
        ...state,
        onlyReporteesWithInactiveIsLoading: true,
      };
    case TeamsActionTypes.FETCH_ONLY_REPORTEES_WITH_INACTIVE_SUCCESS:
      return {
        ...state,
        onlyReporteesWithInactive: (
          action as FetchOnlyReporteesWithInactiveSuccess
        ).response,
        onlyReporteesWithInactiveIsLoading: false,
      };
    case TeamsActionTypes.FETCH_USER_ASSIGNMENTS_BY_ID_SUCCESS:
      return {
        ...state,
        userAssignments: (action as FetchUserAssignmentsByIdSuccess).response,
      };
    case TeamsActionTypes.FETCH_BULK_USER_LEAD_COUNT_SUCCESS:
      return {
        ...state,
        bulkUserLeadCount: (action as FetchBulkUserLeadCountSuccess).response,
      };
    case TeamsActionTypes.UPDATE_BULK_USERS:
      return {
        ...state,
        updateBulkUsersIsLoading: true,
      };
    case TeamsActionTypes.UPDATE_BULK_USERS_SUCCESS:
      return {
        ...state,
        updateBulkUsersIsLoading: false,
      };
    case TeamsActionTypes.FETCH_IVR_SETTING_LIST_SUCCESS:
      return {
        ...state,
        IVRSetting: (action as FetchIVRSettingListSuccess).response,
      };
    case TeamsActionTypes.FETCH_IVR_SETTING_LIST_ID:
      return {
        ...state,
        IVRSettingByIdIsLoading: true,
      };
    case TeamsActionTypes.FETCH_IVR_SETTING_LIST_ID_SUCCESS:
      return {
        ...state,
        IVRSettingById: (action as FetchIVRSettingListByIdSuccess).response,
        IVRSettingByIdIsLoading: false,
      };
    case TeamsActionTypes.FETCH_ONLY_ADMINS:
      return {
        ...state,
        onlyAdminsIsLoading: true,
      };
    case TeamsActionTypes.FETCH_ONLY_ADMINS_SUCCESS:
      return {
        ...state,
        onlyAdmins: (action as FetchOnlyAdminsSuccess).response,
        onlyAdminsIsLoading: false,
      };
    case TeamsActionTypes.EXPORT_USERS_TRACKER:
      return {
        ...state,
        isExportStatusLoading: true
      };
    case TeamsActionTypes.EXPORT_USERS_TRACKER_SUCCESS:
      return {
        ...state,
        exportUsersStatus: (action as FetchUserExportTrackerSuccess).response,
        isExportStatusLoading: false
      };
    case TeamsActionTypes.FETCH_WITHOUT_ADMINS:
      return {
        ...state,
        withoutAdminsIsLoading: true,
      };
    case TeamsActionTypes.FETCH_WITHOUT_ADMINS_SUCCESS:
      return {
        ...state,
        withoutAdmins: (action as FetchWithoutAdminsSuccess).response,
        withoutAdminsIsLoading: false,
      };
    case TeamsActionTypes.FETCH_REPORTING_MANAGER_DETAILS_SUCCESS:
      return {
        ...state,
        managerDetails: (action as FetchReportingManagerDetailsSuccess).managerDetails,
      };
    case TeamsActionTypes.FETCH_TEAM_LIST:
      return {
        ...state,
        teamIsLoading: true,
      };
    case TeamsActionTypes.FETCH_TEAM_LIST_SUCCESS:
      return {
        ...state,
        allTeamLeads: (action as FetchTeamListSuccess).response,
        totalTeamLeads: (action as FetchTeamListSuccess).response,
        teamIsLoading: false,
      };
    case TeamsActionTypes.FETCH_TEAM_MEMBERS:
      return {
        ...state,
        teamMemberIsLoading: true,
      };
    case TeamsActionTypes.FETCH_TEAM_MEMBERS_SUCCESS:
      return {
        ...state,
        allTeamMembers: (action as FetchTeamMemberSuccess).response,
        teamMemberIsLoading: false,
      };
    case TeamsActionTypes.FETCH_UNASSIGNED_USERS:
      return {
        ...state,
        unAssignedIsLoading: true,
      };
    case TeamsActionTypes.FETCH_UNASSIGNED_USERS_SUCCESS:
      return {
        ...state,
        unassignedUser: (action as FetchUnassignedUserSuccess).response,
        unAssignedIsLoading: false,
      };
    case TeamsActionTypes.FETCH_ALL_TEAMS:
      return {
        ...state,
        allTeamsIsLoading: true,
      };
    case TeamsActionTypes.FETCH_ALL_TEAMS_SUCCESS:
      return {
        ...state,
        allTeams: (action as FetchAllTeamsSuccess).resp,
        allTeamsIsLoading: false,
      };
    case TeamsActionTypes.FETCH_RETENTION_LIST:
      return {
        ...state,
        retentionListIsLoading: true,
      };
    case TeamsActionTypes.FETCH_RETENTION_LIST_SUCCESS:
      return {
        ...state,
        retentionList: (action as FetchRetentionListSuccess).resp,
        retentionListIsLoading: false,
      };
    case TeamsActionTypes.TEAMS_FILTER_PAYLOAD:
      return {
        ...state,
        teamsPayload: (action as TeamsFilterPayload).payload,
      };
    case TeamsActionTypes.TEAMS_MEMBER_FILTER_PAYLOAD:
      return {
        ...state,
        teamsMemberPayload: (action as TeamsMemberFilterPayload).payload,
      };
    case TeamsActionTypes.DELETE_TEAM_MEMBER:
      return {
        ...state,
        deleteMemberIsLoading: true,
      };
    case TeamsActionTypes.DELETE_TEAM_MEMBER_SUCCESS:
      return {
        ...state,
        deleteMemberIsLoading: false,
      };
    case TeamsActionTypes.EXPORT_TEAMS_TRACKER:
      return {
        ...state,
        isTeamsExportLoading: true
      };
    case TeamsActionTypes.EXPORT_TEAMS_TRACKER_SUCCESS:
      return {
        ...state,
        exportTeamsStatus: (action as FetchTeamExportTrackerSuccess).response,
        isTeamsExportLoading: false
      };
    case TeamsActionTypes.FETCH_USER_ASSIGNED_DATA_BY_ID:
      return {
        ...state,
        usersDataIsLoading: true,
      };
    case TeamsActionTypes.FETCH_USER_ASSIGNED_DATA_BY_ID_SUCCESS:
      return {
        ...state,
        userData: (action as FetchUserAssignedDataByIdSuccess).response,
        usersDataIsLoading: false,
      };
    case TeamsActionTypes.FETCH_DELETED_USER_LIST:
      return {
        ...state,
        isDeletedUserListLoading: true,
      };
    case TeamsActionTypes.FETCH_DELETED_USER_LIST_SUCCESS:
      return {
        ...state,
        deletedUserList: (action as FetchDeletedUserListSuccess)
          .response,
        isDeletedUserListLoading: false,
      };
    case TeamsActionTypes.DELETE_ASSIGNED_USER:
      return {
        ...state,
        isDeletedUser: false,
      };
    case TeamsActionTypes.DELETE_ASSIGNED_USER_SUCCESS:
      return {
        ...state,
        isDeletedUser: true,
      };
    case TeamsActionTypes.FETCH_GENERAL_MANAGER_LIST:
      return {
        ...state,
        generalManagerListIsLoading: true,
      };
    case TeamsActionTypes.FETCH_GENERAL_MANAGER_LIST_SUCCESS:
      return {
        ...state,
        generalManagerList: (action as FetchGeneralManagerListSuccess).response,
        generalManagerListIsLoading: false,
      };
    case TeamsActionTypes.FETCH_USERS_BY_DESIGNATION:
      return {
        ...state,
        usersByDesignationIsLoading: true,
      };
    case TeamsActionTypes.FETCH_USERS_BY_DESIGNATION_SUCCESS:
      return {
        ...state,
        usersByDesignation: (action as FetchUsersByDesignationSuccess).response,
        usersByDesignationIsLoading: false,
      };
    case TeamsActionTypes.FETCH_EXCEL_UPLOADED_LIST:
      return {
        ...state,
        isExcelUploadedListLoading: true,
      };
    case TeamsActionTypes.FETCH_EXCEL_UPLOADED_LIST_SUCCESS:
      return {
        ...state,
        excelUploadedList: (action as FetchUserExcelUploadedSuccess).response,
        isExcelUploadedListLoading: false,
      };
    case TeamsActionTypes.DELETE_ROLES:
      return {
        ...state,
        isBulkDeleteRolesLoading: true,
      };
    case TeamsActionTypes.DELETE_ROLES_SUCCESS:
      return {
        ...state,
        bulkDeleteRoles: (action as DeleteRolesSuccess).response || {},
        isBulkDeleteRolesLoading: false
      };
    case TeamsActionTypes.FETCH_WHATSAPP_SETTING_LIST_SUCCESS:
      return {
        ...state,
        whatsappSetting: (action as FetchWhatsappSettingListSuccess).response,
      };
    case TeamsActionTypes.ADD_GEO_FENCING:
      return {
        ...state,
        isAddGeoFencingLoading: true,
      };
    case TeamsActionTypes.ADD_GEO_FENCING_SUCCESS:
      return {
        ...state,
        isAddGeoFencingLoading: false,
      };
    case TeamsActionTypes.UPDATE_GEO_FENCING:
      return {
        ...state,
        isUpdateGeoFencingLoading: true,
      };
    case TeamsActionTypes.UPDATE_GEO_FENCING_SUCCESS:
      return {
        ...state,
        isUpdateGeoFencingLoading: false,
      };
    case TeamsActionTypes.FETCH_GEO_FENCING_LIST:
      return {
        ...state,
        isGeoFencingListLoading: true,
      };
    case TeamsActionTypes.FETCH_GEO_FENCING_LIST_SUCCESS:
      return {
        ...state,
        geoFencingList: (action as FetchGeoFencingListSuccess).response,
        isGeoFencingListLoading: false,
      };
    case TeamsActionTypes.UPDATE_USER_SEARCH:
      return {
        ...state,
        isUpdateUserSearchLoading: true,
      };
    case TeamsActionTypes.UPDATE_USER_SEARCH_SUCCESS:
      return {
        ...state,
        isUpdateUserSearchLoading: false,
      };
    case TeamsActionTypes.FETCH_RECENT_SEARCH:
      return {
        ...state,
        isRecentSearchLoading: true,
      };
    case TeamsActionTypes.FETCH_RECENT_SEARCH_SUCCESS:
      return {
        ...state,
        recentSearch: (action as FetchRecentSearchSuccess).response,
        isRecentSearchLoading: false,
      };
    case TeamsActionTypes.FETCH_ROTATION_LIST:
      return {
        ...state,
        rotationListIsLoading: true,
      };
    case TeamsActionTypes.FETCH_ROTATION_LIST_SUCCESS:
      return {
        ...state,
        rotationList: (action as FetchRotationListSuccess).response,
        rotationListIsLoading: false,
      };
    case TeamsActionTypes.FETCH_ALL_ADMIN:
      return {
        ...state,
        allAdminIsLoading: true,
      };
    case TeamsActionTypes.FETCH_ALL_ADMIN_SUCCESS:
      return {
        ...state,
        allAdmin: (action as FetchAllAdminSuccess).response,
        allAdminIsLoading: false,
      };
    default:
      return state;
  }
}

export const selectFeature = (state: AppState) => state.teams;

export const getAllPermissions = createSelector(
  selectFeature,
  (state: TeamsState) => state.permissionsList
);
export const getRolesList = createSelector(
  selectFeature,
  (state: TeamsState) => state.rolesList
);
export const getUsersList = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersList
);
export const getFiltersPayload = createSelector(
  selectFeature,
  (state: TeamsState) => state.filtersPayload
);
export const getUsersListForReassignment = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersListForReassignment
);
export const getUsersListForReassignmentIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersListForReassignmentIsLoading
);
export const getDesignationsList = createSelector(
  selectFeature,
  (state: TeamsState) => state.designationList
);
export const getDesignationsListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.designationListisLoading
);
export const getDepartmentsList = createSelector(
  selectFeature,
  (state: TeamsState) => state.departmentsList
);
export const getDepartmentsListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.departmentsListIsLoading
);
export const getSelectedRole = createSelector(
  selectFeature,
  (state: TeamsState) => state.selectedRoleToUpdate
);
export const getSelectedUser = createSelector(
  selectFeature,
  (state: TeamsState) => state.selectedUserToUpdate
);
export const getUserBasicDetails = createSelector(
  selectFeature,
  (state: TeamsState) => state.userBasicDetails
);
export const getSelectedUserDocuments = createSelector(
  selectFeature,
  (state: TeamsState) => state.userProfile?.documents
);
export const getUserAssignments = createSelector(
  selectFeature,
  (state: TeamsState) => state.userAssignments
);
export const getUsersCount = createSelector(
  selectFeature,
  (state: TeamsState) => state.userCount
);
export const getUsersCountIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersCountIsLoading
);
export const doesUserNameExists = createSelector(
  selectFeature,
  (state: TeamsState) => state.doesUserNameExists
);
export const doesEmailExists = createSelector(
  selectFeature,
  (state: TeamsState) => state.doesEmailExists
);
export const doesPhoneNoxists = createSelector(
  selectFeature,
  (state: TeamsState) => state.doesPhoneNoExists
);
export const getDuplicateUsers = createSelector(
  selectFeature,
  (state: TeamsState) => state.excelColumnHeading
);
export const getUserProfile = createSelector(
  selectFeature,
  (state: TeamsState) => state.userProfile
);
export const getReportees = createSelector(
  selectFeature,
  (state: TeamsState) => state.reportees
);
export const getAdminsAndReportees = createSelector(
  selectFeature,
  (state: TeamsState) => state.adminsAndReportees
);

export const getAdminsAndReporteesIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.adminsAndReporteesIsLoading
);

export const getOnlyReportees = createSelector(
  selectFeature,
  (state: TeamsState) => state.onlyReportees
);

export const getOnlyReporteesWithInactive = createSelector(
  selectFeature,
  (state: TeamsState) => state.onlyReporteesWithInactive
);

export const getOnlyReporteesWithInactiveIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.onlyReporteesWithInactiveIsLoading
);

export const getReporteesIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.reporteesIsLoading
);
export const getBulkUserLeadCount = createSelector(
  selectFeature,
  (state: TeamsState) => state.bulkUserLeadCount
);
export const getUsersListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersListIsLoading
);
export const getUpdateBulkUsersIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.updateBulkUsersIsLoading
);
export const getIVRSetting = createSelector(
  selectFeature,
  (state: TeamsState) => state.IVRSetting
);
export const getIVRSettingById = createSelector(
  selectFeature,
  (state: TeamsState) => state.IVRSettingById
);
export const getIVRSettingByIdIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.IVRSettingByIdIsLoading
);
export const getOnlyAdmins = createSelector(
  selectFeature,
  (state: TeamsState) => {
    return {
      items: state.onlyAdmins,
      isLoading: state.onlyAdminsIsLoading,
    };
  }
);

export const getUserExportStatus = createSelector(
  selectFeature,
  (state: TeamsState) => {
    return {
      items: state.exportUsersStatus.items,
      totalCount: state.exportUsersStatus.totalCount
    };
  }
);

export const getUserExportStatusLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isExportStatusLoading
);

export const getWithoutAdmins = createSelector(
  selectFeature,
  (state: TeamsState) => {
    return {
      items: state.withoutAdmins,
      isLoading: state.withoutAdminsIsLoading,
    };
  }
);

export const getManagerDetails = createSelector(
  selectFeature,
  (state: TeamsState) => state.managerDetails
);

export const getUserAssignData = createSelector(
  selectFeature,
  (state: TeamsState) => state.userData
);

export const getUserAssignDataIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersDataIsLoading
);

export const getIsRolesLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isRolesLoading
);

export const getAllTeams = createSelector(
  selectFeature,
  (state: TeamsState) => state.allTeamLeads
);

export const getAllTeamsIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.teamIsLoading
);

export const getAllTeamMembers = createSelector(
  selectFeature,
  (state: TeamsState) => state.allTeamMembers
);

export const getAllTeamMembersIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.teamMemberIsLoading
);

export const getUnAssigneduser = createSelector(
  selectFeature,
  (state: TeamsState) => state.unassignedUser
);

export const getUnAssigneduserIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.unAssignedIsLoading
);

export const getAllTeamsList = createSelector(
  selectFeature,
  (state: TeamsState) => state.allTeams
);

export const getAllTeamsListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.allTeamsIsLoading
);

export const getRetentionList = createSelector(
  selectFeature,
  (state: TeamsState) => state.retentionList
);

export const getRetentionListisLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.retentionListIsLoading
);

export const getTeamsFilterPayload = createSelector(
  selectFeature,
  (state: TeamsState) => state.teamsPayload
);

export const getTeamsMemberFilterPayload = createSelector(
  selectFeature,
  (state: TeamsState) => state.teamsMemberPayload
);

export const getBulkDeleteMemberIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.deleteMemberIsLoading
);

export const getTeamExportStatus = createSelector(
  selectFeature,
  (state: TeamsState) => {
    return {
      items: state.exportTeamsStatus.items,
      totalCount: state.exportTeamsStatus.totalCount
    };
  }
);

export const getTeamExportStatusLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isTeamsExportLoading
);

export const getUserDeletedList = createSelector(
  selectFeature,
  (state: TeamsState) => state.deletedUserList
);

export const getUserDeletedListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isDeletedUserListLoading
);

export const getIsUserDeleted = createSelector(
  selectFeature,
  (state: TeamsState) => state.isDeletedUser
);

export const getGeneralManagerList = createSelector(
  selectFeature,
  (state: TeamsState) => state.generalManagerList
);

export const getGeneralManagerListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.generalManagerListIsLoading
);

export const getUsersByDesignation = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersByDesignation
);

export const getUsersByDesignationIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.usersByDesignationIsLoading
);

export const getUserExcelUploadedList = createSelector(
  selectFeature,
  (state: TeamsState) => state.excelUploadedList
);

export const getUserExcelUploadedListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isExcelUploadedListLoading
);

export const IsBulkDeleteRolesLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isBulkDeleteRolesLoading
);

export const bulkDeleteRoles = createSelector(
  selectFeature,
  (state: TeamsState) => state.bulkDeleteRoles
);

export const getWhatsappSetting = createSelector(
  selectFeature,
  (state: TeamsState) => state.whatsappSetting
);

export const getIsAddGeoFencingLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isAddGeoFencingLoading
);

export const getIsUpdateGeoFencingLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isUpdateGeoFencingLoading
);

export const getGeoFencingList = createSelector(
  selectFeature,
  (state: TeamsState) => state.geoFencingList
);

export const getGeoFencingListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isGeoFencingListLoading
)

export const getIsUpdateUserSearchLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isUpdateUserSearchLoading
);

export const getRecentSearch = createSelector(
  selectFeature,
  (state: TeamsState) => state.recentSearch
);

export const getIsRecentSearchLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.isRecentSearchLoading
);

export const getRotationList = createSelector(
  selectFeature,
  (state: TeamsState) => state.rotationList
);

export const getRotationListIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.rotationListIsLoading
);

export const getAllAdmin = createSelector(
  selectFeature,
  (state: TeamsState) => state.allAdmin
);

export const getAllAdminIsLoading = createSelector(
  selectFeature,
  (state: TeamsState) => state.allAdminIsLoading
);

