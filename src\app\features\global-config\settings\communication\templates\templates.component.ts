import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import {
  <PERSON><PERSON>anitizer,
  SafeResourceUrl,
  Title,
} from '@angular/platform-browser';
import { Store } from '@ngrx/store';
import { firstValueFrom, skipWhile } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getTenantName } from 'src/app/core/utils/common.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { ShareDataService } from 'src/app/services/shared/share-data.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'templates',
  templateUrl: './templates.component.html',
})
export class TemplatesComponent implements OnInit {
  public modules: Array<object> = [
    { title: 'Lead Templates', link: 'lead-templates' },
    { title: 'Property Templates', link: 'property-templates' },
    { title: 'Project Templates', link: 'project-templates' },
    { title: 'Project Unit Templates', link: 'project-unit-templates' },
  ];
  showLeftNav: boolean = true;
  selectedSection: string = 'Lead';
  @ViewChild('iframeElement') iframeElement: ElementRef<HTMLIFrameElement>;
  engageToUrl: string = environment.engageToURL;
  safeUrl: SafeResourceUrl;
  globalSettingsData: any;

  constructor(
    private store: Store<AppState>,
    private shareDataService: ShareDataService,
    private headerTitle: HeaderTitleService,
    public metaTitle: Title,
    private sanitizer: DomSanitizer
  ) {
    this.metaTitle.setTitle('CRM | Global Config | Templates');
    this.headerTitle.setLangTitle('BULK_LEAD.templates');
    this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(
      this.engageToUrl + 'templates-all'
    );
    // this.store.select(getPermissions).subscribe((permissions: any) => {
    //   if (permissions?.includes('Permissions.Templates.View')) {
    //     this.canTemplatesView = true;
    //   }
    // });
  }

  async ngOnInit() {
    this.globalSettingsData = await firstValueFrom(this.store.select(getGlobalSettingsAnonymous).pipe(skipWhile((res: any) => !Object.keys(res).length)));
    this.shareDataService.showLeftNav$.subscribe((show) => {
      this.showLeftNav = show;
    });
  }

  onIframeLoad() {
    if (this.iframeElement?.nativeElement) {
      const iframe = this.iframeElement.nativeElement;
      iframe.contentWindow?.postMessage(
        {
          tenantId: getTenantName(),
        },
        '*'
      );
    }
  }
}
