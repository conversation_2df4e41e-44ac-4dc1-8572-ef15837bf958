import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { NotificationsService } from 'angular2-notifications';
import { BsModalService } from 'ngx-bootstrap/modal';
import { EMPTY, of } from 'rxjs';
import {
  catchError,
  map,
  switchMap,
} from 'rxjs/operators';
import { OnError } from 'src/app/app.actions';
import { AppState } from 'src/app/app.reducer';
import { CustomFormService } from 'src/app/services/controllers/custom-form.service';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { AddCustomForm, AddCustomFormSuccess, CustomFormActionTypes, DeleteCustomForm, DeleteCustomFormSuccess, FetchCustomForm, FetchCustomFormField, FetchCustomFormFieldExist, FetchCustomFormFieldExistSuccess, FetchCustomFormFieldSuccess, FetchCustomFormSuccess } from './custom-form.action';

@Injectable()
export class CustomFormEffects {
  addProjectsToLeads$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.ADD_CUSTOMFORM),
      switchMap((action: AddCustomForm) => {
        return this.api.addCustomForm(action?.payload).pipe(
          map((resp: any) => {
            this._notificationService.success('Fields added Successfully')
            return new AddCustomFormSuccess(), new FetchCustomForm();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomForm$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.FETCH_CUSTOMFORM),
      map((action: FetchCustomForm) => action),
      switchMap((data: any) => {
        return this.api.getCustomForm().pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCustomFormSuccess(resp.items);
            }
            return new FetchCustomFormSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomFormFieldExist$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD_EXIST),
      switchMap((action: FetchCustomFormFieldExist) => {
        return this.api.getCustomFieldExist(action?.payload).pipe(
          map((resp: any) => {
            return new FetchCustomFormFieldExistSuccess(resp.data);
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  getCustomFormFields$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.FETCH_CUSTOMFORM_FIELD),
      map((action: FetchCustomFormField) => action),
      switchMap((data: any) => {
        return this.api.getCustomFormField(data.id).pipe(
          map((resp: any) => {
            if (resp.succeeded) {
              return new FetchCustomFormFieldSuccess(resp.data);
            }
            return new FetchCustomFormFieldSuccess();
          }),
          catchError((err) => of(new OnError(err)))
        );
      })
    )
  );

  deleteCustomForm$ = createEffect(() =>
    this.actions$.pipe(
      ofType(CustomFormActionTypes.DELETE_CUSTOMFORM),
      map((action: DeleteCustomForm) => action),
      switchMap((data: any) =>
        this.api.deleteCustomForm(data.id).pipe(
          switchMap((resp: any) => {
            if (resp.succeeded) {
              this._notificationService.success('Deleted Successfully');
              return of(new DeleteCustomFormSuccess(), new FetchCustomForm());
            }
            return of(new DeleteCustomFormSuccess());
          }),
          catchError((err: any) => {
            let initialState: any = {
              type: 'customForm', data: {
                fieldType: 'Warning',
                heading: 'Field cannot be deleted',
                message: `Field cannot be deleted as it has associated data`,
              },
            };
            this.modalService.show(UserAlertPopupComponent, {
              class: 'modal-400 modal-dialog-centered ph-modal-unset',
              initialState,
            });
            this.store.dispatch(new FetchCustomForm())
            return EMPTY;
          }))
      )
    )
  );

  constructor(
    private actions$: Actions,
    private api: CustomFormService,
    private _notificationService: NotificationsService,
    private store: Store<AppState>,
    public modalService: BsModalService

  ) { }
}