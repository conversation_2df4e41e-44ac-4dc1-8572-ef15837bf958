.modal-header {
  border-bottom: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  
  .modal-title {
    font-weight: 600;
    color: #333;
  }
  
  .btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    
    &:hover {
      color: #333;
    }
  }
}

.modal-body {
  padding: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
  
  .form-group {
    margin-bottom: 1.5rem;
    
    .form-label {
      margin-bottom: 0.5rem;
      color: #333;
      font-size: 0.9rem;
    }
    
    .form-control, .form-select {
      border: 1px solid #ddd;
      border-radius: 0.375rem;
      padding: 0.5rem 0.75rem;
      font-size: 0.9rem;
      
      &:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }
      
      &.is-invalid {
        border-color: #dc3545;
      }
    }
    
    .form-text {
      font-size: 0.8rem;
      margin-top: 0.25rem;
    }
  }
  
  .form-check-input {
    &:checked {
      background-color: #0d6efd;
      border-color: #0d6efd;
    }
  }
  
  .input-group {
    .btn {
      border-left: none;
    }
  }
  
  .card {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    
    .card-header {
      background-color: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      padding: 0.75rem 1rem;
      
      h6 {
        color: #495057;
        font-weight: 600;
      }
    }
    
    .card-body {
      padding: 1rem;
    }
  }
  
  .bg-light {
    background-color: #f8f9fa !important;
    border: 1px solid #e9ecef;
    
    h6 {
      color: #495057;
      margin-bottom: 0.5rem;
    }
    
    p {
      margin-bottom: 0.75rem;
    }
  }
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 0.375rem;
    
    &.btn-primary {
      background-color: #0d6efd;
      border-color: #0d6efd;
      
      &:hover {
        background-color: #0b5ed7;
        border-color: #0a58ca;
      }
      
      &:disabled {
        background-color: #6c757d;
        border-color: #6c757d;
        opacity: 0.65;
      }
    }
    
    &.btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      
      &:hover {
        background-color: #5c636a;
        border-color: #565e64;
      }
    }
    
    &.btn-outline-secondary {
      color: #6c757d;
      border-color: #6c757d;
      
      &:hover {
        background-color: #6c757d;
        color: #fff;
      }
    }
    
    &.btn-outline-info {
      color: #0dcaf0;
      border-color: #0dcaf0;
      
      &:hover {
        background-color: #0dcaf0;
        color: #000;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .modal-body {
    padding: 1rem;
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      
      .form-check {
        margin-top: 0.5rem;
      }
    }
  }
  
  .modal-footer {
    padding: 1rem;
    flex-direction: column;
    
    .btn {
      width: 100%;
      margin-bottom: 0.5rem;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Animation for form switches
.form-check-input {
  transition: all 0.2s ease-in-out;
}

// Validation styles
.is-invalid {
  .form-control, .form-select {
    border-color: #dc3545;
    
    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
}

.invalid-feedback {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}
