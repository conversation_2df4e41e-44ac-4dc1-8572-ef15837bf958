import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { SecurityConfigService, SecurityConfig } from 'src/app/core/services/security-config.service';
import { ClickjackingPreventionService } from 'src/app/core/services/clickjacking-prevention.service';

@Component({
  selector: 'app-clickjacking-settings',
  templateUrl: './clickjacking-settings.component.html',
  styleUrls: ['./clickjacking-settings.component.scss']
})
export class ClickjackingSettingsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  clickjackingForm: FormGroup;
  securityConfig: SecurityConfig;
  
  frameOptionsValues = [
    { value: 'DENY', label: 'DENY - Prevent all framing' },
    { value: 'SAMEORIGIN', label: 'SAMEORIGIN - Allow same origin framing' }
  ];

  constructor(
    private fb: FormBuilder,
    private securityConfigService: SecurityConfigService,
    private clickjackingService: ClickjackingPreventionService,
    private modalRef: BsModalRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadCurrentConfig();
    this.subscribeToConfigChanges();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.clickjackingForm = this.fb.group({
      enabled: [true],
      frameOptions: ['DENY', Validators.required],
      allowedOrigins: this.fb.array([]),
      continuousMonitoring: [true],
      monitoringInterval: [2000, [Validators.required, Validators.min(1000)]],
      cspEnabled: [true],
      xContentTypeOptions: [true],
      xXSSProtection: [true],
      referrerPolicy: ['strict-origin-when-cross-origin']
    });
  }

  private loadCurrentConfig(): void {
    this.securityConfig = this.securityConfigService.getConfig();
    this.patchFormValues();
  }

  private subscribeToConfigChanges(): void {
    this.securityConfigService.config$
      .pipe(takeUntil(this.destroy$))
      .subscribe(config => {
        this.securityConfig = config;
        this.patchFormValues();
      });
  }

  private patchFormValues(): void {
    const config = this.securityConfig;
    
    this.clickjackingForm.patchValue({
      enabled: config.clickjackingProtection.enabled,
      frameOptions: config.clickjackingProtection.frameOptions,
      continuousMonitoring: config.clickjackingProtection.continuousMonitoring,
      monitoringInterval: config.clickjackingProtection.monitoringInterval,
      cspEnabled: config.contentSecurityPolicy.enabled,
      xContentTypeOptions: config.additionalHeaders.xContentTypeOptions,
      xXSSProtection: config.additionalHeaders.xXSSProtection,
      referrerPolicy: config.additionalHeaders.referrerPolicy
    });

    // Update allowed origins form array
    this.updateAllowedOriginsArray(config.clickjackingProtection.allowedOrigins);
  }

  private updateAllowedOriginsArray(origins: string[]): void {
    const originsArray = this.clickjackingForm.get('allowedOrigins') as FormArray;
    originsArray.clear();
    
    origins.forEach(origin => {
      originsArray.push(this.fb.control(origin, Validators.required));
    });
  }

  get allowedOriginsArray(): FormArray {
    return this.clickjackingForm.get('allowedOrigins') as FormArray;
  }

  addAllowedOrigin(): void {
    this.allowedOriginsArray.push(this.fb.control('', Validators.required));
  }

  removeAllowedOrigin(index: number): void {
    this.allowedOriginsArray.removeAt(index);
  }

  onFrameOptionsChange(): void {
    const frameOptions = this.clickjackingForm.get('frameOptions')?.value;
    if (frameOptions === 'DENY') {
      // Clear allowed origins when DENY is selected
      this.allowedOriginsArray.clear();
    }
  }

  testClickjackingProtection(): void {
    // Test if the current page is being framed
    const isFramed = this.clickjackingService.isFramed();
    if (isFramed) {
      alert('Warning: This page appears to be framed! Clickjacking protection should prevent this.');
    } else {
      alert('Good: This page is not being framed.');
    }
  }

  onSave(): void {
    if (this.clickjackingForm.valid) {
      const formValue = this.clickjackingForm.value;
      
      const updatedConfig: Partial<SecurityConfig> = {
        clickjackingProtection: {
          enabled: formValue.enabled,
          frameOptions: formValue.frameOptions,
          allowedOrigins: formValue.allowedOrigins.filter((origin: string) => origin.trim()),
          continuousMonitoring: formValue.continuousMonitoring,
          monitoringInterval: formValue.monitoringInterval
        },
        contentSecurityPolicy: {
          enabled: formValue.cspEnabled,
          frameAncestors: formValue.frameOptions === 'DENY' ? "'none'" : "'self'",
          customDirectives: this.securityConfig.contentSecurityPolicy.customDirectives
        },
        additionalHeaders: {
          xContentTypeOptions: formValue.xContentTypeOptions,
          xXSSProtection: formValue.xXSSProtection,
          referrerPolicy: formValue.referrerPolicy
        }
      };

      this.securityConfigService.updateConfig(updatedConfig);
      
      // Apply changes immediately
      if (formValue.enabled) {
        this.clickjackingService.enableProtection();
      } else {
        this.clickjackingService.disableProtection();
      }

      this.modalRef.hide();
    }
  }

  onCancel(): void {
    this.modalRef.hide();
  }

  resetToDefaults(): void {
    if (confirm('Are you sure you want to reset all security settings to defaults?')) {
      this.securityConfigService.resetToDefaults();
    }
  }
}
