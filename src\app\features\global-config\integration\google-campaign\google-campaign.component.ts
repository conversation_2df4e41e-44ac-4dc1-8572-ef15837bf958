import { ChangeDetectorR<PERSON>, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { GridApi, GridOptions } from 'ag-grid-community';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

import { DATE_FILTER_LIST } from 'src/app/app.constants';
import { DateRange } from 'src/app/app.enum';
import { AppState } from 'src/app/app.reducer';
import {
  getDateRange,
  getSystemTimeOffset,
  getSystemTimeZoneId,
  getTenantName,
  onFilterChanged,
  onPickerOpened,
  setTimeZoneDate,
  validateAllFormFields,
} from 'src/app/core/utils/common.util';
import {
  DeleteGoogleCampaignAccount,
  FetchGoogleCampaignAccounts,
} from 'src/app/reducers/Integration/integration.actions';
import {
  getGoogleCampaignAccounts,
  getGoogleCampaignAccountsIsLoading,
} from 'src/app/reducers/Integration/integration.reducer';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { CommonService } from 'src/app/services/shared/common.service';
import { GridOptionsService } from 'src/app/services/shared/grid-options.service';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';
import { UserConfirmationComponent } from 'src/app/shared/components/user-confirmation/user-confirmation.component';
import { environment as env } from 'src/environments/environment';

interface GoogleAd {
  adId?: string;
  adName?: string;
  adSetName?: string;
  adSetId?: string;
  leadsCount?: number;
  countryCode?: string;
  currencyCode?: string;
  status?: string;
}
interface GoogleCampaign {
  campaignId?: string;
  campaignName?: string;
  totalCost?: number;
  totalClickCost?: number;
  noLeadsCount?: number;
  costPerLead?: number;
  roi?: number;
  costPerClick?: number;
  currencyCode?: string;
  status?: string;
  [key: string]: any; // For additional properties
}

@Component({
  selector: 'app-google-campaign',
  templateUrl: './google-campaign.component.html',
})
export class GoogleCampaignComponent implements OnInit, OnDestroy {
  private stopper = new Subject<void>();
  accounts: any[] = [];
  filteredAccounts: any[] = [];
  allAccounts: any[] = [];
  isAccountsLoading = false;
  // Data for ads and campaigns
  adsData: any[] = [];
  isAdsLoading = false;
  campaignsData: any[] = [];
  isCampaignsLoading = false;
  campaignsMarketingData: any[] = [];
  isCampaignsMarketingLoading = false;
  // Selected accounts for tracking
  selectedAccountForCampaigns: any;
  accountsPageNumber = 1;
  accountsPageSize = 10;
  leadSource = 44;

  userBasicDetails: any;
  currentDate = new Date();
  modalRef: BsModalRef;
  showLeftNav = false;
  gridApi: GridApi;
  gridApiCampaigns: GridApi;
  gridOptions: GridOptions;
  gridOptionsCampaigns: GridOptions;
  // Pagination
  adsPageNumber = 1;
  adsPageSize = 10;
  adsTotalCount = 0;
  campaignsPageNumber = 1;
  campaignsPageSize = 10;
  campaignsTotalCount = 0;
  showEntriesSize = [10, 25, 50, 100];
  // Search
  searchTerm = '';
  campaignsSearchTerm = '';
  // Date Filter
  cplTrackForm: FormGroup;
  showCplFilter = false;
  campaignsCplTrackForm: FormGroup;
  showCampaignsCplFilter = false;
  cplDateFilterList = DATE_FILTER_LIST?.filter(
    (item: any) => item?.value !== 'TillDate'
  );
  // Show Filters
  showFilters = false;
  appliedFilter: any = {};
  isCopyPasteEnabled = false;
  // Permissions
  canAdd = true;
  canDelete = true;
  canView = true;
  // Utility functions
  onFilterChanged = onFilterChanged;
  onPickerOpened = onPickerOpened;

  constructor(
    private store: Store<AppState>,
    private modalService: BsModalService,
    private _notificationService: NotificationsService,
    private cdr: ChangeDetectorRef,
    private formBuilder: FormBuilder,
    private headerTitle: HeaderTitleService,
    private commonService: CommonService,
    private gridOptionsService: GridOptionsService
  ) { }

  ngOnInit(): void {
    this.headerTitle.setTitle('Google Campaign Integration');
    this.initializeComponent();
    this.initializeGridOptions();
    this.initializeDateFilter();
    this.loadAccounts();
    this.subscribeToStore();
  }

  ngOnDestroy(): void {
    this.stopper.next();
    this.stopper.complete();
  }

  initializeComponent(): void {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((settings: any) => {
        this.userBasicDetails = settings;
        this.isCopyPasteEnabled = settings?.isCopyPasteEnabled || false;
        this.refreshGridColumns();
      });
  }

  subscribeToStore(): void {
    this.store
      .select(getGoogleCampaignAccounts)
      .pipe(takeUntil(this.stopper))
      .subscribe((accounts: any[]) => {
        if (accounts && accounts.length >= 0) {
          this.accounts = accounts.map((account) => ({
            id: account.id,
            accountName: account.accountName,
            customerId: account.customerId,
            leadCount: account.leadCount || 0,
            status: account.status,
            isAdsExpanded: false,
            isAdsLoading: false,
            isCampaignsExpanded: false,
            isCampaignsLoading: false,
            adsPageNumber: 1,
            adsPageSize: 10,
            adsTotalCount: 0,
            campaignsPageNumber: 1,
            campaignsPageSize: 10,
            campaignsTotalCount: 0,
            ads: [] as GoogleAd[],
            paginatedAds: [] as GoogleAd[],
            campaigns: [] as GoogleCampaign[],
            paginatedCampaigns: [] as GoogleCampaign[],
            adsLoaded: false,
            campaignsLoaded: false,
          }));
          this.filteredAccounts = [...this.accounts];
          this.allAccounts = [...this.accounts];
        }
      });

    this.store
      .select(getGoogleCampaignAccountsIsLoading)
      .pipe(takeUntil(this.stopper))
      .subscribe((isLoading: boolean) => {
        this.isAccountsLoading = isLoading;
      });
  }

  initializeGridOptions(): void {
    this.gridOptions = this.gridOptionsService.getGridSettings(this);
    this.gridOptions.rowHeight = 60;
    this.gridOptions.columnDefs = [
      {
        headerName: 'Ad Name',
        field: 'Ad Name',
        minWidth: 150,
        valueGetter: (params) => params.data?.adName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Ad Set Name',
        field: 'Ad Set Name',
        minWidth: 120,
        valueGetter: (params) => params.data?.adSetName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Ad Set ID',
        field: 'Ad Set ID',
        minWidth: 100,
        valueGetter: (params) => params.data?.adSetId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Lead Count',
        field: 'Lead Count',
        minWidth: 100,
        valueGetter: (params) => params.data?.leadsCount,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || 0}</p>`;
        },
      },
      {
        headerName: 'Country',
        field: 'Country',
        minWidth: 100,
        valueGetter: (params) => params.data?.countryCode,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Currency',
        field: 'Currency',
        minWidth: 100,
        valueGetter: (params) => params.data?.currencyCode,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 120,
        valueGetter: (params) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status || '--'}</p>`;
        },
      },
    ];

    this.gridOptions.context = {
      componentParent: this,
      componentType: 'ads',
    };

    this.gridOptionsCampaigns = this.gridOptionsService.getGridSettings(this);
    this.gridOptionsCampaigns.rowHeight = 60;
    this.gridOptionsCampaigns.suppressRowClickSelection =
      this.isCopyPasteEnabled;
    this.gridOptionsCampaigns.enableCellTextSelection = this.isCopyPasteEnabled;
    this.gridOptionsCampaigns.columnDefs = [
      {
        headerName: 'Campaign Name',
        field: 'Campaign Name',
        minWidth: 150,
        valueGetter: (params) => params.data?.campaignName,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-2">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Campaign ID',
        field: 'Campaign ID',
        minWidth: 120,
        valueGetter: (params) => params.data?.campaignId,
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Status',
        field: 'Status',
        minWidth: 120,
        valueGetter: (params) => params.data?.status,
        cellRenderer: (params: any) => {
          const status = params.value;
          const classes = this.getStatusClasses(status);
          return `<p class="${classes}">${status || '--'}</p>`;
        },
      },
      {
        headerName: 'Total Cost',
        field: 'Total Cost',
        minWidth: 120,
        valueGetter: (params) => {
          const cost = params.data?.cost;
          const currency = params.data?.currencyCode;
          return cost !== null && cost !== undefined && cost !== 0
            ? currency + ' ' + cost.toFixed(2)
            : '--';
        },
        cellRenderer: (params: any) => {
          if (this.isCampaignsMarketingLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Leads Count',
        field: 'Leads Count',
        minWidth: 120,
        valueGetter: (params) => params.data?.leadCount,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Google Ads Leads Count',
        field: 'Google Ads Leads Count',
        minWidth: 120,
        valueGetter: (params) => params.data?.googleadsleadscount,
        cellRenderer: (params: any) => {
          return `<p>${params?.value || '--'}</p>`;
        },
      },
      {
        headerName: 'Cost Per Lead',
        field: 'Cost Per Lead',
        minWidth: 120,
        valueGetter: (params) => {
          const cpl = params.data?.costPerLead;
          const currency = params.data?.currencyCode;
          return cpl !== null && cpl !== undefined && cpl !== 0
            ? currency + ' ' + cpl.toFixed(2)
            : '--';
        },
        cellRenderer: (params: any) => {
          if (this.isCampaignsMarketingLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'ROI',
        field: 'ROI',
        minWidth: 100,
        valueGetter: (params) => {
          const roi = params.data?.roiPercentage;
          return roi ? roi : '--';
        },
        cellRenderer: (params: any) => {
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Cost Per Click',
        field: 'Cost Per Click',
        minWidth: 120,
        valueGetter: (params) => {
          const cpc = params.data?.averageCpc;
          const currency = params.data?.currencyCode;
          return cpc !== null && cpc !== undefined && cpc !== 0
            ? currency + ' ' + cpc.toFixed(2)
            : '--';
        },
        cellRenderer: (params: any) => {
          if (this.isCampaignsMarketingLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'Total Revenue',
        field: 'Total Revenue',
        minWidth: 120,
        valueGetter: (params) => {
          const cpc = params.data?.totalRevenue;
          const currency = params.data?.currencyCode;
          return cpc !== null && cpc !== undefined && cpc !== 0
            ? currency + ' ' + cpc.toFixed(2)
            : '--';
        },
        cellRenderer: (params: any) => {
          if (this.isCampaignsMarketingLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
      {
        headerName: 'No Of Clicks',
        field: 'No Of Clicks',
        minWidth: 120,
        valueGetter: (params) => {
          const cpc = params.data?.clicks;
          return cpc ? cpc : '--';
        },
        cellRenderer: (params: any) => {
          if (this.isCampaignsMarketingLoading) {
            return `<div class="px-10"><ng-container *ngFor="let dot of [1,2,3]">
            <div class="dot-elastic"></div>
          </ng-container></div>`;
          }
          return `<p class="text-truncate-1">${params?.value}</p>`;
        },
      },
    ];

    this.gridOptionsCampaigns.context = {
      componentParent: this,
      componentType: 'campaigns',
    };
  }

  initializeDateFilter(): void {
    this.cplTrackForm = this.formBuilder.group({
      cplTrackRange: ['Today'],
      cplTrackDate: [null],
    });

    this.campaignsCplTrackForm = this.formBuilder.group({
      cplTrackRange: ['Today'],
      cplTrackDate: [null],
    });
    this.updateAppliedFilter();
  }

  login() {
    if (this.canAdd) {
      const idToken = localStorage?.getItem('idToken');
      const tenant = getTenantName();
      window.location.href = `${env.integrationURL
        }google-ads?id=${idToken}&tenant=${tenant}&returnUrl=${encodeURIComponent(
          window.location.href
        )}`;
    } else {
      this._notificationService.alert(
        'No Access',
        'You dont have access for this action.'
      );
    }
    return;
  }

  loadAccounts(): void {
    const payload = {
      LeadSource: this.leadSource,
      PageNumber: this.accountsPageNumber,
      PageSize: this.accountsPageSize,
    };
    this.store.dispatch(new FetchGoogleCampaignAccounts(payload));
  }

  deleteAccount(accountId: string, accountName: string): void {
    this.store.dispatch(new DeleteGoogleCampaignAccount(accountId));
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.filteredAccounts = [...this.allAccounts];
  }

  toggleExpand(account: any, expandType: string): void {
    const currentState = account[expandType];
    this.filteredAccounts.forEach((acc) => {
      acc.isAdsExpanded = false;
      acc.isCampaignsExpanded = false;
    });

    if (expandType === 'isAdsExpanded') {
      account.isAdsExpanded = !currentState;
      if (account.isAdsExpanded && !account.adsLoaded) {
        account.isAdsLoading = true;
        this.loadAdsData(account);
      }
    } else if (expandType === 'isCampaignsExpanded') {
      account.isCampaignsExpanded = !currentState;
      if (account.isCampaignsExpanded && !account.campaignsLoaded) {
        account.isCampaignsLoading = true;
        this.loadGoogleAccountCampaigns(account);
      }
    }
  }

  loadAdsData(account: any): void {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const selectedDateRange =
      this.cplTrackForm.value.cplTrackRange !== 'Custom'
        ? DateRange[
          this.cplTrackForm.value.cplTrackRange as keyof typeof DateRange
        ] !== undefined
          ? DateRange[
          this.cplTrackForm.value.cplTrackRange as keyof typeof DateRange
          ]
          : DateRange.TillDate
        : null;

    const dateRangeValues =
      selectedDateRange !== null
        ? getDateRange(selectedDateRange as DateRange, this.currentDate)
        : null;
    const searchText =
      this.searchTerm && this.searchTerm.trim()
        ? this.searchTerm.trim()
        : undefined;
    const payloadData: any = {
      path: 'integration/google-ads/account-ads',
      accountId: account.id,
      PageNumber: account.adsPageNumber,
      PageSize: account.adsPageSize,
      TimeZoneId: this.userBasicDetails?.timeZoneInfo?.timeZoneId,
      BaseUTcOffset: timeZoneOffset,
      FromDate:
        this.cplTrackForm.value.cplTrackDate &&
          this.cplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(
            this.cplTrackForm.value.cplTrackDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
      ToDate:
        this.cplTrackForm.value.cplTrackDate &&
          this.cplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(
            this.cplTrackForm.value.cplTrackDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            dateRangeValues?.[1] || this.currentDate,
            timeZoneOffset
          ),
    };

    if (searchText) {
      payloadData.SearchText = searchText;
    }

    // Filter out null/undefined values
    const payload = Object.fromEntries(
      Object.entries(payloadData).filter(
        ([_, value]) => value !== null && value !== undefined
      )
    );

    this.commonService
      .getModuleListByAdvFilter(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          account.isAdsLoading = false;
          account.adsLoaded = true;
          if (response && response.items && response.items.length > 0) {
            account.ads = response.items;
            account.paginatedAds = response.items;
            account.adsTotalCount =
              response.totalCount || response.items.length;
          } else {
            account.ads = [];
            account.paginatedAds = [];
            account.adsTotalCount = 0;
          }
          this.cdr.detectChanges();
        },
        error: (error) => {
          account.isAdsLoading = false;
          account.adsLoaded = true;
          account.ads = [];
          account.paginatedAds = [];
          account.adsTotalCount = 0;
          this.cdr.detectChanges();
        },
      });
  }

  loadGoogleAccountCampaigns(account: any): void {
    this.selectedAccountForCampaigns = account;
    this.isCampaignsLoading = true;
    account.isCampaignsLoading = true;
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const selectedDateRange =
      this.campaignsCplTrackForm.value.cplTrackRange !== 'Custom'
        ? DateRange[
          this.campaignsCplTrackForm.value
            .cplTrackRange as keyof typeof DateRange
        ] !== undefined
          ? DateRange[
          this.campaignsCplTrackForm.value
            .cplTrackRange as keyof typeof DateRange
          ]
          : DateRange.TillDate
        : null;
    const dateRangeValues =
      selectedDateRange !== null
        ? getDateRange(selectedDateRange as DateRange, this.currentDate)
        : null;
    const searchText =
      this.campaignsSearchTerm && this.campaignsSearchTerm.trim()
        ? this.campaignsSearchTerm.trim()
        : undefined;
    const payloadData: any = {
      path: 'integration/googleads/account-campaigns',
      accountId: account.id,
      PageNumber: account.campaignsPageNumber,
      PageSize: account.campaignsPageSize,
      TimeZoneId:
        this.userBasicDetails?.timeZoneInfo?.timeZoneId ||
        getSystemTimeZoneId(),
      BaseUTcOffset: timeZoneOffset || getSystemTimeOffset(),
      FromDate:
        this.campaignsCplTrackForm.value.cplTrackDate &&
          this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(
            this.campaignsCplTrackForm.value.cplTrackDate[0],
            timeZoneOffset
          )
          : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
      ToDate:
        this.campaignsCplTrackForm.value.cplTrackDate &&
          this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
          ? setTimeZoneDate(
            this.campaignsCplTrackForm.value.cplTrackDate[1],
            timeZoneOffset
          )
          : setTimeZoneDate(
            dateRangeValues?.[1] || this.currentDate,
            timeZoneOffset
          ),
    };
    if (searchText) {
      payloadData.SearchText = searchText;
    }
    // Filter out null/undefined values
    const payload = Object.fromEntries(
      Object.entries(payloadData).filter(
        ([_, value]) => value !== null && value !== undefined
      )
    );
    this.commonService
      .getModuleListByAdvFilter(payload)
      .pipe(takeUntil(this.stopper))
      .subscribe({
        next: (response: any) => {
          if (response && response.items) {
            account.paginatedCampaigns = response.items;
            account.campaignsTotalCount =
              response.totalCount || response.items.length;
            account.hasCampaigns = true;
            account.campaigns = account.paginatedCampaigns;
            this.fetchGoogleCampaignMarketingFinances(
              account,
              account.campaigns || []
            );
          } else {
            account.paginatedCampaigns = [];
            account.campaigns = [];
            account.campaignsTotalCount = 0;
            account.hasCampaigns = false;
            this.fetchGoogleCampaignMarketingFinances(account, []);
          }
          this.isCampaignsLoading = false;
          account.isCampaignsLoading = false;
          account.campaignsLoaded = true;
          this.cdr.detectChanges();
        },
        error: (error: any) => {
          account.paginatedCampaigns = [];
          account.campaigns = [];
          account.campaignsTotalCount = 0;
          account.hasCampaigns = false;
          this.isCampaignsLoading = false;
          account.isCampaignsLoading = false;
          account.campaignsLoaded = true;
          this.cdr.detectChanges();
        },
      });
  }

  fetchGoogleCampaignMarketingFinances(account: any, campaigns: any[]): void {
    const timeZoneOffset = this.userBasicDetails?.timeZoneInfo?.baseUTcOffset;
    const accountId = account?.id;
    if (!accountId) {
      return;
    }
    try {
      this.isCampaignsMarketingLoading = true;
      const campaignIds =
        campaigns && campaigns.length > 0
          ? campaigns
            .map((campaign: any) => campaign?.campaignId || campaign?.id)
            .filter((campaignId) => !!campaignId)
          : [];

      const selectedDateRange =
        this.campaignsCplTrackForm.value.cplTrackRange !== 'Custom'
          ? DateRange[
            this.campaignsCplTrackForm.value
              .cplTrackRange as keyof typeof DateRange
          ] !== undefined
            ? DateRange[
            this.campaignsCplTrackForm.value
              .cplTrackRange as keyof typeof DateRange
            ]
            : DateRange.TillDate
          : null;
      const dateRangeValues =
        selectedDateRange !== null
          ? getDateRange(selectedDateRange as DateRange, this.currentDate)
          : null;
      const marketingPayloadData: any = {
        path: 'dashboard/user/googleads/marketingfinances/campaigns',
        AccountId: accountId,
        CampaignIds: campaignIds,
        BaseUTcOffset: timeZoneOffset || getSystemTimeOffset(),
        TimeZoneId:
          this.userBasicDetails?.timeZoneInfo?.timeZoneId ||
          getSystemTimeZoneId(),
        FromDate:
          this.campaignsCplTrackForm.value.cplTrackDate &&
            this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
            ? setTimeZoneDate(
              this.campaignsCplTrackForm.value.cplTrackDate[0],
              timeZoneOffset
            )
            : setTimeZoneDate(dateRangeValues?.[0], timeZoneOffset),
        ToDate:
          this.campaignsCplTrackForm.value.cplTrackDate &&
            this.campaignsCplTrackForm.value.cplTrackRange === 'Custom'
            ? setTimeZoneDate(
              this.campaignsCplTrackForm.value.cplTrackDate[1],
              timeZoneOffset
            )
            : setTimeZoneDate(
              dateRangeValues?.[1] || this.currentDate,
              timeZoneOffset
            ),
      };
      // Filter out null/undefined values
      const marketingPayload = Object.fromEntries(
        Object.entries(marketingPayloadData).filter(
          ([_, value]) => value !== null && value !== undefined
        )
      );
      this.commonService
        .getModuleListByAdvFilter(marketingPayload)
        .pipe(takeUntil(this.stopper))
        .subscribe({
          next: (response: any) => {
            if (response && response.data && response.data.length > 0) {
              this.campaignsMarketingData = response.data;
              this.updateExpandedAccountWithCampaignsMarketingData();
            } else {
              this.campaignsMarketingData = [];
            }
            this.isCampaignsMarketingLoading = false;
          },
          error: (error) => {
            this.campaignsMarketingData = [];
            this.isCampaignsMarketingLoading = false;
          },
        });
    } catch (error) { }
  }

  updateExpandedAccountWithCampaignsMarketingData(): void {
    if (
      !this.campaignsMarketingData ||
      this.campaignsMarketingData?.length === 0
    ) {
      return;
    }
    const expandedAccount = this.filteredAccounts?.find(
      (account) => account?.isCampaignsExpanded
    );
    if (expandedAccount && expandedAccount?.campaigns) {
      expandedAccount.campaigns = this.mergeCampaignsMarketingDataWithCampaigns(
        expandedAccount?.campaigns,
        this.campaignsMarketingData
      );
      expandedAccount.paginatedCampaigns = [...expandedAccount.campaigns];
      if (this.gridApiCampaigns) {
        this.gridApiCampaigns.refreshCells({
          columns: [
            'Total Cost',
            'Total Click Cost',
            'Cost Per Lead',
            'Cost Per Click',
          ],
          force: true,
        });
      }
      this.cdr.detectChanges();
    }
  }

  mergeCampaignsMarketingDataWithCampaigns(
    campaigns: any[],
    marketingData: any[]
  ): any[] {
    if (!campaigns || !marketingData) {
      return campaigns || [];
    }
    return campaigns.map((campaign) => {
      const marketingInfo = marketingData.find(
        (marketing) =>
          marketing.campaignId?.toString() === campaign.campaignId?.toString()
      );
      if (marketingInfo) {
        return {
          ...campaign,
          ...marketingInfo,
          leadCount: campaign.leadCount,
          googleadsleadscount: marketingInfo.leadCount,
          campaignName: campaign.campaignName || marketingInfo.campaignName,
          campaignId: campaign.campaignId || campaign.id,
        };
      }
      return campaign;
    });
  }

  initDeleteIntegration(accountId: string, accountName: string): void {
    if (!this.canDelete) {
      this._notificationService.alert(
        'No Access',
        'You dont have access for this action.'
      );
      return;
    }
    let initialState: any = {
      message: 'GLOBAL.user-confirmation',
      confirmType: 'delete',
      title: accountName,
      fieldType: 'account',
    };
    this.modalRef = this.modalService.show(
      UserConfirmationComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.deleteAccount(accountId, accountName);
        }
      });
    }
  }

  getAppName(): string {
    return 'LeadRat';
  }

  // openCustomGoogleCampaign(): void {
  //   const googleCampaignDetails = {
  //     image: 'assets/images/integration/google-ads.svg',
  //     displayName: 'Google Campaign',
  //     name: 'GoogleCampaign',
  //   };
  //   localStorage.setItem('integrationData', JSON.stringify(googleCampaignDetails));
  //   this.router.navigate(['/global-config/integration']);
  // }

  onAdsPageChange(event: any, account: any): void {
    account.adsPageNumber = event + 1;
    account.isAdsLoading = true;
    this.loadAdsData(account);
  }

  onCampaignsPageChange(event: any, account: any): void {
    account.campaignsPageNumber = event + 1;
    account.isCampaignsLoading = true;
    this.loadGoogleAccountCampaigns(account);
  }

  assignAdsCount(account: any): void {
    account.adsPageNumber = 1;
    account.isAdsLoading = true;
    this.loadAdsData(account);
  }

  assignCampaignsCount(account: any): void {
    account.campaignsPageNumber = 1;
    account.isCampaignsLoading = true;
    this.loadGoogleAccountCampaigns(account);
  }

  getPages(totalCount: number, pageSize: number): number {
    return Math.ceil(totalCount / pageSize);
  }

  isEmptyInput(): void {
    if (!this.searchTerm.trim()) {
      this.filteredAccounts = [...this.allAccounts];
      const expandedAccount = this.filteredAccounts?.find(
        (account) => account?.isAdsExpanded
      );
      if (expandedAccount) {
        expandedAccount.adsPageNumber = 1;
        expandedAccount.isAdsLoading = true;
        this.loadAdsData(expandedAccount);
      }
    }
  }

  isEmptyCampaignsInput(): void {
    if (!this.campaignsSearchTerm.trim()) {
      if (this.selectedAccountForCampaigns) {
        this.selectedAccountForCampaigns.campaignsPageNumber = 1;
        this.loadGoogleAccountCampaigns(this.selectedAccountForCampaigns);
      }
    }
  }

  onSearch(): void {
    if (this.searchTerm && this.searchTerm.trim()) {
      const expandedAccount = this.filteredAccounts?.find(
        (account) => account?.isAdsExpanded
      );
      if (expandedAccount) {
        expandedAccount.adsPageNumber = 1;
        expandedAccount.isAdsLoading = true;
        this.loadAdsData(expandedAccount);
      }
    }
  }

  onCampaignsSearch(): void {
    if (this.campaignsSearchTerm && this.campaignsSearchTerm.trim()) {
      if (this.selectedAccountForCampaigns) {
        this.selectedAccountForCampaigns.campaignsPageNumber = 1;
        this.loadGoogleAccountCampaigns(this.selectedAccountForCampaigns);
      }
    }
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
  }

  onCampaignsGridReady(params: any): void {
    this.gridApiCampaigns = params.api;
  }

  onCplTrackChange(): void {
    if (!this.cplTrackForm.valid) {
      validateAllFormFields(this.cplTrackForm);
      return;
    }
    if (this.cplTrackForm.value.cplTrackRange !== 'Custom') {
      this.cplTrackForm.patchValue({ cplTrackDate: null });
    }
    const expandedAccount = this.filteredAccounts?.find(
      (account) => account?.isAdsExpanded
    );
    if (expandedAccount) {
      expandedAccount.isAdsLoading = true;
      this.loadAdsData(expandedAccount);
    }
    this.updateAppliedFilter();
    this.showCplFilter = false;
  }

  onCampaignsCplTrackChange(): void {
    if (!this.campaignsCplTrackForm.valid) {
      validateAllFormFields(this.campaignsCplTrackForm);
      return;
    }
    if (this.campaignsCplTrackForm.value.cplTrackRange !== 'Custom') {
      this.campaignsCplTrackForm.patchValue({ cplTrackDate: null });
    }
    if (this.selectedAccountForCampaigns) {
      this.loadGoogleAccountCampaigns(this.selectedAccountForCampaigns);
    }
    this.updateAppliedFilter();
    this.showCampaignsCplFilter = false;
  }

  getStatusClasses(status: string) {
    if (status === 'Enabled') {
      return 'text-accent-green fw-700';
    }
    if (status === 'Paused') {
      return 'text-red';
    }
    return '';
  }

  updateAppliedFilter(): void {
    const adsDateRange = this.cplTrackForm.value.cplTrackRange;
    const campaignsDateRange = this.campaignsCplTrackForm.value.cplTrackRange;
    this.appliedFilter = {
      adsDateRange:
        adsDateRange !== 'Today' && adsDateRange !== 'Custom'
          ? adsDateRange
          : null,
      campaignsDateRange:
        campaignsDateRange !== 'Today' && campaignsDateRange !== 'Custom'
          ? campaignsDateRange
          : null,
      adsCustomDate:
        adsDateRange === 'Custom' ? this.cplTrackForm.value.cplTrackDate : null,
      campaignsCustomDate:
        campaignsDateRange === 'Custom'
          ? this.campaignsCplTrackForm.value.cplTrackDate
          : null,
    };
    const hasAdsFilters = !!(
      this.appliedFilter.adsDateRange ||
      (this.appliedFilter.adsCustomDate &&
        this.appliedFilter.adsCustomDate.length > 0)
    );
    const hasCampaignsFilters = !!(
      this.appliedFilter.campaignsDateRange ||
      (this.appliedFilter.campaignsCustomDate &&
        this.appliedFilter.campaignsCustomDate.length > 0)
    );
    this.showFilters = hasAdsFilters || hasCampaignsFilters;
  }

  onRemoveFilter(key: string, _value: any): void {
    if (key === 'adsDateRange') {
      this.cplTrackForm.patchValue({
        cplTrackRange: 'Today',
        cplTrackDate: null,
      });
      const expandedAccount = this.filteredAccounts?.find(
        (account) => account?.isAdsExpanded
      );
      if (expandedAccount) {
        expandedAccount.isAdsLoading = true;
        this.loadAdsData(expandedAccount);
      }
    } else if (key === 'campaignsDateRange') {
      this.campaignsCplTrackForm.patchValue({
        cplTrackRange: 'Today',
        cplTrackDate: null,
      });
      if (this.selectedAccountForCampaigns) {
        this.loadGoogleAccountCampaigns(this.selectedAccountForCampaigns);
      }
    }
    this.updateAppliedFilter();
  }

  onClearAllFilters(): void {
    this.cplTrackForm.patchValue({
      cplTrackRange: 'Today',
      cplTrackDate: null,
    });
    this.campaignsCplTrackForm.patchValue({
      cplTrackRange: 'Today',
      cplTrackDate: null,
    });
    const expandedAccount = this.filteredAccounts?.find(
      (account) => account?.isAdsExpanded
    );
    if (expandedAccount) {
      expandedAccount.isAdsLoading = true;
      this.loadAdsData(expandedAccount);
    }
    if (this.selectedAccountForCampaigns) {
      this.loadGoogleAccountCampaigns(this.selectedAccountForCampaigns);
    }
    this.updateAppliedFilter();
  }

  getArrayOfFilters(key: string, value: any): any[] {
    if (key === 'adsCustomDate' || key === 'campaignsCustomDate') {
      if (value && value.length === 2) {
        const fromDate = new Date(value[0]).toLocaleDateString();
        const toDate = new Date(value[1]).toLocaleDateString();
        return [`${fromDate} - ${toDate}`];
      }
    }
    return value ? [value] : [];
  }

  hasValue(value: any): boolean {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return value !== null && value !== undefined && value !== '';
  }

  isKeyVisible(key: string): boolean {
    return [
      'adsDateRange',
      'campaignsDateRange',
      'adsCustomDate',
      'campaignsCustomDate',
    ].includes(key);
  }

  refreshGridColumns(): void {
    this.initializeGridOptions();
    if (this.gridApiCampaigns) {
      this.gridApiCampaigns.setColumnDefs(this.gridOptionsCampaigns.columnDefs);
      this.gridApiCampaigns.refreshCells({
        columns: ['Campaign Name', 'Campaign ID', 'Status'],
        force: true,
      });
    }
  }
}
