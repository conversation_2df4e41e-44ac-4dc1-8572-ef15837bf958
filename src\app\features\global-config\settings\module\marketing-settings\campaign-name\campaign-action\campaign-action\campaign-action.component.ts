import { Component, EventEmitter, OnInit } from '@angular/core';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { getEditPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { AddCampaignComponent } from '../../add-campaign/add-campaign.component';
import { UserAlertPopupComponent } from 'src/app/shared/components/user-alert-popup/user-alert-popup.component';
import { DeleteCampaign } from 'src/app/reducers/manage-marketing/marketing.action';

@Component({
  selector: 'campaign-action',
  templateUrl: './campaign-action.component.html',
})
export class CampaignActionComponent implements OnInit {

  private stopper: EventEmitter<void> = new EventEmitter<void>();
  params: any;
  canUpdate: boolean;
  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.store
      .select(getEditPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((canEdit: any) => {
        if (canEdit?.includes('GlobalSettings')) {
          this.canUpdate = true;
        }
      });
  }

  agInit(params: any): void {
    this.params = params;
  }

  editCampaign(campaign: any): void {
    const initialState = {
      selectedCampaign: campaign,
    };
    this.modalRef = this.modalService.show(AddCampaignComponent, {
      class: 'modal-350 top-modal ip-modal-unset',
      initialState,
    });
  }

  deleteCampaign(campaign: any){
    let initialState: any = {
      type: 'manageMarketingDelete',
      data: {
        buttonContent: 'Delete Campaign',
        fieldType: 'Delete',
        heading: `Deleting Campaign Name?`,
        agencyData: campaign,
      },
      class: 'modal-450 modal-dialog-centered ph-modal-unset',
    };
    this.modalRef = this.modalService.show(
      UserAlertPopupComponent,
      Object.assign(
        {},
        {
          class: 'modal-400 top-modal ph-modal-unset',
          initialState,
        }
      )
    );
    if (this.modalRef?.onHide) {
      this.modalRef.onHide.subscribe((reason: string) => {
        if (reason == 'confirmed') {
          this.store.dispatch(new DeleteCampaign([campaign?.id]));
        }
      });
    }
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }

}
