import { Component, EventEmitter, OnInit, ViewChild, OnD<PERSON>roy } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { CountryCode, isPossiblePhoneNumber } from 'libphonenumber-js';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { BehaviorSubject, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, takeUntil } from 'rxjs/operators';
import { EMPTY_GUID } from 'src/app/app.constants';
import { AppState } from 'src/app/app.reducer';
import { getLocationDetailsByObj } from 'src/app/core/utils/common.util';
import { ValidationUtil } from 'src/app/core/utils/validation.util';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { AddAgencyName, ExistAgency, UpdateAgencyName } from 'src/app/reducers/manage-marketing/marketing.action';
import { getAgencyExist, getIsAgencyAddLoading, getIsAgencyUpdateLoading } from 'src/app/reducers/manage-marketing/marketing.reducer';
import { FetchLocationsWithGoogle } from 'src/app/reducers/site/site.actions';
import { getLocationsWithGoogleApi } from 'src/app/reducers/site/site.reducer';

@Component({
  selector: 'add-agency',
  templateUrl: './add-agency.component.html',
})
export class AddAgencyComponent implements OnInit, OnDestroy {
  @ViewChild('phoneNumber') phoneNumber: any;
  searchPlaceTerm$: BehaviorSubject<string> = new BehaviorSubject<string>('');
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  private unsubscribe$ = new Subject<void>();

  preferredCountries = ['in'];
  hasInternationalSupport = false;
  addAgencyForm: FormGroup;
  selectedAgent: any;
  isShowManualCustomerLocation = false;
  placesList: any[] = [];
  isLoadingPlaces = false;
  doesExistAgency: boolean;
  isAddEditCP: boolean = false;

  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private fb: FormBuilder
  ) { }

  ngOnInit(): void {
    this.initForm();
    this.fetchingGS();

    if (this.selectedAgent) {
      this.patchAgencyForm(this.selectedAgent);
    }

    this.addAgencyForm
      .valueChanges.subscribe((value) => {
      });

    this.searchPlaceTerm$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        filter((searchTerm: string) => searchTerm && searchTerm.trim() !== ''),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((searchTerm: string) => {
        if (searchTerm) {
          this.isLoadingPlaces = true;
          this.store.dispatch(new FetchLocationsWithGoogle(searchTerm));
        }
      });

    this.store
      .select(getLocationsWithGoogleApi)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((data: any) => {
        this.placesList = data
          ?.slice()
          .sort((a: any, b: any) => a.location.localeCompare(b.location));
        this.isLoadingPlaces = false;
      });
  }

  private initForm(): void {
    this.addAgencyForm = this.fb.group({
      agencyName: [null, Validators.required],
      phoneNumber: [null, this.contactNumberValidator()],
      email: [null, ValidationUtil.emailValidatorMinLength],
      location: [null],
      customerLocality: null,
      customerCity: null,
      customerState: null,
    });
  }

  private fetchingGS(): void {
    this.store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((data: any) => {
        this.hasInternationalSupport = data?.hasInternationalSupport;
        this.preferredCountries = this.hasInternationalSupport
          ? data.countries.length
            ? [data.countries[0].code.toLowerCase()]
            : ['in']
          : ['in'];
      });
  }

  contactNumberValidator(): ValidatorFn {
    let defaultCountry: CountryCode = 'IN';
    return (control: AbstractControl): ValidationErrors | null => {
      const input = document.querySelector(
        '.phoneNumber > div > input'
      ) as HTMLInputElement;
      if (!input?.value?.length) {
        return null;
      }
      defaultCountry = this.getSelectedCountryCodeContactNo()?.dialCode;
      try {
        const validNumber = isPossiblePhoneNumber(
          this.phoneNumber?.value || control?.value,
          defaultCountry
        );
        if (!validNumber) {
          return { validatePhoneNumber: true };
        }
        return null;
      } catch (error) {
        return { validatePhoneNumber: true };
      }
    };
  }

  getSelectedCountryCodeContactNo(): any {
    return this.phoneNumber?.selectedCountry;
  }

  onSubmit(): void {
    Object.keys(this.addAgencyForm.controls).forEach((field) => {
      const control = this.addAgencyForm.get(field);
      if (control && control.invalid) {
        // console.log(`Invalid field: ${field}`);
      }
    });
    if (this.addAgencyForm.valid) {
      this.isAddEditCP = true;
      const placeId = this.addAgencyForm.value.location?.placeId;
      const locationId = this.addAgencyForm.value.location?.locationId;
      let payload;
      if (this.isShowManualCustomerLocation) {
        payload = {
          id: this.selectedAgent?.id,
          name: this.addAgencyForm.value.agencyName,
          contactNo: this.addAgencyForm.value.phoneNumber,
          email: this.addAgencyForm.value.email,
          address: {
            placeId: placeId && placeId !== "" ? placeId : null,
            locationId: (!placeId || placeId === "") && locationId ? locationId : null,
            subLocality: this.addAgencyForm.value.customerLocality,
            city: this.addAgencyForm.value.customerCity,
            state: this.addAgencyForm.value.customerState,
          },
        };
      } else {
        payload = {
          id: this.selectedAgent?.id,
          name: this.addAgencyForm.value.agencyName,
          contactNo: this.addAgencyForm.value.phoneNumber,
          email: this.addAgencyForm.value.email,
          address: {
            placeId: placeId && placeId !== "" ? placeId : null,
            locationId: (!placeId || placeId === "") && locationId ? locationId : null,
            subLocality: this.addAgencyForm.value.customerLocality,
            city: this.addAgencyForm.value.customerCity,
            state: this.addAgencyForm.value.customerState,
          },
        };
      }
      if (this.selectedAgent) {
        this.store.dispatch(new UpdateAgencyName(payload));
        this.store.select(getIsAgencyUpdateLoading).subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isAddEditCP = false;
            this.modalRef.hide();
          }
        });
      } else {
        this.store.dispatch(new AddAgencyName(payload));
        this.store.select(getIsAgencyAddLoading).subscribe((isLoading: boolean) => {
          if (!isLoading) {
            this.isAddEditCP = false;
            this.modalRef.hide();
          }
        });
      }
    } else {
      this.addAgencyForm.markAllAsTouched();
    }
  }

  patchAgencyForm(data: any): void {
    const isLocationAvailable = data?.address?.subLocality || data?.address?.city || data?.address?.state;
    this.addAgencyForm.patchValue({
      agencyName: data?.name || null,
      phoneNumber: data?.contactNo || null,
      email: data?.email || null,
      location: isLocationAvailable
        ? { id: EMPTY_GUID, location: getLocationDetailsByObj(data?.address), placeId: data?.address?.placeId }
        : null,
      customerLocality: data?.address?.locality ? data?.address?.locality : data?.address?.subLocality || null,
      customerCity: data?.address?.city || null,
      customerState: data?.address?.state || null,
    });
  }

  doesAgencyExist(agencyName: string) {
    if (agencyName) {
      this.store.dispatch(new ExistAgency(agencyName));
      this.store.select(getAgencyExist).subscribe((doesExistAgency: boolean) => {
        this.doesExistAgency = doesExistAgency;
        this.addAgencyForm.get('agencyName').setErrors(
          doesExistAgency ? { alreadyExist: true } : null
        );
      });
    }
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Backspace' || event.keyCode === 8) {
      this.searchPlaceTerm$.next('');
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
