<ng-container *ngIf="logs?.length; else noLogs">
    <div *ngFor="let log of logs.slice(0, 2); let last = last" class="flex-start gap-1">
        <span class="ic-location-mark ic-light-gray icon ic-xs"></span>
        <div [title]="log.clockInLocation" class="fw-400 text-sm text-truncate-1 text-nowrap break-all text-black-100">
            [{{ log.clockInTime | date: 'shortTime' }}] {{ log.clockInLocation }}
        </div>
        <span *ngIf="logs.length > 2 && last"
            class="fw-500 bg-dark text-white text-nowrap p-8 br-6 text-decoration-underline text-xs text-blue cursor-pointer"
            (click)="openPopup(locationModal)">+{{ logs.length - 2 }} more
        </span>
    </div>
</ng-container>
<ng-template #locationModal>
    <div class="w-100 br-10">
        <div class="flex-between bg-coal px-24 py-12 text-white brtr-10 brtl-10">
            <h4>Punch-in Locations</h4>
            <div class="icon ic-close ic-sm cursor-pointer" (click)="modalService.hide()"></div>
        </div>
        <div class="scrollbar flex-start flex-wrap fw-600  text-dark-gray max-h-300 p-10">
            <div *ngFor="let log of logs"  class="fw-400  py-8  ml-10 text-sm text-nowrap break-all text-black-100">
                <span class="ic-location-mark ic-light-gray icon ic-xs"></span>
                [{{ log.clockInTime | date: 'shortTime' }}] {{ log.clockInLocation }}
            </div>
        </div>
    </div>
</ng-template>
<ng-template #noLogs>
    <span>-</span>
</ng-template>