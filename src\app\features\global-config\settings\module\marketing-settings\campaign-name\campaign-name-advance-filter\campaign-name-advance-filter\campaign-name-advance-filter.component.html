<div class="d-flex flex-wrap w-100 pl-30 bg-white brbl-15 brbr-15">
    <div class="w-100 flex-between align-center">
        <h5 class="field-label text-dark-gray">Filter by</h5>
        <div class="filters-grid d-flex mt-20">
            <div class="dropdown-date-picker d-flex rounded">
                <div class="bg-white rounded-start manage-select datefilter-scroll">
                    <ng-select [virtualScroll]="true" placeholder=All [searchable]="false" class="lead-date w-100px"
                        ResizableDropdown [(ngModel)]="appliedFilter.DateType">
                        <ng-option name="dateType" ngDefaultControl *ngFor="let dType of dateTypeList"
                            [value]="dType">{{dType}}</ng-option>
                    </ng-select>
                </div>
                <div class="date-picker align-center py-4 rounded-end" id="leadsAppointmentDate"
                    data-automate-id="leadsAppointmentDate">
                    <span class="ic-appointment icon ic-xxs ic-black" [owlDateTimeTrigger]="dt1"></span>
                    <input type="text" readonly placeholder="ex. 5-03-2025 - 14-03-2025" class="pl-20 text-large w-100"
                        [max]="appliedFilter.DateType === 'Modified Date' || appliedFilter.DateType === 'Created Date' ? 'maxDate' : ''"
                        [owlDateTimeTrigger]="dt1" [owlDateTime]="dt1" [selectMode]="'range'"
                        (ngModelChange)="appliedFilter.date = $event" [ngModel]="appliedFilter.date"
                        [disabled]="!appliedFilter.DateType" />
                    <owl-date-time [pickerType]="'calendar'" #dt1
                        (afterPickerOpen)="onPickerOpened(currentDate)"></owl-date-time>
                </div>
            </div>
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label">Associated Leads</div>
        <div class="mr-20">
            <ng-select [(ngModel)]="appliedFilter.AssociatedLeadsCount" [virtualScroll]="true" id="AssociatedLeadsCount"
                dropdownPosition="bottom" placeholder="ex. 0 - 50"
                data-automate-id="AssociatedLeadsCount">
                <ng-option *ngFor="let range of propertyList" [value]="range" [appTooltip]="range">
                    {{ range }}
                </ng-option>
            </ng-select>
        </div>
    </div>
    <div class="w-50 ph-w-100">
        <div class="field-label">Associated Data</div>
        <div class="mr-20">
            <ng-select [(ngModel)]="appliedFilter.AssociatedPospectsCount" [virtualScroll]="true"
                id="AssociatedPospectsCount" dropdownPosition="bottom" placeholder="ex. 0 - 50"
                data-automate-id="AssociatedPospectsCount">
                <ng-option *ngFor="let range of propertyList" [value]="range" [appTooltip]="range">
                    {{ range }}
                </ng-option>
            </ng-select>
        </div>
    </div>
    <!-- <div class="flex-column w-50 ph-w-100">
        <div class="field-label">Associated Property</div>
        <div class="mr-20">
            <ng-select [(ngModel)]="appliedFilter.AssociateProperty" [virtualScroll]="true" id="AssociatedProperty"
                dropdownPosition="bottom" placeholder="ex. 0 - 50" class="bg-white mr-10"
                data-automate-id="AssociatedProperty">
                <ng-option *ngFor="let range of propertyList" [value]="range" [appTooltip]="range">
                    {{ range }}
                </ng-option>
            </ng-select>
        </div>
    </div> -->
    <div class="w-100 modal-footer bg-white justify-end position-sticky bottom-0 z-index-1021 mt-20">
        <u class="mr-20 fw-semi-bold text-mud cursor-pointer" (click)="modalRef.hide()">Cancel</u>
        <div class="mr-10 btn-gray" (click)="onClearAllFilters('reset')">Reset</div>
        <button class="btn-coal" (click)="applyAdvancedFilter()">Apply filter</button>
    </div>
</div>

<ng-template #fieldLoader>
    <ng-select [virtualScroll]="true" class="pe-none blinking"></ng-select>
</ng-template>