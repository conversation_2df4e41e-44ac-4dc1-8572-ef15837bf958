import { Action } from '@ngrx/store';

export enum LoginActions {
  LOGIN = '[LOGIN] Login',
  LOGIN_SUCCESS = '[LOGIN] Login Success',
  STORE_USERNAME = '[LOGIN] Store username',
  OTP_GENERATION = '[LOGIN] OTP generation',
  OTP_GENERATION_SUCCESS = '[LOGIN] OTP generation Success',
  OTP_VERIFY = '[LOGIN] OTP Verify',
  OTP_VERIFY_SUCCESS = '[LOGIN] OTP Verify Success',
  CLEAR_DETAILS = '[LOGIN] Clear Details',
  TRY_ANOTHER_WAY = '[LOGIN] TRY ANOTHER WAY',
  TRY_ANOTHER_WAY_SUCCESS = '[LOGIN] TRY ANOTHER WAY SUCCESS',
  LOGOUT = '[LOGIN] LOGOUT',
  LOGOUT_SUCCESS = '[LOGIN] LOGOUT SUCCESS',
}

export class Login implements Action {
  readonly type: string = LoginActions.LOGIN;
  constructor(public resource: any) { }
}

export class LoginSuccess implements Action {
  readonly type: string = LoginActions.LOGIN_SUCCESS;
  constructor(public response: any) { }
}

export class StoreUsername implements Action {
  readonly type: string = LoginActions.STORE_USERNAME;
  constructor(public username: string) { }
}

export class TwoFactorOtpGeneration implements Action {
  readonly type: string = LoginActions.OTP_GENERATION;
  constructor(public username: string) { }
}

export class TwoFactorOtpGenerationSuccess implements Action {
  readonly type: string = LoginActions.OTP_GENERATION_SUCCESS;
  constructor(public response: any = {}) { }
}

export class TwoFactorOtpVerify implements Action {
  readonly type: string = LoginActions.OTP_VERIFY;
  constructor(
    public username: string,
    public sessionId: string,
    public otp: string
  ) { }
}

export class clearOtpDetails implements Action {
  readonly type: string = LoginActions.CLEAR_DETAILS;
  constructor() { }
}

export class TryAnotherWay implements Action {
  readonly type: string = LoginActions.TRY_ANOTHER_WAY;
  constructor(public payload: any) { }
}


export class TryAnotherWaySuccess implements Action {
  readonly type: string = LoginActions.TRY_ANOTHER_WAY_SUCCESS;
  constructor(public resp: any) { }
}

export class Logout implements Action {
  readonly type: string = LoginActions.LOGOUT;
  constructor(public payload: any) { }
}

export class LogoutSuccess implements Action {
  readonly type: string = LoginActions.LOGOUT_SUCCESS;
  constructor(public resp: any = '') { }
}

