<ng-container *ngIf="canView">
    <div routerLink='/global-config' [ngClass]="showLeftNav ? 'left-150' : 'left-50px'"
        class="icon ic-circle-chevron-left ic-xxs position-absolute top-18 tb-left-32 z-index-1021 cursor-pointer">
    </div>
    <div class="p-24 position-relative">
        <div class="flex-between mb-20">
            <div class="border br-20 bg-white align-center user mr-10">
                <div class="activation" [ngClass]="{'active' : selectedSection == 'Agency Name'}"
                    (click)="selectedSection = 'Agency Name';selectedTab()">
                    <span class="icon ic-briefcase-solid ic-sm mr-8 ip-mr-0"
                        [ngClass]="{'active' : selectedSection !== 'Agency Name'}"></span>
                    <span class="ip-d-none">Agency Name</span>
                </div>
                <div class="activation" [ngClass]="{'active' : selectedSection == 'Campaign name'}"
                    (click)="selectedSection = 'Campaign name'; selectedTab()">
                    <span class="icon ic-address-card-solid ic-sm mr-8 ip-mr-0"
                        [ngClass]="{'active' : selectedSection !== 'Campaign name'}"></span>
                    <span class="ip-d-none">Campaign name</span>
                </div>
                <div class="activation" [ngClass]="{'active' : selectedSection == 'Channel partner'}"
                    (click)="selectedSection = 'Channel partner';selectedTab()">
                    <span class="icon ic-handshake-solid ic-large mr-8 ip-mr-0"
                        [ngClass]="{'active' : selectedSection !== 'Channel partner'}"></span>
                    <span class="ip-d-none">Channel Partner</span>
                </div>
            </div>
            <div class="flex-center">
                <div class="btn-full-dropdown btn-w-100">
                    <div class="position-absolute top-9 left-9 ip-top-11 align-center z-index-2">
                        <span class="ic-tracker icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Tracker</span>
                    </div>
                    <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                        [(ngModel)]="selectedTrackerOption" (click)="openLeadsTracker()">
                        <ng-option (click)="selectedTrackerOption = null" value="bulkUpload">
                            <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                            {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }} Tracker</ng-option>
                        <ng-option (click)="selectedTrackerOption = null" value="export">
                            <span class="ic-download icon ic-xxs ic-dark mr-8"></span>
                            Export Tracker</ng-option>
                    </ng-select>
                </div>

                <!-- Add New Agency Button with Dropdown -->
                <div *ngIf="selectedSection == 'Agency Name'" class="flex-center">
                    <div class="btn-left-dropdown ml-10" (click)="openAgencyNameModal()">
                        <span class="ic-add icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Add New Agency</span>
                    </div>
                    <!-- Dropdown for Add New Agency -->
                    <div class="btn-right-dropdown btn-w-30 black-100">
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                            (change)="uploadMarketingBulk($event,'Agency Name')">
                            <ng-option value="bulkUpload">
                                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}
                            </ng-option>
                        </ng-select>

                    </div>
                </div>

                <!-- Add New Campaign Button with Dropdown -->
                <div *ngIf="selectedSection == 'Campaign name'" class="flex-center">
                    <div class="btn-left-dropdown ml-10" (click)="openCampaignNameModal()">
                        <span class="ic-add icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Add New Campaign</span>
                    </div>
                    <div class="btn-right-dropdown btn-w-30 black-100">
                        <ng-select (change)="uploadMarketingBulk($event,'Campaign name')" [virtualScroll]="true"
                            [searchable]="false" [clearable]="false">
                            <ng-option (click)="selectedOption = null" value="bulkUpload">
                                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}
                            </ng-option>
                        </ng-select>
                    </div>
                </div>
                <!-- Add New Channel Partner Button with Dropdown -->
                <div *ngIf="selectedSection == 'Channel partner'" class="flex-center">
                    <div class="btn-left-dropdown ml-10" (click)="openChannelPartnerModal()">
                        <span class="ic-add icon ic-xxs"></span>
                        <span class="ml-8 ip-d-none">Add New Channel Partner</span>
                    </div>
                    <div class="btn-right-dropdown btn-w-30 black-100">
                        <ng-select [virtualScroll]="true" [searchable]="false" [clearable]="false"
                            (change)="uploadMarketingBulk($event, 'Channel partner')">
                            <ng-option value="bulkUpload">
                                <span class="ic-upload icon ic-xxs ic-dark mr-8"></span>
                                {{ 'LEADS.bulk' | translate }} {{ 'LEADS.upload' | translate }}
                            </ng-option>
                        </ng-select>
                    </div>
                </div>
            </div>
        </div>
        <div *ngIf="selectedSection == 'Agency Name'">
            <agency-name></agency-name>
        </div>
        <div *ngIf="selectedSection == 'Campaign name'">
            <campaign-name></campaign-name>
        </div>
        <div *ngIf="selectedSection == 'Channel partner'">
            <channel-partners></channel-partners>
        </div>
    </div>
</ng-container>